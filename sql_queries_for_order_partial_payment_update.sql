-- SQL查询以展示OrderPartialPaymentUpdateProcess将处理的数据

-- 1. 查询需要处理的OrderPartialPayment记录
-- 这个查询会找出所有c_payment_id为null、c_state为1且c_order_payment_type为2或3的记录
SELECT id, c_order_id, c_amount, c_order_payment_type, c_payment_id, c_state
FROM t_order_partial_payment
WHERE c_payment_id IS NULL
  AND c_state = '1'
  AND c_order_payment_type IN ('2', '3');

-- 2. 对于每个OrderPartialPayment记录，查询关联的OrderPaymentToOrderLink记录
-- 假设我们有一个示例OrderPartialPayment记录，其c_order_id为'example_order_id'
SELECT id, c_order_id, c_payment_id, c_payment_no
FROM t_order_payment_to_order_link
WHERE c_order_id = 'example_order_id';

-- 3. 查询已完成付款的OrderPayment记录
-- 这个查询会找出所有c_state为1且c_payment_status为'4'(支付成功)的记录
SELECT id, c_payment_status, c_payment_no, c_state
FROM t_order_payment
WHERE c_state = '1'
  AND c_payment_status = '4';

-- 4. 过滤出与特定orderId关联的已完成付款的OrderPayment记录，并按创建时间排序选择最早的一条
-- 假设从OrderPaymentToOrderLink中获取的paymentIds为'payment_id_1', 'payment_id_2'
SELECT op.id, op.c_payment_status, op.c_payment_no, op.c_state, op.c_create_time
FROM t_order_payment op
WHERE op.c_state = '1'
  AND op.c_payment_status = '4'
  AND op.id IN ('payment_id_1', 'payment_id_2')
ORDER BY op.c_create_time ASC
LIMIT 1;

-- 5. 更新OrderPartialPayment记录的c_payment_id
-- 这个UPDATE语句展示了如何更新OrderPartialPayment记录
-- 假设我们找到了一个匹配的OrderPayment，其id为'matched_payment_id'
UPDATE t_order_partial_payment
SET c_payment_id = 'matched_payment_id'
WHERE id = 'example_partial_payment_id';

-- 6. 完整的数据处理流程示例（使用JOIN和子查询）- MySQL 5.6兼容版本
-- 这个查询展示了整个数据处理流程，从找到需要处理的OrderPartialPayment记录
-- 到找到关联的OrderPaymentToOrderLink和最早的OrderPayment记录
-- 使用MySQL 5.6兼容的方式（不使用窗口函数）

-- 首先，创建一个临时表来存储需要处理的OrderPartialPayment记录
CREATE TEMPORARY TABLE IF NOT EXISTS temp_orders_to_process AS
SELECT
    opp.id AS partial_payment_id,
    opp.c_order_id AS order_id,
    opp.c_amount AS amount,
    opp.c_order_payment_type AS payment_type
FROM
    t_order_partial_payment opp
WHERE
    opp.c_payment_id IS NULL
    AND opp.c_state = '1'
    AND opp.c_order_payment_type IN ('2', '3');

-- 然后，为每个订单找到最早的付款记录
SELECT
    otp.partial_payment_id,
    otp.order_id,
    otp.amount,
    otp.payment_type,
    t.payment_id,
    t.payment_status,
    t.payment_no,
    t.create_time
FROM
    temp_orders_to_process otp
JOIN (
    -- 对于每个订单，选择创建时间最早的付款记录
    SELECT
        link.c_order_id,
        op.id AS payment_id,
        op.c_payment_status AS payment_status,
        op.c_payment_no AS payment_no,
        op.c_create_time AS create_time
    FROM
        t_order_payment_to_order_link link
    JOIN
        t_order_payment op ON link.c_payment_id = op.id
    WHERE
        op.c_state = '1'
        AND op.c_payment_status = '4'
    AND (link.c_order_id, op.c_create_time) IN (
        -- 为每个订单找到最早的创建时间
        SELECT
            l.c_order_id,
            MIN(p.c_create_time)
        FROM
            t_order_payment_to_order_link l
        JOIN
            t_order_payment p ON l.c_payment_id = p.id
        WHERE
            p.c_state = '1'
            AND p.c_payment_status = '4'
        GROUP BY
            l.c_order_id
    )
) t ON otp.order_id = t.c_order_id
LIMIT 10; -- 限制结果数量，仅用于示例

-- 清理临时表
DROP TEMPORARY TABLE IF EXISTS temp_orders_to_process;

-- 7. 统计将被更新的记录数量
SELECT COUNT(*) AS total_records_to_update
FROM t_order_partial_payment
WHERE c_payment_id IS NULL
  AND c_state = '1'
  AND c_order_payment_type IN ('2', '3');

-- 8. 按orderPaymentType分组统计
SELECT
    c_order_payment_type AS payment_type,
    COUNT(*) AS record_count
FROM
    t_order_partial_payment
WHERE
    c_payment_id IS NULL
    AND c_state = '1'
    AND c_order_payment_type IN ('2', '3')
GROUP BY
    c_order_payment_type;

-- 9. 为特定订单查找最早的已完成付款记录 - MySQL 5.6兼容版本
-- 这个查询展示了如何为单个订单找到最早的已完成付款记录
-- 假设订单ID为'example_order_id'
SELECT
    op.id,
    op.c_payment_no,
    op.c_payment_status,
    op.c_create_time
FROM
    t_order_payment op
WHERE
    op.id IN (
        SELECT
            c_payment_id
        FROM
            t_order_payment_to_order_link
        WHERE
            c_order_id = 'example_order_id'
    )
    AND op.c_state = '1'
    AND op.c_payment_status = '4'
ORDER BY
    op.c_create_time ASC
LIMIT 1;
