package com.xhgj.srm.api.dto;

import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.entity.ExtraFile;
import com.xhgj.srm.jpa.entity.File;
import com.xhiot.boot.core.common.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021/3/2 15:05
 */
@Data
public class SupplierDetailFile {

    @ApiModelProperty("uid")
    private String uid;
    @ApiModelProperty("文件名")
    private String name;
    @ApiModelProperty("文件路径")
    private String url;
    @ApiModelProperty("文件类型名称")
    private String realname;
    @ApiModelProperty("附件类型")
    private String type;
    @ApiModelProperty("所属组织")
    private String groupName;
    @ApiModelProperty("基础路径")
    private String baseUrl;

    public SupplierDetailFile(File file, String fileType) {
        this.uid = file.getId();
        String fileName = StrUtil.emptyIfNull(file.getName());
        String fileUrl = StrUtil.emptyIfNull(file.getUrl());
        if (!StringUtils.isNullOrEmpty(fileUrl) && !fileUrl.contains("srm/")) {
            fileUrl = "srm" + StrUtil.addPrefixIfNot(fileUrl, "/");
        }
        this.name = !StringUtils.isNullOrEmpty(fileName) ? fileName : "";
        this.url = !StringUtils.isNullOrEmpty(fileUrl) ? fileUrl : "";
        this.realname = !StringUtils.isNullOrEmpty(fileType) ? Constants.FILE_TYPE_TO_NAME.get(fileType) : "";
        this.type = !StringUtils.isNullOrEmpty(file.getRelationType()) ? file.getRelationType() : "";
    }

    public SupplierDetailFile(File file, String fileType,String groupName,String baseUrl){
        this(file,fileType);
        this.groupName = groupName;
        this.baseUrl = baseUrl;
    }

    public SupplierDetailFile(ExtraFile extraFile,String groupName,String baseUrl) {
        this.uid = extraFile.getId();
        String fileName = StrUtil.emptyIfNull(extraFile.getName());
        String fileUrl = StrUtil.emptyIfNull(extraFile.getUrl());
        if (!StringUtils.isNullOrEmpty(fileUrl) && !fileUrl.contains("srm/")) {
            fileUrl = "srm" + fileUrl;
        }
        this.name = !StringUtils.isNullOrEmpty(fileName) ? fileName : "";
        this.url = !StringUtils.isNullOrEmpty(fileUrl) ? fileUrl : "";
        this.realname = extraFile.getRelationName();
        this.groupName = groupName;
        this.baseUrl = baseUrl;
    }
}
