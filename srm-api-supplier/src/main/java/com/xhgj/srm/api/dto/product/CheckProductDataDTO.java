package com.xhgj.srm.api.dto.product;

import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.enums.product.ProductStateEnum;
import com.xhgj.srm.jpa.entity.Product;
import io.swagger.annotations.ApiModelProperty;
import java.util.Optional;
import lombok.Data;


@Data
public class CheckProductDataDTO {

    @ApiModelProperty("商品id")
    String id;
    @ApiModelProperty("编码")
    String tempCode;
    @ApiModelProperty("商品名称")
    String name;
    @ApiModelProperty("品牌")
    String brand;
    @ApiModelProperty("参考供货价")
    String purchasePrice;
    @ApiModelProperty("型号")
    String model;
    @ApiModelProperty("基本单位名称")
    String unitName;
    @ApiModelProperty("审核状态")
    String state;
    @ApiModelProperty("审核状态值")
    String stateValue;
    @ApiModelProperty("当前审核人")
    String checkMan;
    @ApiModelProperty("审核时间")
    String checkTime;
    @ApiModelProperty("审核说明")
    String des;
    //add zhuhd 2021年7月13日09:18:28
    @ApiModelProperty("fourthCateMdmId")
    String fourthCateMdmId;
    /**
     * productMmdId
     */
    @ApiModelProperty("物料mdm id")
    private String productMdmId;


  public CheckProductDataDTO(Product product) {
        this.id = product.getId();
        this.name = product.getName();
        this.tempCode = product.getTempCode();
        this.purchasePrice = Optional.ofNullable(product.getPurchasePrice()).map(String::valueOf).orElse(StrUtil.EMPTY);
        this.model = product.getModel();
        this.state = product.getState();
        Optional.ofNullable(ProductStateEnum.fromKey(product.getState())).ifPresent(e -> this.stateValue = e.getValue());
        this.unitName = product.getUnitName();
        this.fourthCateMdmId = product.getFourthCateMdmId();
        this.productMdmId = product.getProductMdmId();
    }
}
