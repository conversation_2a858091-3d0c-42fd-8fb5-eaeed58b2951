package com.xhgj.srm.api.dto;

import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.entity.File;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @ClassName SupplierLicenseAddParam
 * Create by Liuyq on 2021/6/3 17:32
 **/
@Data
public class FinancialLicenseAddParam {
    @NotBlank(message = "财务信息id不能为空")
    @ApiModelProperty("财务信息id")
    private String financialId;
    @NotBlank(message = "附件url不能为空")
    @ApiModelProperty("附件url")
    private String fileUrl;
    @NotBlank(message = "附件url不能为空")
    @ApiModelProperty("附件url")
    private String fileName;

    public File buildFinancialLicense(){
        File file = new File();
        file.setState(Constants.STATE_OK);
        file.setRelationId(financialId);
        file.setUrl(fileUrl);
        file.setName(fileName);
        file.setRelationType(Constants.FILE_TYPE_KHXK);
        return file;
    }
}
