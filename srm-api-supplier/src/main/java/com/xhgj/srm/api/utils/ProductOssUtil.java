package com.xhgj.srm.api.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.aliyun.oss.OSS;
import com.aliyun.oss.model.ListObjectsRequest;
import com.aliyun.oss.model.OSSObjectSummary;
import com.aliyun.oss.model.ObjectListing;
import com.xhgj.srm.common.config.SrmConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Component
@Slf4j
public class ProductOssUtil {


    private final OSS ossClient;
    private final String bucketNameFtp;

    public ProductOssUtil(OSS ossClient, SrmConfig srmConfig) {
        this.ossClient = ossClient;
        this.bucketNameFtp = srmConfig.getBucketNameFtp();

    }

    // 处理获取到的所有文件，code对应图片名称拼接
    public HashMap<String, String> getAllChildFileNameMap(String parentFoler) {
        List<String> folerFiles = getAllChildFileName(parentFoler);
        if (CollUtil.isEmpty(folerFiles)) {
            return null;
        }
        HashMap<String, String> folerFilesMap = new HashMap<String, String>();
        String tempcode = "";
        String tempfileStr = "";
        for (int i = 0; i < folerFiles.size(); i++) {
            String codeAndFileNamePath = folerFiles.get(i);
            if (i == 0) {
                tempcode = codeAndFileNamePath.substring(0, codeAndFileNamePath.indexOf("/"));
            }
            if (codeAndFileNamePath.indexOf(".") > -1) {
                if (codeAndFileNamePath.indexOf(tempcode) == -1) {
                    log.info("codeAndFileNamePath: 【" + codeAndFileNamePath + "】");
                    if (codeAndFileNamePath.contains("/")) {
                        tempcode = codeAndFileNamePath.substring(0, codeAndFileNamePath.indexOf("/"));
                    } else {
                        tempcode = codeAndFileNamePath;
                    }
                    tempfileStr = "";
                }
                tempfileStr += codeAndFileNamePath.replace(tempcode, "").replace("/", "") + ";";
                folerFilesMap.put(tempcode, tempfileStr);
            } else {
                tempcode = codeAndFileNamePath.substring(0, codeAndFileNamePath.indexOf("/"));
                if (!folerFilesMap.containsKey(tempcode)) {
                    folerFilesMap.put(tempcode, "");
                    tempfileStr = "";
                }
            }
        }
        return folerFilesMap;
    }


    /** 根据父目录路径 获取其下所有子目录和子目录下文件 */
    public List<String> getAllChildFileName(String parentFoler) {
        if (StrUtil.isEmpty(parentFoler)) {
            return null;
        }
        if (parentFoler.lastIndexOf("/") < 0) {
            return null;
        }
        List<String> returnChildFolerName = new ArrayList<String>();
        // 构造ListObjectsRequest请求。
        ListObjectsRequest listObjectsRequest = new ListObjectsRequest(bucketNameFtp);
        // 列出fun目录下的所有文件和文件夹。
        listObjectsRequest.setPrefix(parentFoler);
        String nextMarker = null;
        ObjectListing objectListing;
        listObjectsRequest.withMaxKeys(200);
        do {
            listObjectsRequest.withMarker(nextMarker);
            objectListing = ossClient.listObjects(listObjectsRequest);
            for (OSSObjectSummary objectSummary : objectListing.getObjectSummaries()) {
                // System.out.println(objectSummary.getKey());
                String s = objectSummary.getKey().replace(parentFoler, "");
                if (ObjectUtil.isEmpty(s)) {
                    continue;
                }
                returnChildFolerName.add(s);
            }
            nextMarker = objectListing.getNextMarker();
        } while (objectListing.isTruncated());
        return returnChildFolerName;
    }
}
