package com.xhgj.srm.api.dto;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.enums.TitleOfTheContractEnum;
import com.xhgj.srm.jpa.entity.InputInvoiceOrder;
import com.xhgj.srm.jpa.entity.Order;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;
import lombok.Data;

/**
 * Created by Geng Shy on 2023/8/20
 */
@Data
public class OrderInvoiceRelationDTO {
  /**
   * id
   */
  @ApiModelProperty("id")
  private String id;

  /**
   * 订单id 多个
   */
  @ApiModelProperty("订单编号")
  private String orderNos;

  /**
   * 发票id 多个
   */
  @ApiModelProperty("发票编号")
  private String invoiceNums;

  /**
   * 发票价税合计
   */
  @ApiModelProperty("发票价税合计")
  private BigDecimal invoiceAmount;

  /**
   * 订单价税合计
   */
  @ApiModelProperty("订单价税合计")
  private BigDecimal orderAmount;

  /**
   * 下单平台
   */
  @ApiModelProperty("下单平台")
  private String platform;

  /**
   * 1 审核中 2 暂存 3 通过 4 驳回
   */
  @ApiModelProperty("1 审核中 2 暂存 3 通过 4 驳回")
  private String invoiceState;

  @ApiModelProperty("对账单id ")
  private String accountId;
  @ApiModelProperty("源单类型")
  private String orderSource;
  @ApiModelProperty("采购订单号")
  private String orderCodes;
  @ApiModelProperty("签约抬头（组织编码）")
  private String titleOfTheContract;

  /**
   * 构造方法
   * @param orderInvoiceRelation 订单发票关系
   * @param orders 订单
   */
  public OrderInvoiceRelationDTO(InputInvoiceOrder orderInvoiceRelation, List<Order> orders) {
    this.id = orderInvoiceRelation.getId();
    this.invoiceAmount = orderInvoiceRelation.getInvoiceAmount();
    this.invoiceState = orderInvoiceRelation.getInvoiceState();
    this.orderAmount = orderInvoiceRelation.getOrderAmount();
    this.invoiceNums = orderInvoiceRelation.getInvoiceNums();
    this.orderNos = "";
    if (CollUtil.isNotEmpty(orders)) {
      this.orderNos = StrUtil.join(StrUtil.SLASH, orders.stream().map(Order::getOrderNo).collect(Collectors.toList()));
    }
    this.accountId = orderInvoiceRelation.getAccountId();
    this.orderSource = orderInvoiceRelation.getOrderSource();
    this.orderCodes = orderInvoiceRelation.getOrderCodes();
    TitleOfTheContractEnum titleOfTheContractEnum =
        TitleOfTheContractEnum.getEnumByCode(orderInvoiceRelation.getGroupCode());
    this.titleOfTheContract =
        titleOfTheContractEnum == null ? StrUtil.EMPTY : titleOfTheContractEnum.getName();
  }
}
