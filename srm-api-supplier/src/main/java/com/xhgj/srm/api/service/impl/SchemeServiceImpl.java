package com.xhgj.srm.api.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.xhgj.srm.api.dto.*;
import com.xhgj.srm.api.service.SchemeService;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.dao.SearchSchemeDao;
import com.xhgj.srm.jpa.entity.SearchScheme;
import com.xhgj.srm.jpa.repository.SearchSchemeRepository;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.StringUtils;

import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName SchemeServiceImpl
 * Create by Liuyq on 2021/6/18 16:35
 **/
@Service
public class SchemeServiceImpl implements SchemeService {
    @Autowired
    SearchSchemeRepository repository;

    @Autowired
    SearchSchemeDao searchSchemeDao;

    @Override
    public BootBaseRepository<SearchScheme, String> getRepository() {
        return repository;
    }

    @Override
    public void addScheme(SchemeAddParamDTO schemeAddParamDTO) {
        SearchScheme searchScheme = schemeAddParamDTO.bulidSearchScheme();
        repository.save(searchScheme);
    }

    @Override
    public void updateScheme(SchemeUpdateParamDTO schemeUpdateParamDTO) {
        String id = schemeUpdateParamDTO.getId();
        SearchScheme searchScheme =
                repository
                        .findById(id)
                        .orElseThrow(() -> CheckException.noFindException(SearchScheme.class, id));
        if (searchScheme == null) {
            throw new CheckException("查询方案为空");
        }
        SearchScheme target = schemeUpdateParamDTO.updateSearchScheme(searchScheme);
        repository.save(target);
    }

    @Override
    public void deleteScheme(SingleBaseParam param) {
        String id = param.getId();
        SearchScheme searchScheme =
                repository
                        .findById(id)
                        .orElseThrow(() -> CheckException.noFindException(SearchScheme.class, id));
        if (searchScheme == null) {
            throw new CheckException("查询方案为空");
        }
        searchScheme.setState(Constants.STATE_DELETE);
        repository.save(searchScheme);
    }

    @Override
    public List<SchemeDataDTO> getMySchemeList(String supplierUserId, String type) {
        if (StringUtils.isNullOrEmpty(supplierUserId)) {
            throw new CheckException("登陆供应商用户id不能为空");
        }
        if (StringUtils.isNullOrEmpty(type)) {
            throw new CheckException("类型不能为空");
        }
        List<SchemeDataDTO> schemeDataDTOList = new ArrayList<>();
        List<SearchScheme> schemeList = searchSchemeDao.getSearchSchemeListByUser(supplierUserId, type);
        if (!CollUtil.isEmpty(schemeList)) {
            for (SearchScheme searchScheme : schemeList) {
                SchemeDataDTO schemeDataDTO = new SchemeDataDTO(searchScheme);
                schemeDataDTOList.add(schemeDataDTO);
            }
        }
        return schemeDataDTOList;
    }


    @Override
    public SchemeDataDTO getSchemeDetail(String schemeId) {
        if (StringUtils.isNullOrEmpty(schemeId)) {
            throw new CheckException("接口请求有误");
        }
        SearchScheme searchScheme =
                repository
                        .findById(schemeId)
                        .orElseThrow(() -> CheckException.noFindException(SearchScheme.class, schemeId));
        return new SchemeDataDTO(searchScheme);
    }

    @Transactional
    @Override
    public void setSchemeDefault(SingleBaseParam param) {
        String schemeId = param.getId();
        SearchScheme searchScheme =
                repository
                        .findById(schemeId)
                        .orElseThrow(() -> CheckException.noFindException(SearchScheme.class, schemeId));
        List<SearchScheme> searchSchemeList = searchSchemeDao.getSearchSchemeListByUser(searchScheme.getCreateMan(), searchScheme.getType());
        for (SearchScheme scheme : searchSchemeList) {
            scheme.setIsDefault(Constants.STATE_DELETE);
            repository.save(scheme);
        }
        searchScheme.setIsDefault(Constants.YES);
        repository.save(searchScheme);
    }
}
