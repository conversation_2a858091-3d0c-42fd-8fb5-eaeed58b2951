package com.xhgj.srm.api.dto;

import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.enums.OrderInvoiceEnums;
import com.xhgj.srm.jpa.entity.OrderInvoice;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;

/**
 * OmsSubmitOrderInvoiceDTO
 */
@Data
@NoArgsConstructor
public class OmsSubmitOrderInvoiceDTO {

  /**
   * 客户订单号
   */
  private List<String> customerOrderNoList;

  /**
   * 发票类型:1,普通发票;2,增值税专用发票
   */
  private String invoiceType;

  /**
   * 发票类型中文
   */
  private String invoiceTypeName;
  /**
   * 发票抬头
   */
  private String title;
  /**
   * 税号
   */
  private String invoiceTaxIdentificationNo;
  /**
   * 开户银行
   */
  private String invoiceOpenBank;
  /**
   * 银行账号
   */
  private String bankAccountNumber;
  /**
   * 电话
   */
  private String invoiceOpenPhone;
  /**
   * 地址
   */
  private String invoiceOpenAddress;
  /**
   * 票面信息
   */
  private String content;
  /**
   * 收件人
   */
  private String invoiceAcceptMan;
  /**
   * 收件人地址
   */
  private String invoiceReceiveAddress;
  /**
   * 其它备注
   */
  private String invoiceOtherAsk;
  /**
   * 收件人联系电话
   */
  private String invoiceAcceptPhone;
  /**
   * 开票申请单号
   */
  private String invoiceApplicationNumber;
  /**
   * 申请供应商
   */
  private String applySupplier;
  /**
   * 落地商订单开票类型 - 1:单个订单开票,2:订单合并开票
   */
  private String orderInvoiceType;

  public OmsSubmitOrderInvoiceDTO(OrderInvoice orderInvoice,String applySupplier,String orderInvoiceType) {
    this.invoiceType = orderInvoice.getType();
    this.title = orderInvoice.getTitle();
    this.invoiceTaxIdentificationNo = orderInvoice.getTaxNumber();
    this.invoiceOpenBank = orderInvoice.getBankName();
    this.bankAccountNumber = orderInvoice.getBankAccount();
    this.invoiceOpenPhone = orderInvoice.getMobile();
    this.invoiceOpenAddress = orderInvoice.getAddress();
    this.content = orderInvoice.getContent();
    this.invoiceAcceptMan = orderInvoice.getReceiveMan();
    this.invoiceReceiveAddress = orderInvoice.getReceiveAddress();
    this.invoiceOtherAsk = orderInvoice.getRemark();
    this.invoiceAcceptPhone = orderInvoice.getReceiveMobile();
    this.invoiceApplicationNumber = orderInvoice.getInvoiceApplicationNumber();
    this.applySupplier = applySupplier;
    this.orderInvoiceType = orderInvoiceType;
    OrderInvoiceEnums invoiceEnums = OrderInvoiceEnums.fromKey(orderInvoice.getType());
    this.invoiceTypeName = invoiceEnums != null ? invoiceEnums.getDesc() : StrUtil.EMPTY;
  }
}


