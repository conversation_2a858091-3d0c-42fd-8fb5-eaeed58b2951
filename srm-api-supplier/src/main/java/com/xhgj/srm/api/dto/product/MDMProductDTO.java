package com.xhgj.srm.api.dto.product;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import com.xhgj.srm.jpa.entity.ProductSellableArea;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
  *@ClassName MDMProductDTO
  *<AUTHOR>
  *@Date 2023/10/18 15:40
*/
@NoArgsConstructor
@Data
public class MDMProductDTO {
  @ApiModelProperty("返回信息")
  @JsonProperty("msg")
  private String msg;
  @ApiModelProperty("code码")
  @JsonProperty("code")
  private Integer code;
  @ApiModelProperty("数据")
  @JsonProperty("data")
  private DataDTO data;

  @NoArgsConstructor
  @Data
  public static class DataDTO {
    @ApiModelProperty("页数")
    @JsonProperty("pageNo")
    private Integer pageNo;
    @ApiModelProperty("总条数")
    @JsonProperty("totalPages")
    private Integer totalPages;
    @ApiModelProperty("条数")
    @JsonProperty("pageSize")
    private Integer pageSize;
    @ApiModelProperty("总数")
    @JsonProperty("totalCount")
    private Integer totalCount;
    @ApiModelProperty("数据内容")
    @JsonProperty("content")
    private List<ContentDTO> content;

    @NoArgsConstructor
    @Data
    public static class ContentDTO {
      @ApiModelProperty("品牌名称")
      @JsonProperty("brandName")
      private String brandName;
      @ApiModelProperty("价格")
      @JsonProperty("marketPrice")
      private String marketPrice;
      @ApiModelProperty("物料编码")
      @JsonProperty("code")
      private String code;
      @ApiModelProperty("市场价")
      @JsonProperty("supplyPrice")
      private String supplyPrice;
      @ApiModelProperty("srm产品类型")
      @JsonProperty("srmProductType")
      private String srmProductType;
      @ApiModelProperty("单位名称")
      @JsonProperty("unitName")
      private String unitName;
      @JsonProperty("isFreeShipping")
      private String isFreeShipping;
      @ApiModelProperty("规格")
      @JsonProperty("manuCode")
      private String manuCode;
      @JsonProperty("pushPlatformCodeAndNames")
      @ApiModelProperty("推送平台信息")
      private List<?> pushPlatformCodeAndNames;
      @JsonProperty("relationPlatformCodeAndNames")
      @ApiModelProperty("关联平台信息")
      private List<RelationPlatformCodeAndNamesDTO> relationPlatformCodeAndNames;
      @ApiModelProperty("产品类型名称")
      @JsonProperty("srmProductTypeName")
      private String srmProductTypeName;
      @JsonProperty("isPhaseOut")
      private String isPhaseOut;
      @JsonProperty("isDelisting")
      private String isDelisting;
      @JsonProperty("netWeight")
      private String netWeight;
      @ApiModelProperty("物料名称")
      @JsonProperty("name")
      private String name;
      @JsonProperty("mdmId")
      private String mdmId;
      /**
       * 可售区域
       */
      @ApiModelProperty("可售区域")
      List<ProductSellableArea> list;

      @NoArgsConstructor
      @Data
      public static class RelationPlatformCodeAndNamesDTO {
        @ApiModelProperty("编码")
        @JsonProperty("code")
        private String code;
        @ApiModelProperty("名称")
        @JsonProperty("name")
        private String name;
        @JsonProperty("abbreviation")
        private String abbreviation;
      }
    }
  }
}
