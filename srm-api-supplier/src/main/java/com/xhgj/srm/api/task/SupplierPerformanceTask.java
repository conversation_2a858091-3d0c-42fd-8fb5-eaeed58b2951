package com.xhgj.srm.api.task;

import com.xhgj.srm.api.service.SupplierPerformanceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@Configuration
@EnableScheduling
@Slf4j
public class SupplierPerformanceTask {
  @Autowired private SupplierPerformanceService supplierPerformanceService;

  /** 更新账号状态 */
  @Scheduled(cron = "0 0 * ? * ?")
  private void updateState() {
    log.info("每日0点修改七天未上传合同附件的账号状态，开始执行=============================");
    supplierPerformanceService.updateState();
    log.info("每日0点修改七天未上传合同附件的账号状态，开始执行=============================");
  }
}
