package com.xhgj.srm.api.dto;

import com.xhgj.srm.jpa.entity.Contact;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName ContactPageData
 * Create by Liuyq on 2021/6/7 8:43
 **/
@Data
public class ContactPageData {
    @ApiModelProperty("联系人id")
    private String id;
    @ApiModelProperty("姓名")
    private String name;
    @ApiModelProperty("性别")
    private String sex;
    @ApiModelProperty("职务")
    private String duty;
    @ApiModelProperty("邮箱")
    private String mail;
    @ApiModelProperty("联系方式")
    private String phone;
    @ApiModelProperty("负责区域")
    private String area;

    public ContactPageData(Contact contact){
        this.id = contact.getId();
        this.name = contact.getName();
        this.sex = contact.getSex();
        this.duty = contact.getDuty();
        this.mail = contact.getMail();
        this.phone = contact.getPhone();
        this.area = contact.getArea();
    }
}
