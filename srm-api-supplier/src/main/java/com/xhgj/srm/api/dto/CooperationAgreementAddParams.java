package com.xhgj.srm.api.dto;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/10/12 14:28
 */
@Data
public class CooperationAgreementAddParams {
  @NotBlank(message = "组织下供应商 id 不能为空")
  @ApiModelProperty("组织下供应商 id ")
  private String supplierInGroupId;

  @Valid
  @ApiModelProperty("上传附件")
  @NotNull(message = "协议必传")
  private SupplierLicenseFileAddParam fileAddParamList;

  @NotBlank(message = "协议等级不能为空")
  @ApiModelProperty("协议等级")
  private String level;
}
