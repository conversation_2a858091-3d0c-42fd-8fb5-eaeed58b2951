package com.xhgj.srm.api.dto;

import com.xhgj.srm.jpa.entity.File;
import com.xhiot.boot.core.common.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @ClassName BrandAuthorizationData
 * Create by Liuyq on 2021/6/21 9:27
 **/
@Data
public class BrandAuthorizationData {
    @ApiModelProperty("授权类型")
    private String type;

    @ApiModelProperty("授权附件id")
    private String fileId;

    @ApiModelProperty("授权品牌")
    List<BrandData> brands;

    @ApiModelProperty("授权品牌")
    List<SupplierLicenseFileAddParam> fileList;

    public BrandAuthorizationData(File file) {
        this.fileId = file.getId();
        this.type = !StringUtils.isNullOrEmpty(file.getType()) ? file.getType() : "";
    }
}
