package com.xhgj.srm.api.dto;

import lombok.Data;
import org.apache.poi.ss.formula.functions.T;

import java.util.List;

/**
 * @ClassName BrandPage
 * Create by Liuyq on 2021/6/4 16:08
 **/
@Data
public class BrandPage<T> {

    List<T> content;
    String pageNo;
    String pageSize;
    String totalCount;
    String totalPages;

    public BrandPage(List<T> list, String pageNo, String pageSize, String totalCount, String totalPages) {
        this.content = list;
        this.pageNo = pageNo;
        this.pageSize = pageSize;
        this.totalCount = totalCount;
        this.totalPages = totalPages;
    }
}
