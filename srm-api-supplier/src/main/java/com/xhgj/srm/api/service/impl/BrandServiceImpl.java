package com.xhgj.srm.api.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.PageUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.xhgj.srm.api.dto.ArrayBaseParam;
import com.xhgj.srm.api.dto.BrandAddParam;
import com.xhgj.srm.api.dto.BrandAuthorizationAddParam;
import com.xhgj.srm.api.dto.BrandAuthorizationData;
import com.xhgj.srm.api.dto.BrandAuthorizationPageData;
import com.xhgj.srm.api.dto.BrandAuthorizationUpdateParam;
import com.xhgj.srm.api.dto.BrandData;
import com.xhgj.srm.api.dto.BrandDelParam;
import com.xhgj.srm.api.dto.BrandListAddParam;
import com.xhgj.srm.api.dto.BrandNameParamDTO;
import com.xhgj.srm.api.dto.BrandPage;
import com.xhgj.srm.api.dto.BrandPageData;
import com.xhgj.srm.api.dto.SupplierLicenseFileAddParam;
import com.xhgj.srm.api.service.BrandService;
import com.xhgj.srm.api.service.SupplierInGroupService;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.config.SrmConfig;
import com.xhgj.srm.common.dto.MdmBrandPageData;
import com.xhgj.srm.common.enums.BrandRelationTypeEnum;
import com.xhgj.srm.common.utils.HttpUtil;
import com.xhgj.srm.jpa.dao.BrandDao;
import com.xhgj.srm.jpa.dao.FileDao;
import com.xhgj.srm.jpa.dao.ProductDao;
import com.xhgj.srm.jpa.entity.Brand;
import com.xhgj.srm.jpa.entity.File;
import com.xhgj.srm.jpa.entity.SearchScheme;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierFb;
import com.xhgj.srm.jpa.entity.SupplierInGroup;
import com.xhgj.srm.jpa.repository.BrandRepository;
import com.xhgj.srm.jpa.repository.FileRepository;
import com.xhgj.srm.jpa.repository.SearchSchemeRepository;
import com.xhgj.srm.jpa.repository.SupplierRepository;
import com.xhgj.srm.request.service.third.mpm.MPMService;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.StringUtils;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import com.xhiot.boot.framework.web.util.PageResultBuilder;
import com.xhiot.boot.mvc.base.PageResult;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;

/**
 * @ClassName CheckServiceIImpl Create by Liuyq on 2021/5/31 16:12
 */
@Service
public class BrandServiceImpl implements BrandService {

  @Autowired private BrandRepository repository;
  @Autowired private FileRepository fileRepository;
  @Autowired private SupplierRepository supplierRepository;
  @Autowired private SearchSchemeRepository searchSchemeRepository;
  @Autowired private BrandDao dao;
  @Autowired private FileDao fileDao;
  @Autowired private BrandDao brandDao;
  @Autowired private ProductDao productDao;
  @Autowired private SupplierInGroupService supplierInGroupService;
  @Resource
  MPMService mpmService;

  private final String uploadUrl;

  public BrandServiceImpl(SrmConfig srmConfig) {
    this.uploadUrl = srmConfig.getUploadUrl();
  }

  @Override
  public BootBaseRepository<Brand, String> getRepository() {
    return repository;
  }

  @Override
  public PageResult<BrandPageData> getBrandPage(
      String supplierId,
      String brandName,
      String manageType,
      String isPermission,
      String schemeId,
      int pageNo,
      int pageSize) {
    if (StringUtils.isNullOrEmpty(supplierId)) {
      throw new CheckException("supplierId不能为空!");
    }
    Supplier supplier =
        supplierRepository
            .findById(supplierId)
            .orElseThrow(() -> CheckException.noFindException(Supplier.class, supplierId));
    if (!StringUtils.isNullOrEmpty(schemeId)) {
      SearchScheme search =
          searchSchemeRepository
              .findById(schemeId).orElse(null);
      if (search != null && !StringUtils.isNullOrEmpty(search.getContent())) {
        JSONObject searchJo = JSONObject.parseObject(search.getContent());
        if (searchJo != null) {
          brandName = searchJo.containsKey("brandName") ? searchJo.getString("brandName") : "";
          manageType = searchJo.containsKey("manageType") ? searchJo.getString("manageType") : "";
          isPermission =
              searchJo.containsKey("isPermission") ? searchJo.getString("isPermission") : "";
        }
      }
    }
    List<String> supplierInGroupIdList =
        CollUtil.emptyIfNull(
            supplierInGroupService.getBySupplierId(supplier.getId()).stream()
                .map(SupplierInGroup::getId)
                .collect(Collectors.toList()));
    Page<Object[]> page;
    if (CollUtil.isNotEmpty(supplierInGroupIdList)) {
      page =
          dao.getBrandPageBySupplierInGroupId(
              supplierInGroupIdList, brandName, manageType, isPermission, pageNo, pageSize);
    } else {
      page = Page.empty();
    }
    return PageResultBuilder.buildPageResult(
        page,
        brand -> {
          String brandEnName = (String) brand[1];
          String brandCnName = (String) brand[2];
          BrandPageData brandPageData =
              new BrandPageData(
                  (String) brand[0],
                  brandEnName,
                  brandCnName,
                  (String) brand[3],
                  (String) brand[4],
                  getBrandIsPermission(brandEnName, brandCnName, supplier.getId()),
                  uploadUrl);
          brandPageData.setBrandMdmId(getMdmIdByBrandName(supplierId, brandCnName, brandEnName));
          return brandPageData;
        });
  }

  /**
   * 通过品牌中英文名，和供应商 id 获得授权书
   *
   * @param brandEnName 英文名 必传
   * @param brandCnName 中文名 必传
   * @param supplierId 供应商 id 必传
   */
  private List<File> getBrandIsPermission(
      String brandEnName, String brandCnName, String supplierId) {
    Assert.notEmpty(brandEnName);
    Assert.notEmpty(brandCnName);
    Assert.notEmpty(supplierId);
    Brand brand =
        brandDao.getFirstByPermissionIdAndBrandEnAndCnName(brandEnName, brandCnName, supplierId);
    if (brand == null) {
      return null;
    } else {
      return fileDao.getFileByRelationIdAndType(
          supplierId, Constants.FILE_TYPE_PPSQ, brand.getId());
    }
  }

  @Transactional(rollbackFor = Exception.class)
  @Override
  public void addBrand(BrandAddParam addParam) {
    String supplierId = addParam.getSupplierId();
    Supplier supplier =
        supplierRepository
            .findById(supplierId)
            .orElseThrow(() -> CheckException.noFindException(Supplier.class, supplierId));
    List<BrandListAddParam> brands = addParam.getBrands();
    // 组织下供应商 id
    List<String> supplierInGroupList =
        CollUtil.emptyIfNull(
            supplierInGroupService.getBySupplierId(supplier.getId()).stream()
                .map(SupplierInGroup::getId)
                .collect(Collectors.toList()));
    if (CollUtil.isEmpty(supplierInGroupList)) {
      throw new CheckException("不存在组织下供应商，无法新增品牌");
    }
    List<String> error = new ArrayList<>();
    // 这里添加品牌不允许添加重复的品牌
    CollUtil.emptyIfNull(addParam.getBrands())
        .forEach(
            brandListAddParam -> {
              String brandNameCn = brandListAddParam.getBrandNameCn();
              String brandNameEn = brandListAddParam.getBrandNameEn();
              if (dao.existBySupplierInGroupAndBrandName(
                  supplierInGroupList,
                  BrandRelationTypeEnum.SUPPLIER_IN_GROUP.getKey(),
                  brandNameEn,
                  brandNameCn)) {
                error.add(brandNameEn + "/" + brandNameCn);
              }
            });
    if (CollUtil.isNotEmpty(error)) {
      throw new CheckException("品牌存在无法新增：" + CollUtil.join(error, ","));
    }
    supplierInGroupList.forEach(
        supplierInGroupId -> {
          if (!CollUtil.isEmpty(brands)) {
            for (BrandListAddParam brand : brands) {
              if (!Constants.BRAND_MANAGE_TYPE_TO_NAME.containsKey(brand.getManageType())) {
                throw new CheckException("经营形式参数有误!");
              }
              Brand brandByRelationId =
                  dao.getBrandByRelationId(
                      supplierInGroupId,
                      BrandRelationTypeEnum.SUPPLIER_IN_GROUP.getKey(),
                      brand.getBrandNameEn(),
                      brand.getBrandNameCn());
              if (brandByRelationId == null) {
                Brand brand1 =
                    bulidBrand(
                        brand, supplierInGroupId, BrandRelationTypeEnum.SUPPLIER_IN_GROUP.getKey());
                repository.saveAndFlush(brand1);
              }
            }
          }
        });
  }

  private Brand bulidBrand(
      BrandListAddParam brandListAddParam, String relationId, String relationType) {
    Brand brand = new Brand();
    brand.setRelationId(relationId);
    brand.setRelationType(relationType);
    brand.setLogoUrl(brandListAddParam.getLogoUrl());
    brand.setBrandMdmId(brandListAddParam.getBrandMdmId());
    brand.setBrandnameCn(brandListAddParam.getBrandNameCn());
    brand.setBrandnameEn(brandListAddParam.getBrandNameEn());
    brand.setManageType(brandListAddParam.getManageType());
    brand.setIsPermission(Constants.UPLOAD_STATUS_NOT_UPLOADED);
    brand.setState(Constants.STATE_OK);
    return brand;
  }

  @Transactional(rollbackFor = Exception.class)
  @Override
  public void addBrandAuthorization(BrandAuthorizationAddParam addParam) {
    if (!Constants.BRAND_AUTHORIZATION_TYPE_TO_NAME.containsKey(addParam.getType())) {
      throw new CheckException("授权类型参数有误!");
    }
    String supplierId = addParam.getSupplierId();
    Supplier supplier =
        supplierRepository
            .findById(supplierId)
            .orElseThrow(() -> CheckException.noFindException(Supplier.class, supplierId));
    String brands =
        addParam.getBrandNameParamDTOS().stream()
            .map(
                brandNameParamDTO -> {
                  String brandNameCn = brandNameParamDTO.getBrandNameCn();
                  String brandNameEn = brandNameParamDTO.getBrandNameEn();
                  Brand brand =
                      brandDao.getFirstByPermissionIdAndBrandEnAndCnName(
                          brandNameEn, brandNameCn, supplierId);
                  if (brand == null) {
                    Brand brandNew = new Brand();
                    brandNew.setLogoUrl(brandNameParamDTO.getLogoPic());
                    brandNew.setBrandnameCn(brandNameCn);
                    brandNew.setBrandnameEn(brandNameEn);
                    brandNew.setState(Constants.STATE_OK);
                    brandNew.setPermissionId(supplier.getId());
                    brandNew.setBrandMdmId(brandNameParamDTO.getBrandMdmId());
                    brandNew.setCreateTime(System.currentTimeMillis());
                    return save(brandNew).getId();
                  } else {
                    return brand.getId();
                  }
                })
            .filter(StrUtil::isNotBlank)
            .collect(Collectors.joining(","));
    String type = addParam.getType();
    String[] split = brands.split(",");
    if (split.length > 0) {
      for (String brandId : split) {
        Brand brand = null;
        if (!StringUtils.isNullOrEmpty(brandId)) {
          brand =
              repository
                  .findById(brandId)
                  .orElseThrow(() -> CheckException.noFindException(Brand.class, brandId));
          if (fileDao.existByBrandIdAndTypeAndRelationTypeAndRelationId(
              brand.getId(), type, Constants.FILE_TYPE_PPSQ, supplier.getId())) {
            throw new CheckException(
                "该品牌【"
                    + brand.getBrandnameEn()
                    + "/"
                    + brand.getBrandnameCn()
                    + "】已经授权了该类型，无需重复授权");
          }
        }
      }
    }
    List<SupplierLicenseFileAddParam> fileAddParamList = addParam.getFileAddParamList();
    String fileName = "";
    if (CollUtil.isNotEmpty(fileAddParamList)) {
      for (SupplierLicenseFileAddParam fileAddParam : fileAddParamList) {
        fileName += fileAddParam.getFileName() + ";" + fileAddParam.getLicenceUrl() + ",";
      }
      // 此处品牌授权多个附件上传时保存至description字段
      File file = new File();
      file.setRelationId(supplier.getId());
      file.setBrandIds(brands);
      file.setDescription(
          !StringUtils.isNullOrEmpty(fileName) ? fileName.substring(0, fileName.length() - 1) : "");
      file.setRelationType(Constants.FILE_TYPE_PPSQ);
      file.setType(type);
      file.setState(Constants.STATE_OK);
      file.setCreateTime(System.currentTimeMillis());
      fileRepository.save(file);
    }
  }

  @Override
  public BrandPage<BrandAuthorizationPageData> getBrandAuthorizationPage(
      String supplierId,
      String fileName,
      String brandName,
      String supplierName,
      String type,
      String schemeId,
      String pageNo,
      String pageSize) {
    if (StringUtils.isNullOrEmpty(supplierId)) {
      throw new CheckException("supplierId不能为空!");
    }
    if (!StringUtils.isNullOrEmpty(schemeId)) {
      SearchScheme search =
          searchSchemeRepository
              .findById(schemeId)
              .orElseThrow(() -> CheckException.noFindException(SearchScheme.class, schemeId));
      if (search != null && !StringUtils.isNullOrEmpty(search.getContent())) {
        JSONObject searchJo = JSONObject.parseObject(search.getContent());
        if (searchJo != null) {
          fileName = searchJo.containsKey("fileName") ? searchJo.getString("fileName") : "";
          brandName = searchJo.containsKey("brandName") ? searchJo.getString("brandName") : "";
          supplierName =
              searchJo.containsKey("supplierName") ? searchJo.getString("supplierName") : "";
          type = searchJo.containsKey("type") ? searchJo.getString("type") : "";
        }
      }
    }
    Page<String> page =
        fileDao.getFileListByRelationIdAndType(
            supplierId, fileName, brandName, supplierName, type, pageNo, pageSize);
    int totalPages = page.getTotalPages();
    long totalElements = page.getTotalElements();
    List<BrandAuthorizationPageData> pageDataList = new ArrayList<>();
    if (!(Integer.parseInt(pageNo) > totalPages)) {
      List<String> fileIdList = page.getContent();
      PageUtil.setOneAsFirstPageNo();
      // 计算当前页的起始编号
      for (String id : fileIdList) {
        File file =
            fileRepository
                .findById(id)
                .orElseThrow(() -> CheckException.noFindException(File.class, id));
        String relationId = file.getRelationId();
        Supplier supplier =
            supplierRepository
                .findById(relationId)
                .orElseThrow(() -> CheckException.noFindException(Supplier.class, relationId));
        String description = file.getDescription();
        String allFileName = "";
        if (!StringUtils.isNullOrEmpty(description)) {
          String[] split = description.split(",");
          if (split.length > 0) {
            for (String split1 : split) {
              if (!StringUtils.isNullOrEmpty(split1)) {
                String[] split2 = split1.split(";");
                if (split2.length > 0) {
                  allFileName += split2[0] + ";";
                }
              }
            }
          }
          allFileName =
              !StringUtils.isNullOrEmpty(allFileName)
                  ? allFileName.substring(0, allFileName.length() - 1)
                  : "";
        }
        BrandAuthorizationPageData data =
            new BrandAuthorizationPageData(file, supplier, uploadUrl, allFileName);
        String[] split = file.getBrandIds().split(",");
        StringBuilder brandNames = new StringBuilder();
        List<String> brandIds = new ArrayList<>();
        if (split.length > 0) {
          for (String brandId : split) {
            Brand brand = null;
            if (!StringUtils.isNullOrEmpty(brandId)) {
              brand =
                  repository
                      .findById(brandId)
                      .orElseThrow(() -> CheckException.noFindException(Brand.class, brandId));
            }
            if (brand != null) {
              brandNames
                  .append("(")
                  .append(brand.getBrandnameCn())
                  .append("/")
                  .append(brand.getBrandnameEn())
                  .append(")|");
              brandIds.add(brand.getId());
            }
          }
        }
        data.setBrandName(
            brandNames.length() > 0 ? brandNames.substring(0, brandNames.length() - 1) : "");
        data.setBrandIds(CollUtil.join(brandIds, ","));
        pageDataList.add(data);
      }
    }
    return new BrandPage<>(
        pageDataList,
        String.valueOf(pageNo),
        String.valueOf(pageSize),
        String.valueOf(totalElements),
        String.valueOf(totalPages));
  }

  @Override
  public void updateBrandAuthorization(BrandAuthorizationUpdateParam updateParam) {
    if (!Constants.BRAND_AUTHORIZATION_TYPE_TO_NAME.containsKey(updateParam.getType())) {
      throw new CheckException("授权类型参数有误!");
    }
    String fileId = updateParam.getFileId();
    File file =
        fileRepository
            .findById(fileId)
            .orElseThrow(() -> CheckException.noFindException(File.class, fileId));
    List<SupplierLicenseFileAddParam> fileAddParamList = updateParam.getFileAddParamList();
    String fileName = "";
    if (CollUtil.isNotEmpty(fileAddParamList)) {
      for (SupplierLicenseFileAddParam fileAddParam : fileAddParamList) {
        fileName += fileAddParam.getFileName() + ";" + fileAddParam.getLicenceUrl() + ",";
      }
      fileName =
          !StringUtils.isNullOrEmpty(fileName) ? fileName.substring(0, fileName.length() - 1) : "";
    }
    updateParam.updateBrandAuthorization(file, fileName);
    fileRepository.saveAndFlush(file);
  }

  private String getMdmIdByBrandName(String supplierId, String brandNameCn, String brandNameEn) {
    Assert.notEmpty(supplierId);
    Assert.notEmpty(brandNameCn);
    Assert.notEmpty(brandNameEn);
    PageResult<MdmBrandPageData> mdmBrandPage = getMdmBrandPage(supplierId, brandNameCn, 1, 20);
    for (MdmBrandPageData mdmBrandPageData : CollUtil.emptyIfNull(mdmBrandPage.getContent())) {
      if (mdmBrandPageData.getBrandNameCn().equals(brandNameCn)
          && mdmBrandPageData.getBrandNameEn().equals(brandNameEn)) {
        return mdmBrandPageData.getBrandMdmId();
      }
    }
    if (mdmBrandPage.getTotalPages() > 1) {
      for (int i = 2; i < mdmBrandPage.getTotalPages() + 1; i++) {
        PageResult<MdmBrandPageData> mdmBrandPages =
            getMdmBrandPage(supplierId, brandNameCn, i, 20);
        for (MdmBrandPageData mdmBrandPageData : CollUtil.emptyIfNull(mdmBrandPages.getContent())) {
          if (mdmBrandPageData.getBrandNameCn().equals(brandNameCn)
              && mdmBrandPageData.getBrandNameEn().equals(brandNameEn)) {
            return mdmBrandPageData.getBrandMdmId();
          }
        }
      }
    }
    return StrUtil.EMPTY;
  }

  @Override
  public PageResult<MdmBrandPageData> getMdmBrandPage(String supplierId, String search, int pageNo,
      int pageSize) {
    return mpmService.getMdmBrand(search, pageNo, pageSize);
  }

  @Transactional
  @Override
  public void copySupplierBrandToSupplierFb(
      String supplierId, SupplierFb supplierFb, boolean copyLogoAndRemark) {
    List<Brand> brandList = dao.getBrandListBySid(supplierId);
    if (CollUtil.isNotEmpty(brandList)) {
      for (Brand brand : brandList) {
        Brand newBrand = new Brand();
        newBrand.setSupplierFb(supplierFb);
        newBrand.setBrandnameCn(StrUtil.emptyIfNull(brand.getBrandnameCn()));
        newBrand.setBrandnameEn(StrUtil.emptyIfNull(brand.getBrandnameEn()));
        newBrand.setBrandMdmId(StrUtil.emptyIfNull(brand.getBrandMdmId()));
        if (copyLogoAndRemark) {
          newBrand.setLogoUrl(StrUtil.emptyIfNull(brand.getLogoUrl()));
          newBrand.setRemark(StrUtil.emptyIfNull(brand.getRemark()));
        }
        newBrand.setState(Constants.STATE_OK);
        newBrand.setCreateTime(System.currentTimeMillis());
        repository.save(newBrand);
      }
    }
  }

  @Override
  public void deleteBrandAuthorizationById(ArrayBaseParam deleteParam) {
    String[] ids = deleteParam.getParams();
    if (ids.length <= 0) {
      throw new CheckException("ids不能为空");
    }
    for (int i = 0; i < ids.length; i++) {
      String id = ids[i];
      File file =
          fileRepository
              .findById(id)
              .orElseThrow(() -> CheckException.noFindException(File.class, id));
      file.setState(Constants.STATE_DELETE);
      fileRepository.saveAndFlush(file);
    }
  }

  @Override
  public BrandAuthorizationData getBrandAuthorizationById(String id) {
    File file =
        fileRepository
            .findById(id)
            .orElseThrow(() -> CheckException.noFindException(File.class, id));
    String[] split = file.getBrandIds().split(",");
    List<BrandData> brandDataList = new ArrayList<>();
    if (split.length > 0) {
      for (String brandId : split) {
        Brand brand = null;
        if (!StringUtils.isNullOrEmpty(brandId)) {
          brand =
              repository
                  .findById(brandId)
                  .orElseThrow(() -> CheckException.noFindException(Brand.class, brandId));
        }
        if (brand != null) {
          BrandData brandData = new BrandData();
          brandData.setBrandId(brand.getId());
          brandData.setBrandName(brand.getBrandnameCn() + ("/") + (brand.getBrandnameEn()));
          brandDataList.add(brandData);
        }
      }
    }
    List<SupplierLicenseFileAddParam> fileList = new ArrayList<>();
    if (!StringUtils.isNullOrEmpty(file.getDescription())) {
      String[] split1 = file.getDescription().split(",");
      if (split1.length > 0) {
        for (String fileName : split1) {
          SupplierLicenseFileAddParam fileAddParam = new SupplierLicenseFileAddParam();
          String[] split2 = fileName.split(";");
          if (split2.length > 0) {
            fileAddParam.setFileName(split2[0]);
            fileAddParam.setLicenceUrl(split2[1]);
          }
          fileList.add(fileAddParam);
        }
      }
    }
    BrandAuthorizationData brandAuthorizationData = new BrandAuthorizationData(file);
    brandAuthorizationData.setBrands(brandDataList);
    brandAuthorizationData.setFileList(fileList);
    return brandAuthorizationData;
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void deleteBrandById(BrandDelParam deleteParam) {
    String supplierId = deleteParam.getSupplierId();
    List<BrandNameParamDTO> brandNameParamDTOS = deleteParam.getBrandNameParamDTOS();
    if (CollUtil.isEmpty(brandNameParamDTOS)) {
      throw new CheckException("没有选择需要删除的品牌");
    }
    Supplier supplier =
        supplierRepository
            .findById(supplierId)
            .orElseThrow(() -> CheckException.noFindException(Supplier.class, supplierId));
    // 组织下供应商 id
    List<String> supplierInGroupIdList =
        supplierInGroupService.getBySupplierId(supplierId).stream()
            .map(SupplierInGroup::getId)
            .collect(Collectors.toList());
    if (CollUtil.isEmpty(supplierInGroupIdList)) {
      throw new CheckException("不存在组织下供应商，无法新增品牌");
    }
    brandNameParamDTOS.forEach(
        brandNameParamDTO -> {
          String brandNameCn = brandNameParamDTO.getBrandNameCn();
          String brandNameEn = brandNameParamDTO.getBrandNameEn();
          List<String> deleteBrandIdList = new ArrayList<>();
          supplierInGroupIdList.forEach(
              supplierInGroupId -> {
                Brand brandByRelationId =
                    dao.getBrandByRelationId(
                        supplierInGroupId,
                        BrandRelationTypeEnum.SUPPLIER_IN_GROUP.getKey(),
                        brandNameEn,
                        brandNameCn);
                if (brandByRelationId!= null) {
                  if (productDao.getProductByMdmBrandId(
                      brandByRelationId.getBrandMdmId(), supplier.getId())) {
                    throw new CheckException("【"+brandNameEn +"/"+brandNameCn+ "】该品牌有关联物料，暂无法删除");
                  }
                  deleteBrandIdList.add(brandByRelationId.getId());
                }
              });
          // 删除组织下供应商的品牌
          deleteBrandIdList.forEach(this::delete);
          // 删除授权书
          Brand brand =
              brandDao.getFirstByPermissionIdAndBrandEnAndCnName(
                  brandNameEn, brandNameCn, supplierId);
          if (brand != null) {
            List<String> fileByBrandId =
                fileDao.getFileByBrandId(brand.getId(), Constants.FILE_TYPE_PPSQ);
            if (CollUtil.isNotEmpty(fileByBrandId)) {
              for (String fileId : fileByBrandId) {
                File file = fileRepository.getOne(fileId);
                if (!StringUtils.isNullOrEmpty(file.getBrandIds())) {
                  if (file.getBrandIds().contains(brand.getId() + ",")) {
                    file.setBrandIds(file.getBrandIds().replace(brand.getId() + ",", ""));
                  } else if (file.getBrandIds().contains("," + brand.getId())) {
                    file.setBrandIds(file.getBrandIds().replace("," + brand.getId(), ""));
                  } else if (file.getBrandIds().contains(brand.getId())) {
                    file.setBrandIds(file.getBrandIds().replace(brand.getId(), ""));
                  }
                  fileRepository.save(file);
                }
              }
            }
          }
        });
  }
}
