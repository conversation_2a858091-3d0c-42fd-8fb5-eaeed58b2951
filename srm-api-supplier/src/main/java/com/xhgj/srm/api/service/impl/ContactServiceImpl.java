package com.xhgj.srm.api.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.api.dto.ArrayBaseParam;
import com.xhgj.srm.api.dto.ContactAddParam;
import com.xhgj.srm.api.dto.ContactListAddParam;
import com.xhgj.srm.api.dto.ContactPageData;
import com.xhgj.srm.api.service.ContactService;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.dao.ContactDao;
import com.xhgj.srm.jpa.entity.Contact;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierFb;
import com.xhgj.srm.jpa.entity.SupplierUser;
import com.xhgj.srm.jpa.repository.ContactRepository;
import com.xhgj.srm.jpa.repository.SupplierRepository;
import com.xhgj.srm.jpa.repository.SupplierUserRepository;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.StringUtils;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import com.xhiot.boot.framework.web.util.PageResultBuilder;
import com.xhiot.boot.mvc.base.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.Valid;
import java.util.List;

/**
 * @ClassName ContactServiceImpl
 * Create by Liuyq on 2021/6/4 17:05
 **/
@Service
public class ContactServiceImpl implements ContactService {

    @Autowired
    ContactRepository repository;
    @Autowired
    SupplierRepository supplierRepository;
    @Autowired
    SupplierUserRepository supplierUserRepository;
    @Autowired
    ContactDao contactDao;

    @Override
    public BootBaseRepository<Contact, String> getRepository() {
        return repository;
    }

    @Transactional
    @Override
    public void addContact(ContactAddParam addParam) {
        String supplierId = addParam.getSupplierId();
        String supplierUserId = addParam.getSupplierUserId();
        Supplier supplier = supplierRepository.findById(supplierId).orElseThrow(() -> CheckException.noFindException(Supplier.class, supplierId));
        SupplierUser supplierUser = supplierUserRepository.findById(supplierUserId).orElseThrow(() -> CheckException.noFindException(SupplierUser.class, supplierUserId));
        contactDao.delContactBySidAndUid(supplier.getId(), supplierUser.getId());
        List<ContactListAddParam> contactList = addParam.getContactList();
        if (!CollUtil.isEmpty(contactList)) {
            for (ContactListAddParam contactListAddParam : contactList) {
                saveContact(contactListAddParam, supplier, supplierUser);
            }
        }
    }

    @Override
    public PageResult<ContactPageData> getCurContactList(String supplierId, String supplierUserId, String pageNo, String pageSize) {
        Page<Contact> page = contactDao.getContactListBySidAndUserId(supplierId, supplierUserId, pageNo, pageSize);
        PageResult<Contact> contactPageResult = PageResultBuilder.buildPageResult(page);
        return contactPageResult.map(ContactPageData::new);
    }


    @Override
    public void deleteContactById(ArrayBaseParam deleteParam) {
        String[] ids = deleteParam.getParams();
        if (ids.length <= 0) {
            throw new CheckException("ids不能为空");
        }
        for (int i = 0; i < ids.length; i++) {
            String id = ids[i];
            Contact contact = repository.findById(id).orElseThrow(
                    () -> CheckException.noFindException(Contact.class, id));
            contact.setState(Constants.STATE_DELETE);
            repository.saveAndFlush(contact);
        }
    }

    private void saveContact(ContactListAddParam addParam, Supplier supplier, SupplierUser supplierUser) {
        String name = addParam.getName();
        String phone = addParam.getPhone();
        //判断是否存在相同联系人
        Contact co = contactDao.getCurContactBySidAndUser(supplier.getId(), phone, name, supplierUser.getId());
        if (co != null) {
            throw new CheckException("已存在关联当前供应商的联系人");
        }
        //新增联系人
        Contact contact = addParam.bulidContact(supplier, supplierUser);
        repository.saveAndFlush(contact);
        //更新供应商联系方式
        if (!StringUtils.isNullOrEmpty(phone)) {
            supplier.setMobile(phone);
            supplierRepository.saveAndFlush(supplier);
        }
//        同步已通过审核的供应商信息至erp
//            if (Constants.COMMONSTATE_OK.equals(supplier.getState()) || Constants.YES.equals(supplier.getErpSuccess())) {
//                JsonObject jo = httpsUtil.synSupplierToErp(cursup.getId(), "update", cursup.getSupType());
//                if (jo != null) {
//                    log.info("[=====添加或修改联系人供应商返回信息=====]:" + jo.toString());
//                    String code = jo.get("code").getAsString();
//                    if (!"0".equals(code)) {
//                        throw ApiException.build(ResultMsgKeyConfig.S205);
//                    }
//                } else {
//                    throw ApiException.build(ResultMsgKeyConfig.S205);
//                }
//            }
    }


    @Transactional
    @Override
    public void copySupplierContactToSupplierFb(String supplierId, SupplierFb supplierFb) {
        List<Contact> contactList = contactDao.getContactListBySid(supplierId);
        if (CollUtil.isNotEmpty(contactList)) {
            for (Contact contact : contactList) {
                Contact newContact = new Contact();
                newContact.setSupplierFb(supplierFb);
                newContact.setDuty(StrUtil.emptyIfNull(contact.getDuty()));
                newContact.setMail(StrUtil.emptyIfNull(contact.getMail()));
                newContact.setName(StrUtil.emptyIfNull(contact.getName()));
                newContact.setPhone(StrUtil.emptyIfNull(contact.getPhone()));
                newContact.setSex(StrUtil.emptyIfNull(contact.getSex()));
                newContact.setArea(StrUtil.emptyIfNull(contact.getArea()));
                newContact.setTel(StrUtil.emptyIfNull(contact.getTel()));
                newContact.setState(Constants.STATE_OK);
                newContact.setCreateTime(System.currentTimeMillis());
                repository.save(newContact);
            }
        }
    }
}
