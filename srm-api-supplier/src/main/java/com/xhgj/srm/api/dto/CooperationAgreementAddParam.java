package com.xhgj.srm.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * @ClassName CooperationAgreementAddParam
 * Create by Liuyq on 2021/7/14 20:44
 **/
@Data
public class CooperationAgreementAddParam {
    @NotBlank(message = "供应商id不能为空")
    @ApiModelProperty("供应商id")
    private String supplierId;

    @Valid
    @ApiModelProperty("上传附件")
    private List<SupplierLicenseFileAddParam> fileAddParamList;

    @NotBlank(message = "类型不能为空")
    @ApiModelProperty("类型")
    private String type;
}
