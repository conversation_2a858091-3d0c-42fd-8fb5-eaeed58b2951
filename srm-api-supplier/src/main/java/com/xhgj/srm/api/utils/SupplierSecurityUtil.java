package com.xhgj.srm.api.utils;

import com.xhgj.srm.api.domain.SrmSupplierUserDetails;
import com.xhgj.srm.jpa.entity.SupplierUser;
import com.xhiot.boot.security.util.BootSecurityUtil;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Component;

/**
 *
 */
@Component
public class SupplierSecurityUtil implements BootSecurityUtil {

  public SupplierUser getSupplierUserDetails() {
    UserDetails userDetails = getUserDetails();
    return ((SrmSupplierUserDetails) getUserDetails()).supplierUser();
  }
}
