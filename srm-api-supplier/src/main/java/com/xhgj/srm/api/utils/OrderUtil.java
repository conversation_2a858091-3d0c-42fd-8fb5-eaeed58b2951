package com.xhgj.srm.api.utils;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import com.xhgj.srm.common.Constants_order;
import com.xhgj.srm.common.constants.Constants_LockName;
import com.xhgj.srm.jpa.dao.OrderDao;
import com.xhgj.srm.jpa.dao.OrderInvoiceDao;
import com.xhgj.srm.jpa.dao.OrderInvoiceToOrderDao;
import com.xhgj.srm.jpa.entity.OrderInvoice;
import com.xhiot.boot.core.common.exception.CheckException;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

@Component
@Slf4j
public class OrderUtil {

  private static OrderInvoiceDao orderInvoiceDao;

  private static OrderDao orderDao;

  private static OrderInvoiceToOrderDao orderInvoiceToOrderDao;

  private static RedissonClient redissonClient;

  @Autowired
  public void setOrderInvoiceDao(OrderInvoiceDao orderInvoiceDao) {
    OrderUtil.orderInvoiceDao = orderInvoiceDao;
  }

  @Autowired
  public void setOrderDao(OrderDao orderDao) {
    OrderUtil.orderDao = orderDao;
  }

  @Autowired
  public void setOrderInvoiceToOrderDao(OrderInvoiceToOrderDao orderInvoiceToOrderDao) {
    OrderUtil.orderInvoiceToOrderDao = orderInvoiceToOrderDao;
  }

  @Autowired
  public void setRedissonClient(RedissonClient redissonClient) {
    OrderUtil.redissonClient = redissonClient;
  }

  /**
   * 生成开票申请单号 单个订单申请开票时，开票申请单号为：客户订单号-001（001为三位流水号） 合并开票时，开票申请单号为：HBKP年月日0001（年只取年份后两位）
   *
   * @param InvoiceApplicationType 开票类型
   * @param exteriorOrderId        客户订单号
   * @return 开票申请单号
   */
  public static String generateInvoiceApplicationNumber(String InvoiceApplicationType,
      String exteriorOrderId) {
    if (ObjectUtils.isEmpty(InvoiceApplicationType)) {
      return null;
    }
    String invoiceApplicationNumber = null;
    final String MERGE_INVOICE_APPLICATION_PREFIX = "HBKP";
    switch (InvoiceApplicationType) {
      case Constants_order.ORDER_INVOICE_TYPE_SINGLE:
        invoiceApplicationNumber = handleSingleInvoiceApplication(exteriorOrderId);
        break;
      case Constants_order.ORDER_INVOICE_TYPE_MERGE:
        invoiceApplicationNumber = handleMergeInvoiceApplication(MERGE_INVOICE_APPLICATION_PREFIX);
        break;
    }
    return invoiceApplicationNumber;
  }

  private static String supplementaryHighBit(Integer num, String type) {
    String numStr = String.valueOf(num);
    String prefix = null;

    if (Objects.equals(Constants_order.ORDER_INVOICE_TYPE_MERGE, type)) {
      switch (numStr.length()) {
        case 1:
          prefix = "000";
          break;
        case 2:
          prefix = "00";
          break;
        case 3:
          prefix = "0";
          break;
        case 4:
          prefix = "";
          break;
      }
    } else if (Objects.equals(Constants_order.ORDER_INVOICE_TYPE_SINGLE, type)) {
      switch (numStr.length()) {
        case 1:
          prefix = "00";
          break;
        case 2:
          prefix = "0";
          break;
        case 3:
          prefix = "";
          break;
      }
    } else {
      return null;
    }
    return prefix + num;
  }

  /**
   * 生成单个订单开票的开票订单号
   *
   * @param exteriorOrderId 客户订单号
   */
  private static String handleSingleInvoiceApplication(String exteriorOrderId) {
    if (ObjectUtils.isEmpty(exteriorOrderId)) {
      return null;
    }
    RLock lock = null;
    try {
      lock = redissonClient.getLock(Constants_LockName.INVOICE_APPLY_SINGLE_LOCK);
      lock.lock();
      int MAX_NUMBER = 999;
      StringBuilder invoiceApplicationNumber = new StringBuilder(exteriorOrderId + "-");
      List<OrderInvoice> like = orderInvoiceDao.findOrderInvoiceByInvoiceApplicationNumberLike(
          invoiceApplicationNumber + "%");
      if (CollectionUtils.isEmpty(like)) {
        return invoiceApplicationNumber.append("001").toString();
      } else {
        like.sort(Comparator.comparing(OrderInvoice::getCreateTime));
        OrderInvoice orderInvoice = like.get(like.size() - 1);
        String applicationNumber = orderInvoice.getInvoiceApplicationNumber();
        if (StringUtils.isEmpty(applicationNumber) || applicationNumber.length() < 3) {
          return invoiceApplicationNumber.append("001").toString();
        }
        String substring = applicationNumber.substring(applicationNumber.length() - 3);
        if (substring.length() > 3 || Integer.parseInt(substring) >= MAX_NUMBER) {
          throw new CheckException("超出开票数限制");
        }
        String num = supplementaryHighBit(Integer.parseInt(substring) + 1,
            Constants_order.ORDER_INVOICE_TYPE_SINGLE);
        invoiceApplicationNumber.append(num);
      }
      return invoiceApplicationNumber.toString();
    } catch (CheckException checkException) {
      throw checkException;
    } catch (Exception e) {
      log.error(ExceptionUtil.stacktraceToString(e, -1));
      throw new CheckException("未知异常，请联系管理员！");
    }finally {
      if (lock != null) {
        lock.unlock();
      }
    }
  }

  /**
   * 生成合并订单开票的开票订单号
   *
   * @param prefix
   */
  private static String handleMergeInvoiceApplication(CharSequence prefix) {
    if (ObjectUtils.isEmpty(prefix)) {
      return null;
    }
    RLock lock = null;
    try {
      lock = redissonClient.getLock(Constants_LockName.INVOICE_APPLY_MERGE_LOCK);
      lock.lock();
      int MAX_NUM = 9999;
      String invoiceApplicationNumber = prefix + getTodayStr();
      List<OrderInvoice> orderInvoices =
          orderInvoiceDao.findOrderInvoiceByInvoiceApplicationNumberLike(
              invoiceApplicationNumber + "%");
      if (CollectionUtils.isEmpty(orderInvoices)) {
        return invoiceApplicationNumber + "0001";
      }
      orderInvoices.sort(Comparator.comparing(OrderInvoice::getCreateTime));
      String applicationNumber =
          orderInvoices.get(orderInvoices.size() - 1).getInvoiceApplicationNumber();
      //记录为null，流水号从0001开始。
      if (StringUtils.isEmpty(applicationNumber)) {
        return invoiceApplicationNumber + "0001";
      } else {
        //流水号连续递增
        String substring = applicationNumber.substring(applicationNumber.length() - 4);
        if (substring.length() > 4 || Integer.parseInt(substring) >= MAX_NUM) {
          throw new CheckException("超出每日开票数限制");
        }
        String serialNumber = supplementaryHighBit(Integer.parseInt(substring) + 1,
            Constants_order.ORDER_INVOICE_TYPE_MERGE);
        return invoiceApplicationNumber + serialNumber;
      }
    } catch (CheckException e) {
      throw e;
    } catch (Exception e) {
      log.error(ExceptionUtil.stacktraceToString(e, -1));
      throw new CheckException("未知异常，请联系管理员！");
    }finally {
      if (lock != null) {
        lock.unlock();
      }
    }
  }

  /**
   * @return 开票申请单号的时间格式字符串
   */
  private static String getTodayStr() {
    String today = DateUtil.today();
    return today.substring(2).replace("-", "");
  }
}
