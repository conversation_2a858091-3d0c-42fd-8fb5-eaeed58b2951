package com.xhgj.srm.api.dto;

import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.entity.SupplierUser;
import com.xhiot.boot.core.common.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName SupplierUserPageData
 * Create by Liuyq on 2021/6/9 8:35
 **/
@Data
public class SupplierUserPageData {
    @ApiModelProperty("账号id")
    private String id;
    @ApiModelProperty("姓名")
    private String name;
    @ApiModelProperty("手机号码")
    private String mobile;
    @ApiModelProperty("邮箱")
    private String mail;
    @ApiModelProperty("用户名")
    private String realName;
    @ApiModelProperty("账号类型")
    private String role;

    public SupplierUserPageData(SupplierUser supplierUser) {
        this.id = supplierUser.getId();
        this.name = supplierUser.getName();
        this.mobile = supplierUser.getMobile();
        this.mail = supplierUser.getMail();
        this.realName = supplierUser.getRealName();
        this.role = !StringUtils.isNullOrEmpty(supplierUser.getRole()) ? Constants.SUPPLIER_USER_ROLE_TO_NAME.get(supplierUser.getRole()) : "";
    }
}
