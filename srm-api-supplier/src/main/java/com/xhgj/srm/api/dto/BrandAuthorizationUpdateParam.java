package com.xhgj.srm.api.dto;

import com.xhgj.srm.jpa.entity.File;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * @ClassName BrandAuthorizationAddParam
 * Create by Liuyq on 2021/6/4 11:31
 **/
@Data
public class BrandAuthorizationUpdateParam {
    @NotBlank(message = "授权类型不能为空")
    @ApiModelProperty("授权类型")
    private String type;

    @Valid
    @ApiModelProperty("上传附件")
    private List<SupplierLicenseFileAddParam> fileAddParamList;

    @NotBlank(message = "授权品牌不能为空")
    @ApiModelProperty("授权品牌")
    private String brands;
    @NotBlank(message = "文件id不能为空")
    @ApiModelProperty("文件id")
    private String fileId;


    public File updateBrandAuthorization(File file,String fileName) {
        file.setBrandIds(brands);
        file.setDescription(fileName);
        return file;
    }
}
