package com.xhgj.srm.api.dto;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.enums.VoucherAccountPeriodEnum;
import com.xhgj.srm.common.enums.inputInvoice.InputInvoiceSourceEnums;
import com.xhgj.srm.jpa.entity.InputInvoiceOrder;
import com.xhgj.srm.jpa.entity.Order;
import com.xhgj.srm.jpa.repository.OrderInvoiceRelationSplitRepository;
import com.xhgj.srm.jpa.repository.SupplierOrderRepository;
import com.xhiot.boot.core.common.exception.CheckException;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by Geng Shy on 2023/8/16
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SupplierInvoiceSaveParam {

  @ApiModelProperty(value = "发票信息")
  @NotEmpty
  private List<InvoiceParam> invoiceParam;
  @ApiModelProperty(value = "订单id集合")
  @NotEmpty
  private List<String> orderIds;
  @ApiModelProperty(value = "被删除的发票id")
  private List<String> deleteInvoiceIds;
  @ApiModelProperty(value = "被删除的订单id")
  private List<String> deleteOrderIds;
  @ApiModelProperty(value = "被删除的发票附件id")
  private List<String> deleteFileIds;
  /**
   * {@link Constants.ORDER_SUPPLIER_INVOICE_STATE_TYPE}
   * 只能是审核中或者暂存
   */
  @ApiModelProperty(value = "提交类型")
  private String dateState;
  @ApiModelProperty(value = "供应商id")
  @NotBlank
  private String supplierId;
  @ApiModelProperty(value = "操作人")
  @NotBlank
  private String userId;
  @ApiModelProperty(value = "发票单id")
  private String relationId;
  @ApiModelProperty(value = "物流公司")
  private String logisticsCompany;
  @ApiModelProperty(value = "物流单号")
  private String logisticsNum;

  /**
   * 构建落地商订单发票关联
   * @param id
   * @param platformCode
   * @param orderIds
   * @param createMan
   * @param orderAmount
   * @param handleMan
   * @param auditor
   * @param examineTime
   * @param orderSource
   * @param groupCode
   * @return
   */
  public InputInvoiceOrder buildOrderInvoiceRelation(String id, String platformCode,
      List<Order> orders, String createMan, BigDecimal orderAmount, String handleMan,
      String auditor, Long examineTime, String orderSource, String groupCode) {
    InputInvoiceOrder orderInvoiceRelation = new InputInvoiceOrder();
    List<String> invoiceNums =
        invoiceParam.stream().map(InvoiceParam::getInvoiceNum).collect(Collectors.toList());
    orderInvoiceRelation.setInvoiceNums(CollUtil.join(invoiceNums, "/"));
    BigDecimal totalAmountIncludingTax =
        invoiceParam.stream().map(InvoiceParam::getTotalAmountIncludingTax)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    orderInvoiceRelation.setInvoiceAmount(totalAmountIncludingTax);
    if (!Objects.equals(Constants.ORDER_INVOICE_STATE_ING, dateState) && !Objects.equals(
        Constants.ORDER_INVOICE_STATE_TEMP, dateState)) {
      throw new CheckException("参数异常");
    }
    if (StrUtil.isNotBlank(id)) {
      orderInvoiceRelation.setId(id);
    }
    orderInvoiceRelation.setInvoiceState(dateState);
    orderInvoiceRelation.setSupplierId(supplierId);
    orderInvoiceRelation.setPlatform(platformCode);
    orderInvoiceRelation.setCreateMan(createMan);
    orderInvoiceRelation.setOrderAmount(orderAmount);
    orderInvoiceRelation.setState(Constants.STATE_OK);
    orderInvoiceRelation.setOperator(handleMan);
    orderInvoiceRelation.setCreateTime(System.currentTimeMillis());
    orderInvoiceRelation.setAuditor(auditor);
    orderInvoiceRelation.setExamineTime(examineTime);
    orderInvoiceRelation.setLogisticsNum(logisticsNum);
    orderInvoiceRelation.setLogisticsCompany(logisticsCompany);
    orderInvoiceRelation.setOrderSource(orderSource);
    // setAccountPeriod可以去除，外面已经通过组织内供应商获取
    orderInvoiceRelation.setAccountPeriod(VoucherAccountPeriodEnum.WITHIN_7_DAYS.getDesc());
    // 设置来源
    orderInvoiceRelation.setSource(InputInvoiceSourceEnums.FRONT_DESK.getKey());
    if(Objects.equals(Constants.COOPERATE_TYPE_LANDER, orderSource)){
      List<String> orderIds = orders.stream().map(Order::getId).collect(Collectors.toList());
      List<String> orderNums = orders.stream().map(Order::getOrderNo).collect(Collectors.toList());
      orderInvoiceRelation.setOrderIds(JSON.toJSONString(orderIds));
      orderInvoiceRelation.setOrderNums(CollUtil.join(orderNums, "/"));
    }
    orderInvoiceRelation.setGroupCode(groupCode);
    // 2024年10月16日09:20:37 临时需求 电商供应商订单前台录票，校验改成含税合计必须完全一致否则无法提交
    if (orderInvoiceRelation.getOrderAmount().compareTo(orderInvoiceRelation.getInvoiceAmount()) != 0) {
      throw new CheckException("发票含税金额和订单含税金额不一致");
    }
    return orderInvoiceRelation;
  }

  /**
   * 构建供应商订单发票关联
   * @param id
   * @param platformCode
   * @param orderNums
   * @param createMan
   * @param orderAmount
   * @param handleMan
   * @param auditor
   * @param examineTime
   * @param orderSource
   * @param invoiceNumber
   * @return
   */
  public InputInvoiceOrder buildOrderInvoiceRelationBySupplierInvoice(String id,
      String platformCode,
      List<String> orderNums, String createMan, BigDecimal orderAmount, String handleMan,
      String auditor, Long examineTime, String orderSource, String invoiceNumber) {
    InputInvoiceOrder orderInvoiceRelation = new InputInvoiceOrder();
    orderInvoiceRelation.setInvoiceNums(invoiceNumber);
    BigDecimal totalAmountIncludingTax =
        invoiceParam.stream().map(InvoiceParam::getTotalAmountIncludingTax)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    orderInvoiceRelation.setInvoiceAmount(totalAmountIncludingTax);
    if (!Objects.equals(Constants.ORDER_INVOICE_STATE_ING, dateState) && !Objects.equals(
        Constants.ORDER_INVOICE_STATE_TEMP, dateState)) {
      throw new CheckException("参数异常");
    }
    if (StrUtil.isNotBlank(id)) {
      orderInvoiceRelation.setId(id);
    }
    orderInvoiceRelation.setInvoiceState(dateState);
    orderInvoiceRelation.setSupplierId(supplierId);
    orderInvoiceRelation.setPlatform(platformCode);
    orderInvoiceRelation.setCreateMan(createMan);
    orderInvoiceRelation.setOrderAmount(orderAmount);
    orderInvoiceRelation.setState(Constants.STATE_OK);
    orderInvoiceRelation.setOperator(handleMan);
    orderInvoiceRelation.setCreateTime(System.currentTimeMillis());
    orderInvoiceRelation.setAuditor(auditor);
    orderInvoiceRelation.setExamineTime(examineTime);
    orderInvoiceRelation.setLogisticsNum(logisticsNum);
    orderInvoiceRelation.setLogisticsCompany(logisticsCompany);
    orderInvoiceRelation.setOrderSource(orderSource);
    // setAccountPeriod可以去除，外面已经通过组织内供应商获取
    orderInvoiceRelation.setAccountPeriod(VoucherAccountPeriodEnum.WITHIN_7_DAYS.getDesc());
    // 设置来源
    orderInvoiceRelation.setSource(InputInvoiceSourceEnums.FRONT_DESK.getKey());
    if(Objects.equals(Constants.COOPERATE_TYPE_SUPPLIER, orderSource)){
      orderInvoiceRelation.setOrderCodes(CollUtil.join(orderNums, "/"));
    }
    return orderInvoiceRelation;
  }
}
