package com.xhgj.srm.api.dto;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotEmpty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * @ClassName BrandAuthorizationAddParam
 * Create by Liuyq on 2021/6/4 11:31
 **/
@Data
public class BrandAuthorizationAddParam {
    @NotBlank(message = "授权类型不能为空")
    @ApiModelProperty("授权类型 1 品牌授权/2 公司授权")
    private String type;
    @NotBlank(message = "授权供应商不能为空")
    @ApiModelProperty("授权供应商")
    private String supplierId;
    @Valid
    @ApiModelProperty("上传附件")
    private List<SupplierLicenseFileAddParam> fileAddParamList;

    @NotEmpty(message = "授权品牌不能为空")
    @ApiModelProperty("授权品牌名称")
    private List<BrandNameParamDTO> brandNameParamDTOS;

}
