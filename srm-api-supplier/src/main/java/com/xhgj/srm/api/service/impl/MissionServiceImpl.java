package com.xhgj.srm.api.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.PageUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.api.dto.Mission.MissionDetailPageData;
import com.xhgj.srm.api.dto.Mission.MissionPageData;
import com.xhgj.srm.api.dto.Mission.MissionPageDetailParam;
import com.xhgj.srm.api.service.MissionService;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.utils.ExportUtil;
import com.xhgj.srm.common.utils.MissionUtil;
import com.xhgj.srm.jpa.dao.MissionDao;
import com.xhgj.srm.jpa.dao.MissionDetailDao;
import com.xhgj.srm.jpa.entity.Mission;
import com.xhgj.srm.jpa.entity.MissionDetail;
import com.xhgj.srm.jpa.entity.SupplierUser;
import com.xhgj.srm.jpa.repository.MissionRepository;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.StringUtils;
import com.xhiot.boot.core.common.util.TimeUtil;
import com.xhiot.boot.mvc.base.PageResult;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.SneakyThrows;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

@Service
public class MissionServiceImpl implements MissionService {

  @Autowired private MissionDao missionDao;
  @Autowired private MissionDetailDao missionDetailDao;
  @Autowired private MissionRepository missionRepository;
  @Autowired private ExportUtil exportUtil;
  @Autowired private MissionUtil missionUtil;

  @Override
  public PageResult<MissionPageData> getMissionPage(String type, String state, String startDate,
      String endDate, String code, String supplierId, Integer curpage, Integer pagesize) {
    Page<Mission> page = missionDao.getMissionPage(type,state, startDate, endDate, code,
        Constants.PLATFORM_TYPE_BEFORE, supplierId, curpage, pagesize);
    int totalPages = page.getTotalPages();
    if (curpage > totalPages) {
      return new PageResult<>(
          Collections.emptyList(), page.getTotalElements(), totalPages, curpage, pagesize);
    }
    List<Mission> missionList = page.getContent();
    Map<String, Long> missionIdAndCount;
    if (CollUtil.isNotEmpty(missionList)) {
      List<Object[]> successMissionDetailLists =
          missionDetailDao.getSuccessMissionDetailLists(
              missionList.stream().map(Mission::getId).collect(Collectors.toList()));
      missionIdAndCount =
          CollUtil.emptyIfNull(successMissionDetailLists).stream()
              .collect(
                  Collectors.toMap(
                      object -> (String) object[0],
                      object -> Long.valueOf(String.valueOf(object[1]))));
    } else {
      missionIdAndCount = MapUtil.empty();
    }
    return new PageResult<>(
        page.getContent().parallelStream()
            .map(
                mission -> {
                  MissionPageData data = new MissionPageData(mission);
                  long successRow =
                      ObjectUtil.defaultIfNull(missionIdAndCount.get(mission.getId()), 0L);
                  String esTimeDesc;
                  data.setSuccessRow(String.valueOf(successRow));
                  if (
                    // 无法计算：任务当前是完结状态
                      !Objects.equals(mission.getState(), Constants.MISSION_STATE_ING)
                          // 无法计算：任务无成功行
                          || successRow <= 0) {
                    esTimeDesc = "-";
                  } else {
                    BigDecimal undealRow = new BigDecimal(mission.getTotalRow() - successRow);
                    if (NumberUtil.isLessOrEqual(undealRow, BigDecimal.ZERO)) {
                      // 无法计算：成功行数与总行数相同
                      esTimeDesc = "-";
                    } else {
                      BigDecimal everyTime =
                          new BigDecimal(System.currentTimeMillis() - mission.getStartTime())
                              .divide(new BigDecimal(successRow), 1, BigDecimal.ROUND_HALF_UP);
                      BigDecimal esTime = undealRow.multiply(everyTime);
                      esTimeDesc = TimeUtil.millisecondToMinute(esTime.longValue());
                    }
                  }
                  data.setEstimateTime(esTimeDesc);
                  return data;
                })
            .collect(Collectors.toList()),
        page.getTotalElements(),
        totalPages,
        curpage,
        pagesize);
  }

  @Override
  public MissionPageDetailParam getMissionDetailPage(String id, Integer curpage, Integer pagesize) {
    Mission mission =
        missionRepository
            .findById(id)
            .orElseThrow(() -> CheckException.noFindException(Mission.class, id));
    if (mission == null) {
      throw new CheckException("任务为空");
    }
    Page<MissionDetail> page = missionDetailDao.getMissionDetailPage(id, curpage, pagesize);
    int totalPages = page.getTotalPages();
    List<MissionDetailPageData> pageDataList = new ArrayList<>();
    if (!(curpage > totalPages)) {
      List<MissionDetail> missionList = page.getContent();
      PageUtil.setOneAsFirstPageNo();
      missionList.forEach(missionDetail ->{
        MissionDetailPageData data = new MissionDetailPageData(missionDetail);
        pageDataList.add(data);
      });
    }
    PageResult<MissionDetailPageData> pageDataPageResult =
        new PageResult<>(pageDataList, page.getTotalElements(), totalPages, curpage, pagesize);
    MissionPageDetailParam missionPageDetailParam = new MissionPageDetailParam();
    missionPageDetailParam.setPageDataList(pageDataPageResult);
    missionPageDetailParam.setReason(
        !StringUtils.isNullOrEmpty(mission.getReason()) ? mission.getReason() : "");
    missionPageDetailParam.setFailLink(
        !StringUtils.isNullOrEmpty(mission.getFailLink()) ? mission.getFailLink() : "");
    missionPageDetailParam.setLink(
        !StringUtils.isNullOrEmpty(mission.getLink()) ? mission.getLink() : "");
    missionPageDetailParam.setFileName(
        !StringUtils.isNullOrEmpty(mission.getFileName()) ? mission.getFileName() : "");
    missionPageDetailParam.setState(
        !StringUtils.isNullOrEmpty(mission.getState()) ? mission.getState() : "");
    missionPageDetailParam.setType(
        !StringUtils.isNullOrEmpty(mission.getType()) ? mission.getType() : "");
    return missionPageDetailParam;
  }

  @SneakyThrows
  @Override
  public byte[] exportMission(String id) {
    XSSFWorkbook workbook = new XSSFWorkbook();
    XSSFSheet sheet = (XSSFSheet) exportUtil.createSheet(workbook, "任务失败表单", Arrays.asList(30, 50));
    CellStyle baseStyle = exportUtil.getBaseStyle(workbook);
    CellStyle titleStyle = exportUtil.getTitleStyle(workbook);
    XSSFRow title = sheet.createRow(0);
    exportUtil.createCell(title, 0, "标识", titleStyle, false, false);
    exportUtil.createCell(title, 1, "说明", titleStyle, false, false);
    List<MissionDetail> missionDetailList = missionDetailDao.getFailMissionDetailList(id);
    int rowNum = 1;
    if (missionDetailList != null && missionDetailList.size() > 0) {
      for (MissionDetail missionDetail : missionDetailList) {
        XSSFRow row = sheet.createRow(rowNum);
        exportUtil.createCell(
            row,
            0,
            !StringUtils.isNullOrEmpty(missionDetail.getSign()) ? missionDetail.getSign() : "",
            baseStyle,
            false,
            false);
        exportUtil.createCell(
            row,
            1,
            !StringUtils.isNullOrEmpty(missionDetail.getInformation())
                ? missionDetail.getInformation()
                : "",
            baseStyle,
            false,
            false);
        rowNum += 1;
      }
    }
    ByteArrayOutputStream out = new ByteArrayOutputStream();
    exportUtil.write(out, workbook);
    return out.toByteArray();
  }

  @Override
  public Mission createMission(
      SupplierUser supplierUser, String type, String source, String link, String fileName) {
    Assert.notNull(supplierUser);
    Assert.notEmpty(type);
    Assert.notEmpty(source);
    // 设置任务编号
    String fileNewName = StrUtil.EMPTY;
    if (StrUtil.isNotEmpty(fileName)) {
      // 获取文件后缀名
      String fileExt = fileName.substring(fileName.lastIndexOf(".")).toLowerCase();
      // 重命名文件 并保存
      String now = String.valueOf(System.currentTimeMillis());
      fileNewName = now + fileExt;
    }
    // 新增任务
    Mission mission = Mission.createStartingMission(
        missionUtil.getMissionCode(supplierUser.getId().substring(0,4)),
        type,
        supplierUser.getId(),
        source,
        fileNewName,
        StrUtil.emptyIfNull(link)
    );
    missionRepository.save(mission);
    return mission;
  }
}
