package com.xhgj.srm.api.dto.supplierOrder;

import com.xhgj.srm.common.utils.supplierorder.BigDecimalUtil;
import com.xhgj.srm.jpa.entity.SupplierOrderDetail;
import com.xhgj.srm.jpa.entity.SupplierOrderProduct;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;
import lombok.NoArgsConstructor;

/** <AUTHOR> @ClassName DetailedProductDTO */
@Data
@NoArgsConstructor
@ApiModel("物料明细")
public class SupplierOrderDetailedProductDTO extends BaseOrderDetailProductDTO {

  @ApiModelProperty("id")
  private String id;

  @ApiModelProperty("数量")
  private BigDecimal num;

  @ApiModelProperty("待发数量")
  private BigDecimal waitQty;

  @ApiModelProperty("已发数量")
  private BigDecimal shipQty;

  @ApiModelProperty("退货数量")
  private BigDecimal returnQty;

  @ApiModelProperty("取消数量")
  private BigDecimal cancelQty;

  @ApiModelProperty("采购入库数量")
  private BigDecimal stockInputQty;

  @ApiModelProperty("退库数量")
  private BigDecimal stockOutputQty;

  @ApiModelProperty("最终结算数量")
  private BigDecimal settleQty;

  @ApiModelProperty("是否全部发货")
  private Boolean isDisabled;

  @ApiModelProperty("交货时间")
  private Long deliverTime;

  @ApiModelProperty("描述")
  private String description;

  public SupplierOrderDetailedProductDTO(
      SupplierOrderDetail supplierOrderDetail, SupplierOrderProduct supplierOrderProduct) {
    super(supplierOrderProduct, supplierOrderDetail);
    Integer unitDigit = supplierOrderProduct.getUnitDigit();
    this.id = supplierOrderDetail.getId();
    this.num = BigDecimalUtil.setScaleBigDecimalHalfUp(supplierOrderDetail.getNum(), unitDigit);
    this.waitQty =
        BigDecimalUtil.setScaleBigDecimalHalfUp(supplierOrderDetail.getWaitQty(), unitDigit);
    this.shipQty =
        BigDecimalUtil.setScaleBigDecimalHalfUp(supplierOrderDetail.getShipQty(), unitDigit);
    this.returnQty =
        BigDecimalUtil.setScaleBigDecimalHalfUp(supplierOrderDetail.getReturnQty(), unitDigit);
    this.cancelQty =
        BigDecimalUtil.setScaleBigDecimalHalfUp(supplierOrderDetail.getCancelQty(), unitDigit);
    this.stockInputQty =
        BigDecimalUtil.setScaleBigDecimalHalfUp(supplierOrderDetail.getStockInputQty(), unitDigit);

    this.settleQty =
        BigDecimalUtil.setScaleBigDecimalHalfUp(supplierOrderDetail.getSettleQty(), unitDigit);
    this.isDisabled =
        supplierOrderDetail
                .getNum()
                .compareTo(supplierOrderDetail.getShipQty().add(supplierOrderDetail.getCancelQty()))
            == 0;
    this.deliverTime = supplierOrderDetail.getDeliverTime();
    this.description = supplierOrderDetail.getDescription();
  }
}
