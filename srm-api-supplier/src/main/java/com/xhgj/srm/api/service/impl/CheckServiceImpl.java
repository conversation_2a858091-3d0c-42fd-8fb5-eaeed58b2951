package com.xhgj.srm.api.service.impl;

import com.xhgj.srm.api.dto.MatterDto;
import com.xhgj.srm.api.service.CheckService;
import com.xhgj.srm.jpa.entity.Check;
import com.xhgj.srm.jpa.repository.CheckRepository;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @ClassName CheckServiceIImpl
 * Create by Liuyq on 2021/5/31 16:12
 **/
@Service
public class CheckServiceImpl implements CheckService {

    @Autowired
    CheckRepository repository;

    @Override
    public BootBaseRepository<Check, String> getRepository() {
        return repository;
    }

    @Override
    public MatterDto getMatterInfo() {
        return null;
    }
}
