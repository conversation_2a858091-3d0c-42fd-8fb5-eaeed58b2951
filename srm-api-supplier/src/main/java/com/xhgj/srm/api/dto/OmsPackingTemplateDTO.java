package com.xhgj.srm.api.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;

/**
 * OmsPackingTemplateDTO
 */
@Data
public class OmsPackingTemplateDTO {
  @ApiModelProperty("返回信息")
  @JsonProperty("msg")
  private String msg;
  @ApiModelProperty("code码")
  @JsonProperty("code")
  private Integer code;
  @ApiModelProperty("数据")
  @JsonProperty("data")
  private List<DataDTO> data;

  @NoArgsConstructor
  @Data
  public static class DataDTO {
    @ApiModelProperty("订单时间")
    @JsonProperty("orderTime")
    private String orderTime;

    @ApiModelProperty("客户名称")
    @JsonProperty("customerUnit")
    private String customerUnit;

    @ApiModelProperty("客户订单号")
    @JsonProperty("dockingOrderNo")
    private String dockingOrderNo;

    @ApiModelProperty("咸亨订单号")
    @JsonProperty("xhOrderNo")
    private String xhOrderNo;

    @ApiModelProperty("单位名称")
    @JsonProperty("purchaseDept")
    private String purchaseDept;

    @ApiModelProperty("收货人")
    @JsonProperty("consignee")
    private String consignee;

    @ApiModelProperty("电话")
    @JsonProperty("mobile")
    private String mobile;

    @ApiModelProperty("收获地址")
    @JsonProperty("address")
    private String address;

    @ApiModelProperty("订单备注")
    @JsonProperty("remark")
    private String remark;

    @ApiModelProperty("下单人")
    @JsonProperty("orderManName")
    private String orderManName;

    @ApiModelProperty("下单人电话")
    @JsonProperty("orderManMobile")
    private String orderManMobile;

    @ApiModelProperty("咸亨销售员")
    @JsonProperty("businessManName")
    private String businessManName;

    @ApiModelProperty("服务电话")
    @JsonProperty("businessManMobile")
    private String businessManMobile;

    @ApiModelProperty("验收单详情信息列表")
    @JsonProperty("receiptDetailInfoDTOList")
    private List<ReceiptDetailInfoDTO> receiptDetailInfoDTOList;

    @Data
    public static class ReceiptDetailInfoDTO {
      @ApiModelProperty("商品编码")
      @JsonProperty("platformProductCode")
      private String platformProductCode;

      @ApiModelProperty("平台物资编码")
      @JsonProperty("platformMaterialCode")
      private String platformMaterialCode;

      @ApiModelProperty("商品名称")
      @JsonProperty("productName")
      private String productName;

      @ApiModelProperty("型号")
      @JsonProperty("productModel")
      private String productModel;

      @ApiModelProperty("采购数量")
      @JsonProperty("num")
      private String num;

      @ApiModelProperty("发货数量")
      @JsonProperty("deliveryNum")
      private String deliveryNum;

      @ApiModelProperty("商品单位")
      @JsonProperty("productUnit")
      private String productUnit;

      @ApiModelProperty("销售单详情信息列表")
      @JsonProperty("saleOrderDetailInfoDTOList")
      private List<SaleOrderDetailInfoDTO> saleOrderDetailInfoDTOList;

      @Data
      public static class SaleOrderDetailInfoDTO {
        @ApiModelProperty("销售订单号")
        @JsonProperty("saleOrderNo")
        private String saleOrderNo;

        @ApiModelProperty("销售订单物料行id")
        @JsonProperty("rowNo")
        private String rowNo;

        @JsonProperty("productCode")
        private String productCode;

        @JsonProperty("platformProductCode")
        private String platformProductCode;
      }
    }
  }
}
