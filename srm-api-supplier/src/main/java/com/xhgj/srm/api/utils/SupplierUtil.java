package com.xhgj.srm.api.utils;

import com.xhgj.srm.api.dto.BusinessUpdateParam;
import com.xhgj.srm.api.service.*;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.dao.SupplierFbDao;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierChangeInfo;
import com.xhgj.srm.jpa.entity.SupplierFb;
import com.xhgj.srm.jpa.repository.*;
import com.xhiot.boot.core.common.util.DateUtils;
import com.xhiot.boot.core.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class SupplierUtil {

    @Autowired
    SupplierFbService supplierFbService;
    @Autowired
    SupplierService supplierService;
    @Autowired
    BrandService brandService;
    @Autowired
    ContactService contactService;
    @Autowired
    FinancialService financialService;
    @Autowired
    FileService fileService;
    @Autowired
    ExtraFileService extraFileService;
    @Autowired
    SupplierFbRepository supplierFbRepository;
    @Autowired
    BrandRepository brandRepository;
    @Autowired
    ContactRepository contactRepository;
    @Autowired
    FinancialRepository financialRepository;
    @Autowired
    SupplierRepository supplierRepository;
    @Autowired
    SupplierFbDao supplierFbDao;
    @Autowired
    SupplierChangeInfoRepository supplierChangeInfoRepository;

    /**
     * @return java.lang.String
     * @Title UpdateSupplierFb
     * @description 根据供应商信息新增副本信息
     * <AUTHOR>
     * @date 2019/8/12 11:27
     */
    public String UpdateSupplierFb(String supplierId) {
        SupplierFb supplierFb = supplierFbService.addBySupplier(supplierId);
        // 品牌
        brandService.copySupplierBrandToSupplierFb(supplierId, supplierFb, false);
        // 联系人
        contactService.copySupplierContactToSupplierFb(supplierId, supplierFb);
        //财务信息
        financialService.copySupplierFinancialToSupplierFb(supplierId, supplierFb);
        // 保存协议
        fileService.copySupplierAgreeFileToSupplierFb(supplierId, supplierFb.getId());
        // 附件
        fileService.copySupplierFileToSupplierFb(supplierId, supplierFb.getId());
        // 自定义附件
        extraFileService.copySupplierZDYFileToSupplierFb(supplierId, supplierFb.getId());

        return supplierFb.getId();
    }

    public void comparToChange(BusinessUpdateParam updateParam, Supplier supplier) {
        //企业名称
        if (!StringUtils.isNullOrEmpty(updateParam.getEnterpriseName()) && !updateParam.getEnterpriseName().equals(supplier.getEnterpriseName())) {
            saveSupplierChange(updateParam.getEnterpriseName(), supplier.getEnterpriseName(), supplier, "企业名称");
        }
        //法定代表人
        if (!StringUtils.isNullOrEmpty(updateParam.getCorporate()) && !updateParam.getCorporate().equals(supplier.getCorporate())) {
            saveSupplierChange(updateParam.getCorporate(), supplier.getCorporate(), supplier, "法定代表人");
        }
        //经营状态
        if (!StringUtils.isNullOrEmpty(updateParam.getRegStatus()) && !updateParam.getRegStatus().equals(supplier.getManageType())) {
            saveSupplierChange(updateParam.getRegStatus(), supplier.getManageType(), supplier, "经营状态");
        }
        //统一社会信用代码
        if (!StringUtils.isNullOrEmpty(updateParam.getUscc()) && !updateParam.getUscc().equals(supplier.getUscc())) {
            saveSupplierChange(updateParam.getUscc(), supplier.getUscc(), supplier, "统一社会信用代码");
        }
        //成立日期
        if (supplier.getDate() != null && supplier.getDate() > 0) {
            if (!StringUtils.isNullOrEmpty(updateParam.getDate()) && !updateParam.getDate().equals(DateUtils.formatTimeStampToNormalDate(supplier.getDate()))) {
                saveSupplierChange(updateParam.getDate(), DateUtils.formatTimeStampToNormalDate(supplier.getDate()), supplier, "成立日期");
            }
        } else {
            if (!StringUtils.isNullOrEmpty(updateParam.getDate())) {
                saveSupplierChange(updateParam.getDate(), "", supplier, "成立日期");
            }
        }
        //营业期限-开始时间
        if (supplier.getStartDate() != null && supplier.getStartDate() > 0) {
            if (!StringUtils.isNullOrEmpty(updateParam.getStartDate()) && !updateParam.getStartDate().equals(DateUtils.formatTimeStampToNormalDate(supplier.getStartDate()))) {
                saveSupplierChange(updateParam.getStartDate(), DateUtils.formatTimeStampToNormalDate(supplier.getStartDate()), supplier, "营业期限-开始时间");
            }
        } else {
            if (!StringUtils.isNullOrEmpty(updateParam.getStartDate())) {
                saveSupplierChange(updateParam.getStartDate(), "", supplier, "成立日期");
            }
        }
        //营业期限-结束时间
        if (supplier.getEndDate() != null && supplier.getEndDate() > 0) {
            if (!StringUtils.isNullOrEmpty(updateParam.getEndDate()) && !updateParam.getEndDate().equals(DateUtils.formatTimeStampToNormalDate(supplier.getEndDate()))) {
                saveSupplierChange(updateParam.getEndDate(), DateUtils.formatTimeStampToNormalDate(supplier.getEndDate()), supplier, "营业期限-结束时间");
            }
        } else {
            if (!StringUtils.isNullOrEmpty(updateParam.getEndDate())) {
                saveSupplierChange(updateParam.getEndDate(), "", supplier, "成立日期");
            }
        }
        //注册资本
        if (!StringUtils.isNullOrEmpty(updateParam.getRegCapital()) && !updateParam.getRegCapital().equals(supplier.getRegCapital())) {
            saveSupplierChange(updateParam.getRegCapital(), supplier.getRegCapital(), supplier, "注册资本");
        }
        //公司性质
        if (!StringUtils.isNullOrEmpty(updateParam.getEnterpriseNature()) && !updateParam.getEnterpriseNature().equals(supplier.getEnterpriseNature())) {
            saveSupplierChange(updateParam.getEnterpriseNature(), supplier.getEnterpriseNature(), supplier, "公司性质");
        }
        //实缴资本
        if (!StringUtils.isNullOrEmpty(updateParam.getPaidCapital()) && !updateParam.getPaidCapital().equals(supplier.getPaidCapital())) {
            saveSupplierChange(updateParam.getPaidCapital(), supplier.getPaidCapital(), supplier, "实缴资本");
        }
        //参保人数
        if (!StringUtils.isNullOrEmpty(updateParam.getInsNum()) && !updateParam.getInsNum().equals(supplier.getInsNum())) {
            saveSupplierChange(updateParam.getInsNum(), supplier.getInsNum(), supplier, "参保人数");
        }
        //纳税人识别号
        if (!StringUtils.isNullOrEmpty(updateParam.getTaxNumber()) && !updateParam.getTaxNumber().equals(supplier.getTaxNumber())) {
            saveSupplierChange(updateParam.getTaxNumber(), supplier.getTaxNumber(), supplier, "纳税人识别号");
        }
        //曾用名
        if (!StringUtils.isNullOrEmpty(updateParam.getUsedName()) && !updateParam.getUsedName().equals(supplier.getUsedName())) {
            saveSupplierChange(updateParam.getUsedName(), supplier.getUsedName(), supplier, "曾用名");
        }
        //纳税人资质
        if (!StringUtils.isNullOrEmpty(updateParam.getTaxQualification()) && !updateParam.getTaxQualification().equals(supplier.getTaxQualification())) {
            saveSupplierChange(updateParam.getTaxQualification(), supplier.getTaxQualification(), supplier, "纳税人资质");
        }
        //英文名称
        if (!StringUtils.isNullOrEmpty(updateParam.getEnglishName()) && !updateParam.getEnglishName().equals(supplier.getEnglishName())) {
            saveSupplierChange(updateParam.getEnglishName(), supplier.getEnglishName(), supplier, "英文名称");
        }
        //登记机关
        if (!StringUtils.isNullOrEmpty(updateParam.getRegAuthority()) && !updateParam.getRegAuthority().equals(supplier.getRegAuthority())) {
            saveSupplierChange(updateParam.getRegAuthority(), supplier.getRegAuthority(), supplier, "登记机关");
        }
        //工商注册号
        if (!StringUtils.isNullOrEmpty(updateParam.getRegNo()) && !updateParam.getRegNo().equals(supplier.getRegNo())) {
            saveSupplierChange(updateParam.getRegNo(), supplier.getRegNo(), supplier, "工商注册号");
        }
        //组织机构代码
        if (!StringUtils.isNullOrEmpty(updateParam.getOrgCode()) && !updateParam.getOrgCode().equals(supplier.getOrgCode())) {
            saveSupplierChange(updateParam.getOrgCode(), supplier.getOrgCode(), supplier, "组织机构代码");
        }
        //行业
        if (!StringUtils.isNullOrEmpty(updateParam.getIndustry()) && !updateParam.getIndustry().equals(supplier.getIndustry())) {
            saveSupplierChange(updateParam.getIndustry(), supplier.getIndustry(), supplier, "行业");
        }
        //人员规模
        if (!StringUtils.isNullOrEmpty(updateParam.getPeopleNum()) && !updateParam.getPeopleNum().equals(supplier.getPeopleNum())) {
            saveSupplierChange(updateParam.getPeopleNum(), supplier.getPeopleNum(), supplier, "人员规模");
        }
        //注册地址
        if (!StringUtils.isNullOrEmpty(updateParam.getRegAddress()) && !updateParam.getRegAddress().equals(supplier.getRegAddress())) {
            saveSupplierChange(updateParam.getRegAddress(), supplier.getRegAddress(), supplier, "注册地址");
        }
        //经营范围
        if (!StringUtils.isNullOrEmpty(updateParam.getBusinessScope()) && !updateParam.getBusinessScope().equals(supplier.getBusinessScope())) {
            saveSupplierChange(updateParam.getBusinessScope(), supplier.getBusinessScope(), supplier, "经营范围");
        }
    }

    private void saveSupplierChange(String before, String after, Supplier supplier, String field) {
        SupplierChangeInfo changeInfo = new SupplierChangeInfo();
        changeInfo.setSupplier(supplier);
        changeInfo.setSupplierId(supplier.getId());
        SupplierFb supplierFb = supplierFbDao.getSupplierFbBySupId(supplier.getId());
        if (supplierFb != null) {
            changeInfo.setSupplierFb(supplierFb);
        }
        changeInfo.setChangeField(field);
        changeInfo.setBeforeInfo(before);
        changeInfo.setAfterInfo(after);
        changeInfo.setCreateTime(System.currentTimeMillis());
        changeInfo.setCheckState(Constants.SUPPLIER_CHANGE_RECODE_CHECK);
        changeInfo.setState(Constants.STATE_OK);
        supplierChangeInfoRepository.saveAndFlush(changeInfo);
    }
}
