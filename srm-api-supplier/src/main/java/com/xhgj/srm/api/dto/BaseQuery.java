package com.xhgj.srm.api.dto;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import lombok.Data;

/** <AUTHOR> @ClassName BaseQuery */
@Data
public class BaseQuery {
  @ApiModelProperty(value = "供应商 id", required = true)
  @NotBlank(message = "供应商 id 必传")
  private String supplierId;

  @ApiModelProperty(value = "用户 id", required = true)
  @NotBlank(message = "用户 id 必传")
  private String userId;

  @ApiModelProperty(value = "方案 id ")
  private String schemeId;
}
