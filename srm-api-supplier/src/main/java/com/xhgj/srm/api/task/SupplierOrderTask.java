package com.xhgj.srm.api.task;

import com.xhgj.srm.api.service.SupplierOrderToFormService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2023-02-16 10:11
 */
@Component
@Configuration
@EnableScheduling
@Slf4j
public class SupplierOrderTask {
  @Autowired
  private SupplierOrderToFormService supplierOrderToFormService;

  /**
   * 提醒采购收货
   */
  @Scheduled(cron = "0 0 1 * * ?")
  private void remindPurchaseReceipt(){
    supplierOrderToFormService.remindPurchaseReceipt(null,null);
  }
  /**
   * 每天上午8点 针对当前订单已签收，但订单状态是未完成的订单，给负责采购发送消息通知
   */
  @Scheduled(cron = "0 0 8 * * ?")
  private void sendDingMsgToPurchase() {
    log.info("每日上午8点发送消息通知，开始执行=============================");
    supplierOrderToFormService.sendDingMsgToPurchase();
    log.info("每日上午8点发送消息通知，执行结束=============================");
  }
}
