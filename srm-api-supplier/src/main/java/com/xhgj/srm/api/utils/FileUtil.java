package com.xhgj.srm.api.utils;

import cn.hutool.core.collection.CollUtil;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class FileUtil {

    /**
     * 获取指定字符开头的文件，并按名称升序排列
     *
     * @param fileNames
     * @param filterStr
     * @return
     */
    public List<String> getFilterSortFileNames(List<String> fileNames, String filterStr) {
        if (CollUtil.isEmpty(fileNames)) {
            return null;
        }
        List<String> nameList = new ArrayList<>();
        try {
            for (String name : fileNames) {
                if (name.toLowerCase().indexOf(filterStr) == 0) {
                    nameList.add(name);
                }
            }
            if (nameList.size() > 0) {
                nameList.sort(
                        (o1, o2) -> {
                            String name1 = o1.toLowerCase().replace(filterStr, "");
                            String name2 = o2.toLowerCase().replace(filterStr, "");
                            Integer num1 = Integer.parseInt(name1.substring(0, name1.lastIndexOf(".")));
                            Integer num2 = Integer.parseInt(name2.substring(0, name2.lastIndexOf(".")));
                            return num1.compareTo(num2);
                        });
            }
            return nameList;
        } catch (Exception e) {
            return null;
        }
    }

}
