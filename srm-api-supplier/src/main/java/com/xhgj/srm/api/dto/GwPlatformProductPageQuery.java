package com.xhgj.srm.api.dto;

import com.xhgj.srm.jpa.dto.BaseDefaultSearchSchemeForm;
import com.xhiot.boot.framework.web.dto.param.PageParam;
import io.swagger.annotations.ApiParam;
import lombok.Data;

@Data
public class GwPlatformProductPageQuery extends PageParam implements BaseDefaultSearchSchemeForm {

  @ApiParam(value = "供应商编码")
  private String supplierCode;

  @ApiParam(value = "国网商品编码")
  private String gwProductSerialNumber;

  @ApiParam(value = "咸亨物料编码")
  private String productCode;

  @ApiParam(value = "国网物料编码")
  private String gwCode;

  @ApiParam(value = "物料名称")
  private String proName;

  @ApiParam(value = "产品编码")
  private String spuModel;

  @ApiParam(value = "类目名称")
  private String categoryName;

  @ApiParam(value = "用户id")
  private String userId;

  @ApiParam(value = "搜索方案id")
  private String schemeId;
}
