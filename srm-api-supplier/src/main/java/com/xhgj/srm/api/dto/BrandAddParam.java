package com.xhgj.srm.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * @ClassName BrandAddParam
 * Create by Liuyq on 2021/6/3 10:02
 **/
@Data
public class BrandAddParam {
    @NotBlank(message = "供应商id不能为空")
    @ApiModelProperty("供应商id")
    private String supplierId;
    @ApiModelProperty("品牌列表")
    @Valid
    private List<BrandListAddParam> brands;
}
