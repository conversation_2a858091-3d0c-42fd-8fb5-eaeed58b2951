package com.xhgj.srm.api.service.impl;

import com.xhgj.srm.api.service.ExpandService;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.dao.ExpandDao;
import com.xhgj.srm.jpa.entity.Expand;
import com.xhgj.srm.jpa.repository.ExpandRepository;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
public class ExpandServiceImpl implements ExpandService {

    @Autowired
    ExpandRepository expandRepository;

    @Autowired
    ExpandDao expandDao;

    @Override
    public BootBaseRepository<Expand, String> getRepository() {
        return null;
    }

    @Transactional
    @Override
    public void deleteExpand(String productId) {
        String hql = "delete from Expand e where e.state = ? and e.product.id = ? ";
        Object[] params = new Object[]{Constants.STATE_OK, productId};
        expandDao.executeUpdate(hql, params);
    }
}
