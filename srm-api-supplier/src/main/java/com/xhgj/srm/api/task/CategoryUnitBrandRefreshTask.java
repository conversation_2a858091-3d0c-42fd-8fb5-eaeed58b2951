package com.xhgj.srm.api.task;

import com.xhgj.srm.request.service.third.mpm.MPMService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * 导出类目/单位/品牌参考数据 刷新任务
 */
@Component
@EnableScheduling
@Slf4j
public class CategoryUnitBrandRefreshTask {

  @Resource
  MPMService mpmService;
  /**
   * 每日0点 与 12点 刷新类目/单位/品牌参考数据
   */
  @Scheduled(cron = "0 0 0,12 * * ?")
  private void refreshCategoryUnitBrand() {
    log.info("每日0点 与 12点 刷新类目/单位/品牌参考数据，开始执行=============================");
    mpmService.refreshExportReferenceData();
    log.info("每日0点 与 12点 刷新类目/单位/品牌参考数据，执行结束=============================");
  }

}
