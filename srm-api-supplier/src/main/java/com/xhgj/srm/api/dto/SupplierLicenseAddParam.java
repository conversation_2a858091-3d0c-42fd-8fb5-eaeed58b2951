package com.xhgj.srm.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * @ClassName SupplierLicenseAddParam
 * Create by Liuyq on 2021/6/3 17:32
 **/
@Data
public class SupplierLicenseAddParam {
    @NotBlank(message = "供应商id不能为空")
    @ApiModelProperty("供应商id")
    private String supplierId;

    @ApiModelProperty("文件数组")
    private List<SupplierLicenseFileAddParam> fileAddParamList;

    @ApiModelProperty("自定义附件 自定义名+';'+路径+'&#&'")
    private String zdy;

    @NotBlank(message = "类型不能为空")
    @ApiModelProperty("类型")
    private String type;

}
