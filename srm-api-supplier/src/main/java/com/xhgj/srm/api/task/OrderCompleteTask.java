package com.xhgj.srm.api.task;

import com.xhgj.srm.api.service.OrderService;
import com.xhgj.srm.api.service.SupplierOrderToFormService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@Configuration
@EnableScheduling
public class OrderCompleteTask {

    @Autowired
    private OrderService orderService;
    @Autowired
    private SupplierOrderToFormService supplierOrderToFormService;

    /**
     * 定时更新订单物流状态
     */
    @Scheduled(cron = "0 0 1 * * ?")
    private void updateOrderLogistic() {
        orderService.updateOrderLogistic();
    }

    /**
     * 定时更新订单物流状态
     */
    @Scheduled(cron = "0 0 1 * * ?")
    private void updateOrderSignState() {
        supplierOrderToFormService.updateOrderSignState(null,null);
    }

}
