package com.xhgj.srm.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * @ClassName SupplierUserUpdatePasswordParam
 * Create by Liuyq on 2021/6/9 15:41
 **/
@Data
public class SupplierUserUpdatePasswordParam{
    @ApiModelProperty("账号id")
    @NotEmpty(message = "账号id不能为空")
    private String supplierUserId;
    @ApiModelProperty("原密码")
    @NotEmpty(message = "原密码不能为空")
    private String originalPassword;
    @ApiModelProperty("新密码")
    @NotEmpty(message = "新密码不能为空")
    private String newPassword;
    @ApiModelProperty("确认新密码")
    @NotEmpty(message = "确认新密码不能为空")
    private String confirmNewPassword;

}
