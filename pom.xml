<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>com.xhgj</groupId>
  <artifactId>srm-boot</artifactId>
  <version>3.0.0-SNAPSHOT</version>
  <modules>
    <module>srm-jpa</module>
    <module>srm-common</module>
    <module>srm-service</module>
    <module>srm-request</module>
    <module>srm-api-manage</module>
    <module>srm-addons</module>
    <module>srm-api-supplier</module>
    <module>srm-batch</module>
    <module>srm-mq</module>
    <module>srm-api-portal</module>
    <module>srm-api-portal-provider</module>
    <module>srm-api-mobile</module>
    <module>srm-mission-consumer</module>
    <module>srm-mission-common</module>
    <module>srm-mission-dispatcher</module>
  </modules>

  <properties>
    <maven.compiler.source>8</maven.compiler.source>
    <maven.compiler.target>8</maven.compiler.target>
    <mapstruct.version>1.5.5.Final</mapstruct.version>
    <common-io.version>2.18.0</common-io.version>
    <snakeyaml.version>1.33</snakeyaml.version>
  </properties>
  <parent>
    <artifactId>xhiot-boot</artifactId>
    <groupId>com.xhiot.xhiot-boot</groupId>
    <version>1.2.6-SNAPSHOT</version>
  </parent>
  <packaging>pom</packaging>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.xhgj</groupId>
        <artifactId>srm-jpa</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.xhgj</groupId>
        <artifactId>srm-common</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>com.xhgj</groupId>
        <artifactId>srm-service</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.xhgj</groupId>
        <artifactId>srm-request</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.xhgj</groupId>
        <artifactId>srm-batch</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.xhgj</groupId>
        <artifactId>srm-mq</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.xhgj</groupId>
        <artifactId>srm-api-portal-provider</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.xhgj</groupId>
        <artifactId>srm-mission-common</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.xhgj</groupId>
        <artifactId>srm-mission-dispatcher</artifactId>
        <version>${project.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <dependencies>
    <!-- SpringCloud 框架 jar 包-->
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-dependencies</artifactId>
      <version>Greenwich.SR6</version>
      <type>pom</type>
      <scope>import</scope>
    </dependency>
    <dependency>
      <groupId>com.alibaba.cloud</groupId>
      <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
      <version>2.1.4.RELEASE</version>
      <exclusions>
        <exclusion>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-starter-logging</artifactId>
        </exclusion>
        <exclusion>
          <artifactId>commons-io</artifactId>
          <groupId>commons-io</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.alibaba.cloud</groupId>
      <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
      <exclusions>
        <exclusion>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-starter-logging</artifactId>
        </exclusion>
        <exclusion>
          <artifactId>archaius-core</artifactId>
          <groupId>com.netflix.archaius</groupId>
        </exclusion>
        <exclusion>
          <artifactId>guava</artifactId>
          <groupId>com.google.guava</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jsr305</artifactId>
          <groupId>com.google.code.findbugs</groupId>
        </exclusion>
        <exclusion>
          <artifactId>servo-core</artifactId>
          <groupId>com.netflix.servo</groupId>
        </exclusion>
      </exclusions>
      <version>2.1.4.RELEASE</version>
    </dependency>
    <dependency>
      <groupId>com.github.dadiyang</groupId>
      <artifactId>equator</artifactId>
      <version>1.0.4</version>
    </dependency>
    <dependency>
      <groupId>com.xhiot.xhiot-boot</groupId>
      <artifactId>boot-event-sys-error-ding</artifactId>
    </dependency>
    <!-- 强制所有子模块使用 MapStruct 1.4 版本 -->
    <dependency>
      <groupId>org.mapstruct</groupId>
      <artifactId>mapstruct</artifactId>
      <version>${mapstruct.version}</version><!-- 强制使用 1.4 版本 -->
    </dependency>
    <dependency>
      <groupId>org.mapstruct</groupId>
      <artifactId>mapstruct-processor</artifactId>
      <version>${mapstruct.version}</version><!-- 强制使用 1.4 版本 -->
    </dependency>
    <dependency>
      <groupId>org.yaml</groupId>
      <artifactId>snakeyaml</artifactId>
      <version>${snakeyaml.version}</version><!-- 强制使用 1.4 版本 -->
    </dependency>
  </dependencies>
</project>