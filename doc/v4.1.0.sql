# 供应商表新增字段
ALTER TABLE t_supplier
    ADD mdm_code varchar(20) NULL COMMENT 'MDM 主数据编码';

# 供应商模板表
CREATE TABLE `t_supplier_template`
(
    `id`            varchar(32) NOT NULL COMMENT '主键',
    `c_group_id`    varchar(32) DEFAULT NULL COMMENT '组织 id',
    `c_state`       varchar(1)  DEFAULT NULL COMMENT '数据状态',
    `c_create_time` bigint(20)  DEFAULT NULL COMMENT '创建时间',
    `c_update_time` bigint(20)  DEFAULT NULL COMMENT '修改时间',
    `c_create_man`  varchar(32) DEFAULT NULL COMMENT '创建人',
    `c_update_man`  varchar(32) DEFAULT NULL COMMENT '修改人',
    PRIMARY KEY (`id`),
    KEY `t_supplier_template_FK` (`c_group_id`),
    CONSTRAINT `t_supplier_template_FK` FOREIGN KEY (`c_group_id`) REFERENCES `t_group` (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT ='供应商模板表';

# 供应商模板字段表
CREATE TABLE `t_supplier_template_field`
(
    `id`                   varchar(32) NOT NULL COMMENT '主键',
    `supplier_template_id` varchar(32)  DEFAULT NULL COMMENT '供应商模板 id',
    `c_type`               varchar(1)   DEFAULT NULL COMMENT '模板类型（1：国内，2：国外，3：个人）',
    `c_field_name`         varchar(100) DEFAULT NULL COMMENT '字段名称',
    `c_required`           tinyint(1)   DEFAULT NULL COMMENT '是否必须',
    `c_state`              varchar(1)   DEFAULT NULL COMMENT '数据状态',
    `c_create_time`        bigint(20)   DEFAULT NULL COMMENT '创建时间',
    `c_update_time`        bigint(20)   DEFAULT NULL COMMENT '修改时间',
    `c_create_man`         varchar(32)  DEFAULT NULL COMMENT '创建人',
    `c_update_man`         varchar(32)  DEFAULT NULL COMMENT '修改人',
    PRIMARY KEY (`id`),
    KEY `t_supplier_template_field_FK` (`supplier_template_id`),
    CONSTRAINT `t_supplier_template_field_FK` FOREIGN KEY (`supplier_template_id`) REFERENCES `t_supplier_template` (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT ='供应商模板字段表';

# 供应商工商信息表
CREATE TABLE `t_supplier_biz_info`
(
    `supplier_id`         VARCHAR(32) NOT NULL COMMENT '供应商 id',
    `c_manage_type`       VARCHAR(31)  DEFAULT NULL COMMENT '经营状态',
    `c_start_date`        BIGINT(20)   DEFAULT 0 COMMENT '营业期限 - 起始时间',
    `c_end_date`          BIGINT(20)   DEFAULT 0 COMMENT '营业期限 - 结束时间',
    `c_people_num`        VARCHAR(10)  DEFAULT NULL COMMENT '人员规模',
    `c_ins_num`           VARCHAR(10)  DEFAULT NULL COMMENT '参保人数',
    `c_used_name`         VARCHAR(50)  DEFAULT NULL COMMENT '曾用名',
    `c_english_name`      VARCHAR(200) DEFAULT NULL COMMENT '英文名称',
    `c_reg_no`            VARCHAR(32)  DEFAULT NULL COMMENT '工商注册号',
    `c_reg_address`       VARCHAR(100) DEFAULT NULL COMMENT '注册地址',
    `c_corporate`         VARCHAR(100) DEFAULT NULL COMMENT '法人代表/负责人',
    `c_date`              BIGINT(20)   DEFAULT 0 COMMENT '成立日期',
    `c_reg_capital`       VARCHAR(32)  DEFAULT NULL COMMENT '注册资本',
    `c_paid_capital`      VARCHAR(50)  DEFAULT NULL COMMENT '实缴资本',
    `c_tax_number`        VARCHAR(30)  DEFAULT NULL COMMENT '纳税人识别号',
    `c_tax_qualification` VARCHAR(200) DEFAULT NULL COMMENT '纳税人资质',
    `c_company_org_type`  VARCHAR(50)  DEFAULT NULL COMMENT '企业类型',
    `c_reg_authority`     VARCHAR(50)  DEFAULT NULL COMMENT '登记机关',
    `c_org_code`          VARCHAR(30)  DEFAULT NULL COMMENT '组织机构代码',
    `c_approved_time`     BIGINT(20)   DEFAULT 0 COMMENT '核准时间',
    `c_business_scope`    TEXT         DEFAULT NULL COMMENT '经营范围',
    primary key (`supplier_id`),
    KEY `t_biz_info_fk_t_supplier` (`supplier_id`),
    CONSTRAINT `t_biz_info_fk_t_supplier` FOREIGN KEY (`supplier_id`) REFERENCES `t_supplier` (`id`) ON DELETE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT = '供应商工商信息表';


# 组织内供应商表
CREATE TABLE `t_supplier_in_group`
(
    `id`                  VARCHAR(32) NOT NULL COMMENT 'id',
    `supplier_id`         VARCHAR(32)  DEFAULT NULL COMMENT '供应商 id',
    `group_id`            VARCHAR(32)  DEFAULT NULL COMMENT '组织 id',
    `purchaser_id`        VARCHAR(32)  DEFAULT NULL COMMENT '负责采购员 id',
    # 业务信息
    `c_license_url`       VARCHAR(200) DEFAULT NULL COMMENT '营业执照 - 文件路径',
    `c_details`           TEXT         DEFAULT NULL COMMENT '邮寄地址',
    `c_enterprise_nature` VARCHAR(100) DEFAULT NULL COMMENT '供应商性质',
    `c_enterprise_level`  VARCHAR(2)   DEFAULT NULL COMMENT '供应商等级',
    `c_integrity`         VARCHAR(10)  DEFAULT NULL COMMENT '资料完整度',
    # 财务信息
    `c_account_period`    VARCHAR(1)   DEFAULT NULL COMMENT '账期',
    `c_invoice_type`      VARCHAR(2)   DEFAULT NULL COMMENT '发票类型',
    `c_tax_rate`          VARCHAR(50)  DEFAULT NULL COMMENT '默认税率',
    `c_settle_currency`   VARCHAR(20)  DEFAULT NULL COMMENT '结算币别',
    # 审核信息
    `c_audit_state`       VARCHAR(2)   DEFAULT NULL COMMENT '审核状态',
    `c_is_cancel`         VARCHAR(32)  DEFAULT NULL COMMENT '是否撤回',
    `c_manage_id`         VARCHAR(32)  DEFAULT NULL COMMENT '审核人 id',
    # 拉黑信息
    `c_shielding_people`  VARCHAR(32)  DEFAULT NULL COMMENT '拉黑人',
    `c_shield_state`      VARCHAR(1)   DEFAULT NULL COMMENT '拉黑状态',
    `c_reason`            text         DEFAULT NULL COMMENT '拉黑原因',
    `c_shield_manager`    VARCHAR(32)  DEFAULT NULL COMMENT '拉黑审核人',
    # ERP
    `c_erp_success`       VARCHAR(2)   DEFAULT NULL COMMENT '标识 erp 是否推送成功',
    `c_erp_code`          VARCHAR(100) DEFAULT NULL COMMENT 'erpCode',
    `c_erp_id`            VARCHAR(100) DEFAULT NULL COMMENT 'erpId',
    `c_syn_state`         VARCHAR(2)   DEFAULT NULL COMMENT '供应商同步状态',
    # 审计字段
    `c_state`             VARCHAR(2)   DEFAULT NULL COMMENT '数据状态',
    `c_create_time`       BIGINT(20)   DEFAULT NULL COMMENT '创建时间',
    `c_create_man`        VARCHAR(32)  DEFAULT NULL COMMENT '创建人',
    `c_update_time`       BIGINT(20)   DEFAULT NULL COMMENT '修改时间',
    `c_update_man`        VARCHAR(32)  DEFAULT NULL COMMENT '修改人',
    primary key (`id`),
    KEY `t_supplier_in_group_fk_t_supplier` (`supplier_id`),
    CONSTRAINT `t_supplier_in_group_fk_t_supplier` FOREIGN KEY (`supplier_id`) REFERENCES `t_supplier` (`id`) ON DELETE CASCADE,
    KEY `t_supplier_in_group_fk_t_group` (`group_id`),
    CONSTRAINT `t_supplier_in_group_fk_t_group` FOREIGN KEY (`group_id`) REFERENCES `t_group` (`id`) ON DELETE CASCADE,
    KEY `t_supplier_in_group_fk_t_user` (`purchaser_id`),
    CONSTRAINT `t_supplier_in_group_fk_t_user` FOREIGN KEY (`purchaser_id`) REFERENCES `t_user` (`id`) ON DELETE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT = '组织内供应商表';


#用户权限类型表
CREATE TABLE `t_permission_type`
(
    `id`                varchar(32) NOT NULL,
    `c_permission_code` varchar(32) DEFAULT NULL COMMENT '权限编码',
    `user_id`           varchar(32) DEFAULT NULL COMMENT '用户id',
    `c_type`            varchar(1)  DEFAULT NULL COMMENT '权限类型',
    `c_state`           varchar(1)  DEFAULT NULL COMMENT '数据状态',
    `c_create_time`     bigint(20)  DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT = '用户权限表';

# 用户权限表
CREATE TABLE `t_permission_user`
(
    `id`                 varchar(32) NOT NULL,
    `permission_type_id` varchar(32) DEFAULT NULL COMMENT '用户权限类型id',
    `user_id`            varchar(32) DEFAULT NULL COMMENT '用户id',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8;

#用户分配组织部门表
CREATE TABLE `t_user_to_group`
(
    `id`            varchar(32) NOT NULL,
    `user_id`       varchar(32) DEFAULT NULL COMMENT '用户id',
    `group_id`      varchar(32) DEFAULT NULL COMMENT '组织id',
    `dept_id`       varchar(32) DEFAULT NULL COMMENT '部门id',
    `c_state`       varchar(1)  DEFAULT NULL COMMENT '数据状态',
    `c_create_time` bigint(20)  DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT = '用户分配组织部门表';

# 联系人表新加字段 组织内供应商 id
ALTER TABLE t_contact
    ADD supplier_in_group_id varchar(32) NULL COMMENT '组织内供应商 id ';

# 合同表新加字段 组织内供应商 id
ALTER TABLE t_contract
    ADD supplier_in_group_id varchar(32) NULL COMMENT '组织内供应商 id ';

# 财务信息表新加字段 组织内供应商 id
ALTER TABLE t_financial
    ADD supplier_in_group_id varchar(32) NULL COMMENT '组织内供应商 id ';

# 品牌表新加字段 组织内供应商 id
ALTER TABLE t_brand
    ADD c_relation_id varchar(32) NULL COMMENT '关联 id ';
ALTER TABLE t_brand
    ADD c_relation_type varchar(2) NULL COMMENT '关联类型';

# 联系人表新增修改时间字段
ALTER TABLE t_contact
    ADD c_update_time BIGINT(20) NULL COMMENT '修改时间';


# 审核表
CREATE TABLE `t_assess`
(

    `id`                VARCHAR(32) NOT NULL COMMENT 'id',
    `group_id`          VARCHAR(32)  DEFAULT NULL COMMENT '组织 id',
    `source_id`         VARCHAR(32)  DEFAULT NULL COMMENT '审核源数据 id',
    `target_id`         VARCHAR(32)  DEFAULT NULL COMMENT '审核目标数据 id',
    `old_target_id`     VARCHAR(32)  DEFAULT NULL COMMENT '审核目标的旧数据 id',
    `external_id`       VARCHAR(32)  DEFAULT NULL COMMENT '外部关联 id',
    `c_assess_type`     VARCHAR(2)   DEFAULT NULL COMMENT '审核类型',
    `c_assess_state`    VARCHAR(2)   DEFAULT NULL COMMENT '审核状态',
    `c_assess_result`   VARCHAR(500) DEFAULT NULL COMMENT '审核结果',
    `c_assess_man`      VARCHAR(32)  DEFAULT NULL COMMENT '审核人 id',
    `c_assess_man_name` VARCHAR(100) DEFAULT NULL COMMENT '审核人名称',
    `c_create_man`      VARCHAR(32)  DEFAULT NULL COMMENT '创建人 id',
    `c_assess_time`     BIGINT(20)   DEFAULT NULL COMMENT '审核时间',
    `c_create_time`     BIGINT(20)   DEFAULT NULL COMMENT '创建时间',
    primary key (`id`)
)
    ENGINE = InnoDB
    DEFAULT CHARSET = utf8 COMMENT = '审核表';
# 组织内供应商副本表
CREATE TABLE `t_supplier_in_group_temp`
(
    `id`                  VARCHAR(32) NOT NULL COMMENT 'id',
    `supplier_id`         VARCHAR(32)  DEFAULT NULL COMMENT '供应商 id',
    `group_id`            VARCHAR(32)  DEFAULT NULL COMMENT '组织 id',
    `purchaser_id`        VARCHAR(32)  DEFAULT NULL COMMENT '负责采购员 id',
    # 业务信息
    `c_license_url`       VARCHAR(200) DEFAULT NULL COMMENT '营业执照 - 文件路径',
    `c_details`           TEXT         DEFAULT NULL COMMENT '邮寄地址',
    `c_enterprise_nature` VARCHAR(100) DEFAULT NULL COMMENT '供应商性质',
    `c_enterprise_level`  VARCHAR(2)   DEFAULT NULL COMMENT '供应商等级',
    `c_integrity`         VARCHAR(10)  DEFAULT NULL COMMENT '资料完整度',
    # 财务信息
    `c_account_period`    VARCHAR(1)   DEFAULT NULL COMMENT '账期',
    `c_invoice_type`      VARCHAR(2)   DEFAULT NULL COMMENT '发票类型',
    `c_tax_rate`          VARCHAR(50)  DEFAULT NULL COMMENT '默认税率',
    `c_settle_currency`   VARCHAR(20)  DEFAULT NULL COMMENT '结算币别',
    primary key (`id`),
    KEY `t_supplier_in_group_temp_fk_t_supplier` (`supplier_id`),
    CONSTRAINT `t_supplier_in_group_temp_fk_t_supplier` FOREIGN KEY (`supplier_id`) REFERENCES `t_supplier` (`id`) ON DELETE CASCADE,
    KEY `t_supplier_in_group_temp_fk_t_group` (`group_id`),
    CONSTRAINT `t_supplier_in_group_temp_fk_t_group` FOREIGN KEY (`group_id`) REFERENCES `t_group` (`id`) ON DELETE CASCADE,
    KEY `t_supplier_in_group_temp_fk_t_user` (`purchaser_id`),
    CONSTRAINT `t_supplier_in_group_temp_fk_t_user` FOREIGN KEY (`purchaser_id`) REFERENCES `t_user` (`id`) ON DELETE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT = '组织内供应商副本表';


# 供应商副本表新加字段 创建人 id
ALTER TABLE t_supplier_fb
    ADD c_createMan varchar(32) NULL COMMENT '创建人 id ';


# 供应商副本表
CREATE TABLE `t_supplier_temp`
(
    `id`             VARCHAR(32) NOT NULL COMMENT 'id',
    c_enterpriseName varchar(100) DEFAULT NULL COMMENT '企业名称/个人供应商姓名',
    c_country        varchar(32)  DEFAULT NULL COMMENT '国别',
    c_province       varchar(32)  DEFAULT NULL COMMENT '省份',
    c_city           varchar(32)  DEFAULT NULL COMMENT '城市',
    c_uscc           varchar(32)  DEFAULT NULL COMMENT '统一社会信用代码',
    c_industry       varchar(100) DEFAULT NULL COMMENT '行业',
    c_supType        varchar(2)   DEFAULT NULL COMMENT '供应商类型',
    c_mobile         varchar(100) DEFAULT NULL COMMENT '联系方式',
    primary key (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT = '供应商副本表';

# 供应商副本表新加字段 创建人 id
ALTER TABLE t_order
    modify column c_type varchar(50) null COMMENT '下单平台';


# 会议表 新增 供应商名称字段
ALTER TABLE t_meeting
    ADD c_supplier_name varchar(100) NULL COMMENT '供应商名称 ';

# 会议表 新增 所属组织字段
ALTER TABLE t_meeting
    ADD group_id varchar(32) NULL COMMENT '所属组织 ';


# 用户排行表
CREATE TABLE `t_user_rank`
(
    id            varchar(32) NOT NULL COMMENT 'id',
    user_id       varchar(32) DEFAULT NULL COMMENT '用户id',
    group_id      varchar(32) DEFAULT NULL COMMENT '组织id',
    rank          int(10)     default 0 comment '排名',
    c_create_time bigint(20)  default 0 comment '创建时间',
    c_state       varchar(1) comment '状态',
    primary key (id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT = '用户排行表';


# 变更记录表
CREATE TABLE `t_change_record`
(
    `id`           VARCHAR(32) NOT NULL COMMENT 'id',
    relation_id    varchar(32)  DEFAULT NULL COMMENT '关联 id',
    c_field_code   varchar(255) DEFAULT NULL COMMENT '字段编码',
    c_old_value    TEXT         DEFAULT NULL COMMENT '旧值',
    c_new_value    TEXT         DEFAULT NULL COMMENT '新值',
    c_operate_man  varchar(32)  DEFAULT NULL COMMENT '操作人',
    c_operate_time BIGINT(20)   DEFAULT NULL COMMENT '操作时间',
    primary key (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT = '变更记录表';

ALTER TABLE t_supplier_in_group ADD c_corporate varchar(100) NULL COMMENT '法人';
ALTER TABLE t_supplier_in_group ADD c_reg_address varchar(500) NULL COMMENT '注册地址';

ALTER TABLE t_supplier_in_group_temp ADD c_corporate varchar(100) NULL COMMENT '法人';
ALTER TABLE t_supplier_in_group_temp ADD c_reg_address varchar(500) NULL COMMENT '注册地址';
