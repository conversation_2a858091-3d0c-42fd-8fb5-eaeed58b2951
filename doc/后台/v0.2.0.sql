ALTER TABLE t_field_config ADD c_big_type varchar(2) DEFAULT '1' NOT NULL COMMENT '大类。1-采购申请，2-采购订单';
ALTER TABLE t_field_config ADD c_ext_json TEXT NULL COMMENT '扩展json。目前采购订单特有';

ALTER TABLE t_template_field_config ADD c_big_type varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '1' NOT NULL COMMENT '大类。1-采购申请，2-采购订单';
ALTER TABLE t_field_config ADD c_sort INT NULL COMMENT 'group_type内排序';
-- 加长字段名长度
ALTER TABLE t_field_config MODIFY COLUMN c_name varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '字段名称';
ALTER TABLE t_field_config MODIFY COLUMN c_group_type varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '字段分组类型(1. 采购申请列表 ; 2 采购申请详情；3.物料详情 ；4 组件详情)';
ALTER TABLE t_field_config MODIFY COLUMN c_is_modify varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' NULL COMMENT '是否可修改(0：不可修改， 1：可修改)';
ALTER TABLE t_field_config MODIFY COLUMN c_is_default varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' NULL COMMENT '是否默认选中 (0：未选中， 1：选中)';
ALTER TABLE t_template_field_config MODIFY COLUMN c_text MEDIUMTEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'json 内容';



-- 物料明细新增字段
ALTER TABLE t_supplier_order_product_v2 ADD c_specification varchar(100) NULL COMMENT '规格';
ALTER TABLE t_supplier_order_product_v2 ADD c_model varchar(200) NULL COMMENT '型号';
ALTER TABLE t_supplier_order_product_v2 ADD c_item_group varchar(50) NULL COMMENT '物料组:编码+名称';
ALTER TABLE t_supplier_order_product_v2 ADD c_item_group_code varchar(10) NULL COMMENT '物料组编码';
ALTER TABLE t_supplier_order_product_v2 ADD c_profile_card varchar(100) NULL COMMENT '资料卡片:编码+名称';
ALTER TABLE t_supplier_order_product_v2 ADD c_profile_card_code varchar(20) NULL COMMENT '资料卡片编码';
ALTER TABLE t_supplier_order_product_v2 ADD c_assignment_category varchar(50) NULL COMMENT '科目分配类别:编码+名称';
ALTER TABLE t_supplier_order_product_v2 ADD c_assignment_category_code varchar(10) NULL COMMENT '科目分配类别编码';
ALTER TABLE t_supplier_order_product_v2 ADD c_ledger_subject varchar(50) NULL COMMENT '总账科目:编码+名称';
ALTER TABLE t_supplier_order_product_v2 ADD c_ledger_subject_code varchar(10) NULL COMMENT '总账科目编码';
ALTER TABLE t_supplier_order_product_v2 ADD c_cost_center varchar(50) NULL COMMENT '成本中心:编码+名称';
ALTER TABLE t_supplier_order_product_v2 ADD c_cost_center_code varchar(30) NULL COMMENT '成本中心编码';
ALTER TABLE t_supplier_order_product_v2 ADD c_order varchar(100) NULL COMMENT '订单:编码+名称';
ALTER TABLE t_supplier_order_product_v2 ADD c_order_code varchar(20) NULL COMMENT '订单编码';
ALTER TABLE t_supplier_order_product_v2 ADD c_quality_check varchar(1) NULL DEFAULT 0 COMMENT '是否质检';


--采购订单新增字段
ALTER TABLE t_supplier_order_v2 ADD c_purchase_use varchar(100) NULL COMMENT '采购用途';

ALTER TABLE `t_supplier_order_detail_v2`
  ADD COLUMN `c_order_to_form_type` varchar(2) NULL COMMENT '关联form类型';


ALTER TABLE `t_supplier_order_product_v2`
  ADD COLUMN `c_component_demand_date` bigint(20) NULL COMMENT '需求日期' AFTER `c_quality_check`,
ADD COLUMN `c_line_item_category` varchar(50) NULL COMMENT '行项目类别' AFTER `c_component_demand_date`,
ADD COLUMN `c_mrp_type` varchar(50) NULL COMMENT 'MRP类型' AFTER `c_line_item_category`,
ADD COLUMN `c_component_unit` varchar(100) NULL COMMENT '单位' AFTER `c_mrp_type`,
ADD COLUMN `c_component_unit_name` varchar(100) NULL COMMENT '单位名称' AFTER `c_component_unit`;

CREATE TABLE `t_supplier_order_sync`
(
  `id`                    varchar(32) NOT NULL COMMENT '唯一id',
  `c_supplier_order_id`   varchar(32)   DEFAULT NULL COMMENT '采购订单id',
  `c_supplier_order_code` varchar(100)  DEFAULT NULL COMMENT '采购订单号',
  `c_target`              varchar(255)  DEFAULT NULL COMMENT '目标系统 飞搭、SAP',
  `c_create_time`         bigint(20) DEFAULT NULL COMMENT '创建时间',
  `c_success_time`        bigint(20) DEFAULT NULL COMMENT '完成时间',
  `c_sync_type`           varchar(20)   DEFAULT NULL COMMENT '同步方式 手动重推、自动同步',
  `c_create_man`          varchar(32)   DEFAULT NULL COMMENT '操作人',
  `c_create_man_name`     varchar(50)   DEFAULT NULL COMMENT '操作人名称',
  `c_status`              tinyint(2) DEFAULT NULL COMMENT '同步结果',
  `c_review_status`       varchar(2)    DEFAULT NULL COMMENT '审核状态',
  `c_review_id`           varchar(32)   DEFAULT NULL COMMENT '审核id',
  `c_review_time`         bigint(20) DEFAULT NULL COMMENT '审核时间',
  `c_review_reason`       varchar(200)  DEFAULT NULL COMMENT '审核原因',
  `c_state`               varchar(1)    DEFAULT NULL COMMENT '数据状态标识',
  `c_req`                 json          DEFAULT NULL COMMENT '请求参数',
  `c_res`                 json          DEFAULT NULL COMMENT '响应参数',
  `c_url`                 varchar(1024) DEFAULT NULL COMMENT '请求url',
  `c_header`              json          DEFAULT NULL COMMENT '请求头',
  `c_type`                varchar(10)   DEFAULT NULL COMMENT '请求类型 1采购订单飞搭审核   2 sap 084',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='采购订单同步记录';


--采购部门新增Z01公司间采购组，需要开发在每个组织里写这个部门
INSERT INTO t_group (
  id,
  c_name,
  c_mdmId,
  c_erpCode,
  c_createTime,
  c_state,
  groupId,
  c_code,
  c_groupCode, -- 来自查询的 c_code
  c_type,
  c_createMan,
  c_parentIds,
  c_parent_code,
  c_parent_name,
  c_full_name, -- 根据原 c_full_name 拼接 "/公司间采购组"
  operator
)
SELECT
  UUID_SHORT() AS id, -- 使用 UUID() 生成唯一的 ID
  '公司间采购组' AS c_name,
  NULL AS c_mdmId,
  'Z01' AS c_erpCode,
  1747113736823 AS c_createTime,
  '1' AS c_state,
  NULL AS groupId,
  'Z01' AS c_code,
  t.c_code AS c_groupCode, -- 查询出的 c_code
  '2' AS c_type,
  '402881756ce75bf8016ce75e01eb0003' AS c_createMan,
  NULL AS c_parentIds,
  NULL AS c_parent_code,
  NULL AS c_parent_name,
  CASE
    WHEN t.c_full_name IS NULL OR TRIM(t.c_full_name) = '' THEN NULL
    ELSE CONCAT(t.c_full_name, '/公司间采购组')
    END AS c_full_name,
  NULL AS operator
FROM t_group t
WHERE t.c_type = '1' AND t.c_state = '1';



