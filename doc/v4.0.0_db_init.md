# 数据库初始化

## 表结构设计

## 初始化 sql

```mysql
##supplier表新增折扣比例,开通订单字段
ALTER TABLE t_supplier  ADD  c_is_open_order varchar(1) comment '是否开通订单';
ALTER TABLE t_supplier  ADD  c_rate varchar(3) comment '折扣比例';


#履约订单表
create table t_order
(
    id                  varchar(32) not null comment 'id',
    supplier_id         varchar(32) comment '供应商id',
    c_order_no          varchar(50)  comment '外部订单号',
    c_type              varchar(5)  comment '下单平台',
    c_price             DECIMAL(16,10) comment '订单金额',
    c_refund_price      DECIMAL(16,10) comment '退货金额',
    c_customer          varchar(50) comment '客户单位',
    c_consignee         varchar(10) comment '收件人',
    c_mobile            varchar(20) comment '联系方式',
    c_address           varchar(200) comment '收货地址',
    c_filing_no         varchar(30) comment '报备单号',
    c_account_no         varchar(30) comment '对账单号',
    c_order_time        bigint  default 0 comment '下单时间',
    c_order_state       varchar(1)  comment '订单状态',
    c_return_state       varchar(1)  comment '取消/退货状态',
    c_invoicing_state   varchar(1)  comment '开票状态',
    c_create_time       bigint  default 0 comment '创建时间',
    c_state             varchar(1)  comment '状态',
    primary key (id)
)
    ENGINE = InnoDB
    DEFAULT CHARSET = utf8
    COMMENT '履约订单表';

alter table t_order add constraint FK_supplier_order foreign key (supplier_id)
    references t_supplier (id) on delete restrict on update restrict;

#订单详情表
create table t_order_detail
(
    id                  varchar(32) not null comment 'id',
    order_id            varchar(32) comment '订单id',
    c_code              varchar(20)  comment '商品编码',
    c_brand             varchar(30)  comment '品牌',
    c_name              varchar(100)  comment '商品名称',
    c_model             varchar(50)  comment '型号',
    c_num               int(10) default 0 comment '数量',
    c_unit              varchar(10)  comment '单位',
    c_price             DECIMAL(16,10) comment '单价',
    c_unship_num        int(10) default 0 comment '未发数量',
    c_ship_num          int(10) default 0 comment '发货数量',
    c_return_num        int(10) default 0 comment '退货数量',
    c_create_time       bigint  default 0 comment '创建时间',
    c_state             varchar(1)  comment '状态',
    primary key (id)
)
    ENGINE = InnoDB
    DEFAULT CHARSET = utf8
    COMMENT '订单详情表';

alter table t_order_detail add constraint FK_order_orderDetail foreign key (order_id)
    references t_order (id) on delete restrict on update restrict;

#订单发货表
create table t_order_delivery
(
    id                  varchar(32) not null comment 'id',
    order_id            varchar(32) comment '订单id',
    c_delivery_no       varchar(50)  comment '发货单号',
    c_express_company   varchar(20)  comment '物流公司',
    c_express_no        varchar(50)  comment '物流单号',
    c_express_code      varchar(10)  comment '物流公司编码',
    c_express_track     text comment '物流信息',
    c_delivery_state    varchar(1)  comment '发货状态',
    c_create_time       bigint  default 0 comment '创建时间',
    c_state             varchar(1)  comment '状态',
    primary key (id)
)
    ENGINE = InnoDB
    DEFAULT CHARSET = utf8
    COMMENT '订单发货表';

alter table t_order_delivery add constraint FK_order_orderDelivery foreign key (order_id)
    references t_order (id) on delete restrict on update restrict;

#订单发货详情表
create table t_order_delivery_detail
(
    id                  varchar(32) not null comment 'id',
    delivery_id         varchar(32) comment '发货单id',
    c_code              varchar(20)  comment '商品编码',
    c_brand             varchar(30)  comment '品牌',
    c_name              varchar(100)  comment '商品名称',
    c_price             DECIMAL(16,10) comment '单价',
    c_model             varchar(50)  comment '型号',
    c_num               int(10) default 0 comment '数量',
    c_unit              varchar(10)  comment '单位',
    c_ship_num          int(10) default 0 comment '发货数量',
    c_create_time       bigint  default 0 comment '创建时间',
    c_state             varchar(1)  comment '状态',
    primary key (id)
)
    ENGINE = InnoDB
    DEFAULT CHARSET = utf8
    COMMENT '订单发货详情表';

alter table t_order_delivery_detail add constraint FK_orderDelivery_orderDeliveryDetail foreign key (delivery_id)
    references t_order_delivery (id) on delete restrict on update restrict;

#订单报备单
create table t_order_filing
(
    id                  varchar(32) not null comment 'id',
    c_filing_no         varchar(50) comment '报备单号',
    c_num               int(10) default 0 comment '报备数量',
    c_price             DECIMAL(16,10) comment '报备金额',
    c_type              varchar(5)  comment '下单平台',
    c_customer          varchar(50) comment '客户单位',
    c_filing_time       bigint  default 0 comment '报备时间',
    c_arrive_time       bigint  default 0 comment '报备到期日',
    c_order_time        bigint  default 0 comment '预计点单日',
    c_filing_state      varchar(1)  comment '报备单状态',
    c_remark            varchar(500) comment '备注',
    c_reason            varchar(500) comment '退回原因',
    c_supplier_id       varchar(32) comment '供应商id',
    c_create_time       bigint  default 0 comment '创建时间',
    c_state             varchar(1)  comment '状态',
    primary key (id)
)
    ENGINE = InnoDB
    DEFAULT CHARSET = utf8
    COMMENT '订单报备单';

#订单报备详情表
create table t_order_filing_detail
(
    id                  varchar(32) not null comment 'id',
    filing_id           varchar(32) comment '报备单id',
    c_code              varchar(20)  comment '商品编码',
    c_brand             varchar(30)  comment '品牌',
    c_name              varchar(100)  comment '商品名称',
    c_model             varchar(50)  comment '型号',
    c_num               int(10) default 0 comment '数量',
    c_price             DECIMAL(16,10) comment '点单价',
    c_create_time       bigint  default 0 comment '创建时间',
    c_state             varchar(1)  comment '状态',
    primary key (id)
)
    ENGINE = InnoDB
    DEFAULT CHARSET = utf8
    COMMENT '订单报备详情表';

alter table t_order_filing_detail add constraint FK_orderFiling_orderFilingDetail foreign key (filing_id)
    references t_order_filing (id) on delete restrict on update restrict;


#订单退货表
create table t_order_return
(
    id                  varchar(32) not null comment 'id',
    order_id            varchar(32) comment '订单id',
    c_return_no         varchar(50) comment '退货单号',
    c_num               int(10) default 0 comment '退货数量',
    c_price             DECIMAL(16,10) comment '退货金额',
    c_apply_man         varchar(10)  comment '申请人',
    c_mobile            varchar(20) comment '联系方式',
    c_apply_time        bigint  default 0 comment '申请时间',
    c_complete_time     bigint  default 0 comment '完成时间',
    c_type              varchar(1) comment '取消/退货',
    c_return_state      varchar(1)  comment '退货单状态',
    c_reject_reason     varchar(500) comment '拒绝原因',
    c_reason            varchar(500) comment '退货/取消原因',
    c_supplier_id       varchar(32) comment '供应商id',
    c_oms_return_id     varchar(32) comment '履约退货单id',
    c_create_time       bigint  default 0 comment '创建时间',
    c_state             varchar(1)  comment '状态',
    primary key (id)
)
    ENGINE = InnoDB
    DEFAULT CHARSET = utf8
    COMMENT '订单退货表';

alter table t_order_return add constraint FK_order_orderReturn foreign key (order_id)
    references t_order (id) on delete restrict on update restrict;

#订单退货详情表
create table t_order_return_detail
(
    id                  varchar(32) not null comment 'id',
    return_id           varchar(32) comment '退货单id',
    c_order_id          varchar(32) comment '订单id',
    c_code              varchar(20)  comment '商品编码',
    c_brand             varchar(30)  comment '品牌',
    c_name              varchar(100)  comment '商品名称',
    c_model             varchar(50)  comment '型号',
    c_unit              varchar(10)  comment '单位',
    c_price             DECIMAL(16,10) comment '单价',
    c_num               int(10) default 0 comment '数量',
    c_return_num        int(10) default 0 comment '退货数量',
    c_create_time       bigint  default 0 comment '创建时间',
    c_state             varchar(1)  comment '状态',
    primary key (id)
)
    ENGINE = InnoDB
    DEFAULT CHARSET = utf8
    COMMENT '订单退货详情表';

alter table t_order_return_detail add constraint FK_orderReturn_orderReturnDetail foreign key (return_id)
    references t_order_return (id) on delete restrict on update restrict;


#订单对账表
create table t_order_account
(
    id                  varchar(32) not null comment 'id',
    c_account_no        varchar(50) comment '对账单号',
    c_price             DECIMAL(16,10) comment '对账金额',
    c_return_price      DECIMAL(16,10) comment '已回金额',
    c_create_supplier   varchar(50)  comment '申请人',
    c_commit_time       bigint  default 0 comment '提交时间',
    c_account_state     varchar(1)  comment '对账单状态',
    c_supplier_id       varchar(32) comment '供应商id',
    c_create_time       bigint  default 0 comment '创建时间',
    c_state             varchar(1)  comment '状态',
    primary key (id)
)
    ENGINE = InnoDB
    DEFAULT CHARSET = utf8
    COMMENT '订单对账表';

#对账订单明细表
create table t_order_account_detail
(
    id                  varchar(32) not null comment 'id',
    account_id         varchar(32) comment '所属对账单id',
    c_order_no          varchar(50)  comment '外部订单号',
    c_type              varchar(5)  comment '下单平台',
    c_price             DECIMAL(16,10) comment '最终结算金额',
    c_customer          varchar(50) comment '客户单位',
    c_consignee         varchar(10) comment '收件人',
    c_mobile            varchar(20) comment '联系方式',
    c_order_time        bigint  default 0 comment '下单时间',
    c_create_time       bigint  default 0 comment '创建时间',
    c_state             varchar(1)  comment '状态',
    primary key (id)
)
    ENGINE = InnoDB
    DEFAULT CHARSET = utf8
    COMMENT '对账订单明细表';

alter table t_order_account_detail add constraint FK_orderAccount_orderAccountDetail foreign key (account_id)
    references t_order_account (id) on delete restrict on update restrict;


#对账商品明细表
create table t_order_account_product_detail
(
    id                  varchar(32) not null comment 'id',
    account_id          varchar(32) comment '所属对账单id',
    c_code              varchar(20)  comment '商品编码',
    c_brand             varchar(30)  comment '品牌',
    c_name              varchar(100)  comment '商品名称',
    c_model             varchar(50)  comment '型号',
    c_unit              varchar(10)  comment '单位',
    c_num               int(10) default 0 comment '数量',
    c_price             DECIMAL(16,10) comment '单价',
    c_order_no          varchar(20)  comment '外部订单号',
    c_create_time       bigint  default 0 comment '创建时间',
    c_account_detail_id varchar(32) comment '对账单订单详情id',
    c_state             varchar(1)  comment '状态',
    primary key (id)
)
    ENGINE = InnoDB
    DEFAULT CHARSET = utf8
    COMMENT '对账商品明细表';

alter table t_order_account_product_detail add constraint FK_orderAccount_orderAccountProductDetail
    foreign key (account_id) references t_order_account (id) on delete restrict on update restrict;

#对账回款明细表
create table t_order_account_return_detail
(
    id                  varchar(32) not null comment 'id',
    account_id          varchar(32) comment '所属对账单id',
    c_remark            varchar(500)  comment '备注',
    c_file_urls         varchar(500)  comment '凭证地址',
    c_is_upload         varchar(1)  comment '是否上传凭证',
    c_price             DECIMAL(16,10) comment '回款金额',
    c_return_state      varchar(1)  comment '回款状态',
    c_return_time       bigint  default 0 comment '回款时间',
    c_create_time       bigint  default 0 comment '创建时间',
    c_state             varchar(1)  comment '状态',
    c_oms_return_id     varchar(32) comment 'oms回款单id',
    primary key (id)
)
    ENGINE = InnoDB
    DEFAULT CHARSET = utf8
    COMMENT '对账回款明细表';

alter table t_order_account_return_detail add constraint FK_orderAccount_orderAccountReturnDetail
    foreign key (account_id) references t_order_account (id) on delete restrict on update restrict;




-- srm_zhens.t_order_account_invoice definition

CREATE TABLE `t_order_account_invoice` (
   `id` varchar(32) NOT NULL COMMENT '主键',
   `c_invoice_num` varchar(50) DEFAULT NULL COMMENT '发票号',
   `c_invoice_code` varchar(100) DEFAULT NULL COMMENT '发票代码',
   `c_invoice_time` bigint(20) DEFAULT NULL COMMENT '开票日期',
   `c_price` decimal(20,10) DEFAULT NULL COMMENT '含税金额',
   `c_logistics_company` varchar(20) DEFAULT NULL COMMENT '物流公司',
   `c_logistics_num` varchar(50) DEFAULT NULL COMMENT '物流单号',
   `c_create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
   `order_account_id` varchar(32) DEFAULT NULL COMMENT '对账 id',
   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='对账单发票信息';








```

