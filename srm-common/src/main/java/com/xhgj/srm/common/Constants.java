package com.xhgj.srm.common;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.map.MapUtil;
import com.google.common.collect.ImmutableMap;
import com.xhgj.srm.common.enums.PayTypeSAPEnums;
import com.xhgj.srm.common.enums.purchase.order.PurchaseApplyOperationPermissionsEnum;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

public class Constants extends com.xhiot.boot.core.common.constants.Constants {
  /** 性别 - 映射表 */
  public static final Map<String, String> SEX_MAP = new TreeMap<>();

  /** 采购单默认税率-百分 */
  public static final String PURCHASE_TAX_RATE = "13";

  /** 采购单默认税率-小数 */
  public static final String PURCHASE_TAX_RATE_DECIMAL = "0.13";

  public static final String PASSWORDSHAPRE = "xhiot";
  public static final String USERNAME = "admin";
  public static final String ADMIN_REAL_NAME = "管理员";
  public static final String CREATECODE = "1025";
  // 返回code
  public static final String CODE_ERROR = "0";
  public static final String CODE_OK = "1";

  // 返回code version-2
  public static final String RESPONSE_CODE_ERROR = "1";
  public static final String RESPONSE_CODE_OK = "0";
  public static final TreeMap<String, String> STATE_MAP = new TreeMap<>();
  public static final String STATE_DELETE = "0";
  public static final String STATE_OK = "1";
  public static final String STATE_NO = "0";
  public static final String STATE_LOCKED = "2";
  public static final String SESSION_USER_ID = "SESSION_USER_ID";
  public static final String SESSION_USER_OBJ = "SESSION_USER_OBJ";
  public static final TreeMap<String, String> YESORNO = new TreeMap<String, String>();
  public static final String YES = "1";
  public static final String NO = "2";
  public static final TreeMap<String, String> ROLEMAP = new TreeMap<String, String>();
  public static final String ROLEMAP_GENERALMANAGER = "3";

  /** * 角色 **** */
  public static final String ROLE_ADMINISTRATOR = "4"; // 后台管理员

  public static final TreeMap<String, String> COMMONSTATE = new TreeMap<String, String>();
  public static final String COMMONSTATE_DELETE = "0"; // 删除
  public static final String COMMONSTATE_OK = "1"; // 正常
  public static final String COMMONSTATE_LOCK = "2"; // 停用
  public static final String COMMONSTATE_CHECKING = "3"; // 审核中
  public static final String COMMONSTATE_BLACKLIST = "4"; // 黑名单
  public static final String COMMONSTATE_TEMPORARYSTORAGE = "5"; // 暂存

  /** 数据状态与 ERP 操作映射表 */
  public static final Map<String, String> STATE_TO_ERP_OPERATION_TYPE_MAP = new HashMap<>(3);



  /** ************** 公共上传附件 ************************ */
  // 附件类型
  public static final String FILE_TYPE_PICTURE = "1"; // 图片

  public static final String FILE_TYPE_WORD = "2"; // 文本（word，ppt，excel）
  public static final String FILE_TYPE_VOICE = "3"; // 音频
  public static final String FILE_TYPE_RARZIP = "4"; // 压缩包
  public static final String FILE_TYPE_OTHER = "5"; // 其他
  public static final String FILE_TYPE_VIDEO = "6"; // 视频
  // 图片类型
  public static final String FILE_TYPE_SCXK = "1"; // 生产许可
  public static final String FILE_TYPE_ZLZS = "2"; // 专利证书
  public static final String FILE_TYPE_SPZCZS = "3"; // 商品注册证书
  public static final String FILE_TYPE_CPSYBG = "4"; // 产品试验报告
  public static final String FILE_TYPE_DSFJCBG = "5"; // 第三方检测报告
  public static final String FILE_TYPE_ISOZLRZTX = "6"; // ISO质量认证体系
  public static final String FILE_TYPE_ISO14001HJGLTX = "7"; // ISO14001环境管理体系
  public static final String FILE_TYPE_OHSAS18001ZYJKAQTX = "8"; // OHSAS18001职业健康安全管理体系
  public static final String FILE_TYPE_CMSZLGLTXRZZS = "9"; // CMS测量管理体系认证证书

  public static final String FILE_TYPE_CGXY = "10"; // 采购协议（供应商等级为战略合作/一般合作时-合作协议）--- 同时包含廉政协议
  /**
   * @deprecated 此类型已废弃，不再使用
   */
  public static final String FILE_TYPE_LZXY = "11"; // 廉政协议（供应商等级为项目合作时-合作协议）
  public static final String FILE_TYPE_HTFJ = "12"; // 合同附件
  public static final String FILE_TYPE_DLZS = "13"; // 代理证书
  public static final String FILE_TYPE_BCXY = "14"; // 补充协议

  /** 经营许可 */
  public static final String FILE_TYPE_JYXK = "15";

  /** 品牌授权 */
  public static final String FILE_TYPE_PPSQ = "16";

  /** 开户许可 */
  public static final String FILE_TYPE_KHXK = "17";

  /** 宣传资料 */
  public static final String FILE_TYPE_XCZL = "19";

  /** 检测报告 */
  public static final String FILE_TYPE_JCBG = "20";

  /** 主图 */
  public static final String FILE_TYPE_ZT = "21";

  /** 详情图 */
  public static final String FILE_TYPE_XQ = "22";

  /** 验收单 */
  public static final String FILE_TYPE_YANSHOU = "23";

  /** 供应商发票 */
  public static final String FILE_TYPE_INVOICE = "24";

  /** 两单 */
  public static final String FILE_TYPE_TWO_ORDERS = "25";

  /** 落地商订单发票 */
  public static final String FILE_TYPE_ODER_INVOICE = "26";

  /** 落地商其它信息中的附件 */
  public static final String FILE_TYPE_ODER_OTHER_FILE = "27";

  /** 落地商合同附件 */
  public static final String FILE_TYPE_LANDING_CONTRACT = "28";

  /** 落地商合同补充协议 */
  public static final String FILE_TYPE_LANDING_CONTRACT_SUPPLEMENT = "29";

  /** 物料 - 质量证明 */
  public static final String FILE_TYPE_CERTIFICATE_OF_QUALITY = "30";

  /** 履约订单客户回款手动确认凭证 */
  public static final String FILE_TYPE_CUSTOMER_RETURN_SIGN = "31";

  /** 准入报备单落地商营业执照 */
  public static final String FILE_TYPE_LANDING_MERCHANT_LICENSE = "32";

  /** 准入报备单落地商身份证照片 */
  public static final String FILE_TYPE_LANDING_MERCHANT_ID_CARD_PHOTO = "33";

  /** 准入报备单落地商产品资质书 */
  public static final String FILE_TYPE_LANDING_MERCHANT_PRODUCT_QUALIFICATION = "34";

  /** 客户资料附件 */
  public static final String FILE_TYPE_CUSTOMER_PROFILE_FILE = "35";

  /** 发货上传附件 */
  public static final String FILE_TYPE_DELIVERY = "36";

  /**
   * 落地商采购合同附件
   */
  public static final String FILE_TYPE_LANDING_PURCHASE_CONTRACT = "37";

  /**
   * 订单履约 客户信息附件
   */
  public static final String FILE_TYPE_ORDER_CUSTOMER_INFO = "38";

  /**
   * 落地商订单说明附件
   */
  public static final String FILE_TYPE_ORDER_DESCRIPTION_INFO = "39";
  /**
   * 准入报备单-合作信息-补充附件
   */
  public static final String FILE_TYPE_REGISTRATION_ORDER_SUPPLEMENT = "44";

  /**
   * 供应商评估表
   */
  public static final String FILE_TYPE_EVALUATE = "40";
  /**
   * 供应商 - 品牌通用授权
   */
  public static final String FILE_TYPE_BRAND_AUTHORIZATION = "41";
  /**
   * 供应商 - 品牌代理授权
   */
  public static final String FILE_TYPE_BRAND_AGENT_AUTHORIZATION = "42";

  /**
   * 供应商 - 招商注册证书
   */
  public static final String FILE_TYPE_BRAND_TRADEMARK = "43";

  /** 图片类型-对应名称 */
  public static final TreeMap<String, String> FILE_TYPE_TO_NAME = new TreeMap<String, String>();

  /** 供应商协议附件类型 */
  public static final List<String> SUPPLIER_AGREEMENT_FILE_TYPE_LIST =
      Arrays.asList(FILE_TYPE_CGXY, FILE_TYPE_LZXY);

  /** 上传资质证照/合作协议类型 */
  public static final List<String> SUPPLIER_FILE_TYPE_TYPE_LIST =
      Arrays.asList(
          FILE_TYPE_CGXY,
          FILE_TYPE_LZXY,
          FILE_TYPE_SCXK,
          FILE_TYPE_CMSZLGLTXRZZS,
          FILE_TYPE_OHSAS18001ZYJKAQTX,
          FILE_TYPE_ISO14001HJGLTX,
          FILE_TYPE_DSFJCBG,
          FILE_TYPE_CPSYBG,
          FILE_TYPE_JYXK);

  // 接收状态
  public static final TreeMap<String, String> RECEIVETYPE_TYPEMAP = new TreeMap<String, String>();
  public static final String RECEIVETYPE_TYPEMAP_WAIT = "1"; // 未接收
  public static final String RECEIVETYPE_TYPEMAP_DONE = "2"; // 已接收
  public static final TreeMap<String, String> SMSMODEL = new TreeMap<String, String>();
  public static final String SMSMODEL_REGISTER = "1"; // 注册短信
  public static final String SMSMODEL_FINDPWD = "2"; // 找回密码
  // 平台
  public static final TreeMap<String, String> PLATFORM_TYPE = new TreeMap<String, String>();
  public static final String PLATFORM_TYPE_BEFORE = "1"; // 前台
  public static final String PLATFORM_TYPE_AFTER = "2"; // 后台
  public static final String PLATFORM_TYPE_OPEN = "3"; // 开发下游供应商
  public static final String LOGINCODE_LOGIN = "1"; // 登入
  public static final String LOGINCODE_LOGOUT = "2"; // 登出
  // 邮箱信息
  public static final String MAILNAME = "<EMAIL>";
  public static final String MAILPASSWORD = "K7938EnFFsvQNuvD";
  // 登录描述Description
  public static final String LOG_DESCRIPTION_AFTER_LOGIN = "后台管理系统登录";
  public static final String LOG_DESCRIPTION_AFTER_LOGOUT = "后台管理系统登出";
  public static final String LOG_DESCRIPTION_WEB_LOGIN = "前台管理系统登录";
  public static final String LOG_DESCRIPTION_WEB_LOGOUT = "前台管理系统登出";

  public static final String LOG_DESCRIPTION_OPEN_LOGIN = "服务商开发接口登录";
  // 审核状态
  public static final TreeMap<String, String> AUDIT_STATE = new TreeMap<String, String>();
  public static final String AUDIT_STATE_PURCHASEIN = "1"; // 采购审核中
  public static final String AUDIT_STATE_PURCHASEFALUT = "2"; // 采购审核未通过
  public static final String AUDIT_STATE_MANAGEIN = "3"; // 经理审核中
  public static final String AUDIT_STATE_MANAGEFAULT = "4"; // 经理审核未通过
  public static final String AUDIT_STATE_MANAGESUCCESS = "5"; // 审核已通过
  public static final String AUDIT_STATE_CANCEL = "6"; // 撤回中
  // 审核类型
  public static final TreeMap<String, String> AUDIT_TYPE = new TreeMap<String, String>();
  public static final String AUDIT_TYPE_PURCHASESUCCESS = "1"; // 采购审核通过
  public static final String AUDIT_TYPE_PURCHASEFAULT = "2"; // 采购审核未通过
  public static final String AUDIT_TYPE_MANAGESUCCESS = "3"; // 经理审核通过
  public static final String AUDIT_TYPE_MANAGEFAULT = "4"; // 经理审核未通过
  public static final String AUDIT_TYPE_AGREESUCCESS = "5"; // 协议审核通过
  public static final String AUDIT_TYPE_AGREEFAULT = "6"; // 协议审核未通过
  // 申请状态
  public static final TreeMap<String, String> APPLY_STATE = new TreeMap<String, String>();
  public static final String APPLY_STATE_WAIT = "1"; // 待申请
  public static final String APPLY_STATE_ING = "2"; // 申请中
  public static final String APPLY_STATE_FAULT = "3"; // 申请失败
  public static final String APPLY_STATE_SUCCESS = "4"; // 申请通过
  // 申请类型
  public static final TreeMap<String, String> APPLY_TYPE = new TreeMap<String, String>();
  public static final String APPLY_TYPE_FAULT = "1"; // 申请失败
  public static final String APPLY_TYPE_SUCCESS = "2"; // 申请通过

  public static final String APPLY_SAP_SUCCESS = "S"; // 申请通过

  public static final String APPLY_SAP_ERROR = "E"; // 申请通过
  // 来源类型
  public static final TreeMap<String, String> RESOURCE_TYPE = new TreeMap<String, String>();
  public static final String RESOURCE_TYPE_REGISTER = "1"; // 注册
  public static final String RESOURCE_TYPE_EDIT = "2"; // 修改
  public static final String RESOURCE_TYPE_INPUT = "3"; // 录入
  public static final String RESOURCE_TYPE_AGREEMENT = "4"; // 协议（合作协议）
  public static final String RESOURCE_TYPE_SPADD = "5"; // 商品新增
  public static final String RESOURCE_TYPE_SPERR = "6"; // 商品纠错
  public static final String RESOURCE_TYPE_AGREEMENT_ZZZZ = "7"; // 资质证照
  public static final String RESOURCE_TYPE_SHIELD = "8"; // 拉黑
  // 协议审核状态
  public static final TreeMap<String, String> AGREECHECK_STATE = new TreeMap<String, String>();
  public static final String AGREECHECK_STATE_NONE = "1"; // 未上传
  public static final String AGREECHECK_STATE_MANAGEIN = "2"; // 审核中
  public static final String AGREECHECK_STATE_MANAGEFAULT = "3"; // 审核未通过
  public static final String AGREECHECK_STATE_MANAGESUCCESS = "4"; // 审核已通过
  public static final String AGREECHECK_STATE_TEMP = "5"; // 暂存
  // 协议类型
  public static final TreeMap<String, String> AGREERESOURCE_TYPE = new TreeMap<>();
  public static final String AGREERESOURCE_TYPE_ADD = "1"; // 初次上传
  public static final String AGREERESOURCE_TYPE_EDIT = "2"; // 协议修改
  // 供应商副本添加来源
  public static final TreeMap<String, String> FBSOURCE_TYPE = new TreeMap<>();
  public static final String FBSOURCE_TYPE_BASE = "1"; // 基本信息审核
  public static final String FBSOURCE_TYPE_AGREEMENT = "2"; // 协议审核
  // 供应商副本协议添加来源
  public static final TreeMap<String, String> FBAGREESOURCE = new TreeMap<>();
  public static final String FBAGREESOURCE_SUPPLIER = "1"; // 供应商
  public static final String FBAGREESOURCE_PURCHASE = "2"; // 采购
  // 申请来源类型
  public static final TreeMap<String, String> APPLYSOURCE = new TreeMap<String, String>();
  public static final String APPLYSOURCE_SUPPLIER = "1"; // 供应商
  public static final String APPLYSOURCE_AGREEMENT = "2"; // 协议
  public static final String APPLYSOURCE_PRODUCT = "3"; // 商品纠错

  // 企业性质
  public static final TreeMap<String, String> ENTERPRISENATURE = new TreeMap<String, String>();
  public static final String ENTERPRISENATURE_SC = "1"; // 生产
  public static final String ENTERPRISENATURE_FU = "2"; // 服务
  public static final String ENTERPRISENATURE_SM = "3"; // 商贸
  // 图片来源
  public static final TreeMap<String, String> PICTURESOURCE = new TreeMap<String, String>();
  public static final String PICTURESOURCE_SRM = "1"; // 本地
  public static final String PICTURESOURCE_MDM = "2"; // mdm
  // 供应商类型
  public static final TreeMap<String, String> SUPPLIERTYPE = new TreeMap<String, String>();

  /** 国内供应商 */
  public static final String SUPPLIERTYPE_CHINA = "1"; // 国内

  /** 国内供应商 */
  public static final String SUPPLIERTYPE_CHINA_CN = "CHINA"; // 国内

  public static final String SUPPLIERTYPE_ABROAD = "2"; //
  public static final String SUPPLIERTYPE_ABROAD_CN = "ABROAD"; //

  /** 个人供应商 */
  public static final String SUPPLIERTYPE_PERSONAL = "3";

  public static final String SUPPLIERTYPE_PERSONAL_CN = "PERSONAL";

  /** 内部供应商 */
  public static final String SUPPLIER_TYPE_INTERNAL = "4";

  /** 一次性(临时)供应商 */
  public static final String SUPPLIER_TYPE_PROVISIONAL = "5";

  // 发票类型
  public static final TreeMap<String, String> INVOICETYPE = new TreeMap<String, String>();

  /** 增值税专用发票 */
  public static final String INVOICETYPE_VAT = "1";

  /** 普通发票 */
  public static final String INVOICETYPE_NORMAL = "2";

  /** 币别 - 映射表 */
  public static final TreeMap<String, String> SETTLE_CURRENCY_MAP = new TreeMap<>();

  /** 默认税率 - 映射表 */
  public static final TreeMap<String, String> TAX_RATE_MAP = new TreeMap<>();

  /** 品牌经营形式-品牌商 */
  public static final String BRAND_MANAGE_TYPE_BRAND = "1";

  /** 品牌经营形式-集货商 */
  public static final String BRAND_MANAGE_TYPE_STORE = "2";

  /** 品牌经营形式-名称对应 */
  public static final TreeMap<String, String> BRAND_MANAGE_TYPE_TO_NAME =
      new TreeMap<String, String>();

  /** 品牌授权类型-品牌授权 */
  public static final String BRAND_AUTHORIZATION_TYPE_BRAND = "1";

  /** 品牌授权类型-公司授权 */
  public static final String BRAND_AUTHORIZATION_TYPE_COMPANY = "2";

  /** 品牌经营形式-名称对应 */
  public static final TreeMap<String, String> BRAND_AUTHORIZATION_TYPE_TO_NAME =
      new TreeMap<String, String>();

  /** 上传状态-已上传 */
  public static final String UPLOAD_STATUS_UPLOADED = "1";

  /** 上传状态-未上传 */
  public static final String UPLOAD_STATUS_NOT_UPLOADED = "2";

  /** 品牌经营形式-名称对应 */
  public static final TreeMap<String, String> UPLOAD_STATUS_TO_NAME = new TreeMap<String, String>();

  /** 供应商变更记录审核状态-通过 */
  public static final String SUPPLIER_CHANGE_RECODE_SUCCESS = "1";

  /** 供应商变更记录审核状态-待审核 */
  public static final String SUPPLIER_CHANGE_RECODE_CHECK = "2";

  /** 供应商变更记录审核状态-驳回 */
  public static final String SUPPLIER_CHANGE_RECODE_REJECT = "3";

  /** 供应商变更记录审核状态-正常 */
  public static final String SUPPLIER_CHANGE_RECODE_OK = "4";

  /** 供应商变更记录审核状态-名称对应 */
  public static final TreeMap<String, String> SUPPLIER_CHANGE_RECODE_TO_NAME =
      new TreeMap<String, String>();

  /** 账号角色-管理员 */
  public static final String SUPPLIER_USER_ROLE_ADMIN = "1";

  /** 账号角色-采购 */
  public static final String SUPPLIER_USER_ROLE_ORDINARY = "2";

  /** 账号角色-财务 */
  public static final String USER_ROLE_FINANCE = "3";

  /** 账号角色-业务员 */
  public static final String SUPPLIER_USER_ROLE_SALESMAN = "5";

  /** 账号角色-电商供应商管理员 */
  public static final String SUPPLIER_USER_ROLE_COMMERCE_ADMIN = "6";

  /** 账号角色-名称对应 */
  public static final TreeMap<String, String> SUPPLIER_USER_ROLE_TO_NAME =
      new TreeMap<String, String>();

  /** 前台默认密码 */
  public static final String SUPPLIER_USER_PASSWORD = "XHGJ2021";

  // 群组类型
  public static final TreeMap<String, String> GROUPTYPE_MAP = new TreeMap<>();
  public static final String GROUPTYPE_MAP_ORGANIZATION = "1";
  public static final String GROUPTYPE_MAP_DEPT = "2";
  // 群组类型
  public static final TreeMap<String, String> GROUP_TYPE_PARAM_MAP = new TreeMap<>();
  public static final TreeMap<String, String> GROUP_TYPE_SQL_PARAM_MAP = new TreeMap<>();
  // 供应商同步状态
  public static final TreeMap<String, String> SUPPLIER_SYN_STATE = new TreeMap<>();
  public static final String SUPPLIER_SYN_STATE_NOXE = "1"; // 协议未上传
  public static final String SUPPLIER_SYN_STATE_NOERPCODE = "2"; // 缺失ERP编码
  public static final String SUPPLIER_SYN_STATE_FALSE = "3"; // 同步失败
  public static final String SUPPLIER_SYN_STATE_SUCCESS = "4"; // 同步成功
  public static final String SUPPLIER_SYN_STATE_TEMP = "5"; // 暂存

  /** 用户审核关系类型 */
  public static final TreeMap<String, String> USERCHECKTYPE_MAP = new TreeMap<>();

  /** 审核关系 - 新增 */
  public static final String USERCHECKTYPE_ADD = "1";

  /** 审核关系 - 修改 */
  public static final String USERCHECKTYPE_UPDATE = "2";

  /** 审核关系 - 拉黑 */
  public static final String USERCHECKTYPE_SHIELD = "3";

  /** 审核关系 - 修改供应商等级 */
  public static final String USERCHECKTYPE_SUPPLIER_LEVEL = "4";

  // 供应商审核类型
  public static final TreeMap<String, String> SUPPLIERCHECKTYPE_MAP = new TreeMap<>();
  public static final String SUPPLIERCHECKTYPE_MAP_ADD = "1";
  public static final String SUPPLIERCHECKTYPE_MAP_UPDATE = "2";
  public static final String SUPPLIERCHECKTYPE_MAP_SHIELD = "3";
  public static final String SUPPLIERCHECKTYPE_MAP_LEVEL = "4";
  // 商品审核类型
  public static final TreeMap<String, String> PRODUCTCHECKTYPE_MAP = new TreeMap<>();
  public static final String PRODUCTCHECKTYPE_MAP_ADD = "1";
  public static final String PRODUCTCHECKTYPE_MAP_UPDATE = "2";
  public static final TreeMap<String, String> SEARCH_TYPE = new TreeMap<String, String>();

  /** 检索方案 - 缺省 */
  public static final String SEARCH_TYPE_DEFAULT = "all";

  /** 合同 */
  public static final String SEARCH_TYPE_CONTRACT = "1";

  /** 供应商 */
  @Deprecated public static final String SEARCH_TYPE_SUPPLIER = "2";

  /** 询价 */
  public static final String SEARCH_TYPE_INQUIRY = "3";

  /** 供应商审核记录(前台) */
  public static final String SEARCH_TYPE_SUPPLIER_CHECK_RECORD = "4";

  /** 供应商账户 */
  public static final String SEARCH_TYPE_SUPPLIER_USER = "5";

  /** 国际供应商 */
  @Deprecated public static final String SEARCH_TYPE_SUPPLIER_ABOARD = "6";

  /** 黑名单供应商 */
  public static final String SEARCH_TYPE_SUPPLIER_BLACK = "7";

  /** 我的申请 */
  public static final String SEARCH_TYPE_SUPPLIER_MY = "8";

  /** 待我审核 */
  public static final String SEARCH_TYPE_SUPPLIER_WAIT = "9";

  /** 审核记录 */
  public static final String SEARCH_TYPE_SUPPLIER_CHECKED = "10";

  /** 会议 */
  public static final String SEARCH_TYPE_MEETING = "11";

  /** 用户 */
  public static final String SEARCH_TYPE_USER = "12";

  /** 部门 */
  public static final String SEARCH_TYPE_DEPART = "13";

  /** 组织 */
  public static final String SEARCH_TYPE_ORG = "14";

  /** 供应商待审核(前台) */
  public static final String SEARCH_TYPE_SUPPLIER_FRONT = "15";

  /** 品牌(前台) */
  public static final String SEARCH_TYPE_SUPPLIER_BRAND = "16";

  /** 品牌授权(前台) */
  public static final String SEARCH_TYPE_SUPPLIER_BRAND_AUTHORIZATION = "17";

  /** 物料列表(前台) */
  public static final String SEARCH_TYPE_SUPPLIER_PRODUCT_LIST = "18";

  /** 批量新增 */
  public static final String SEARCH_TYPE_SUPPLIER_BATCH_ADD = "19";

  /** 供应商订单 */
  public static final String SEARCH_TYPE_SUPPLIER_ORDER = "20";

  /** 履约订单 */
  public static final String SEARCH_TYPE_SUPPLIER_ORDERLIST = "21";

  /** 报备单 */
  public static final String SEARCH_TYPE_SUPPLIER_FILING = "22";

  /** 对账单 */
  public static final String SEARCH_TYPE_SUPPLIER_ACCOUNT = "23";

  /** 供应商模板 */
  public static final String SEARCH_TYPE_SUPPLIER_TEMP = "24";

  /** 国内供应商查询方案 */
  public static final String SEARCH_TYPE_SUPPLIER_CHINA = "25";

  /** 国外供应商查询方案 */
  public static final String SEARCH_TYPE_SUPPLIER_ABROAD = "26";

  /** 个人供应商查询方案 */
  public static final String SEARCH_TYPE_SUPPLIER_PERSON = "27";

  /** 履约订单列表 */
  public static final String SEARCH_TYPE_ERP_ORDER_LIST = "28";

  /** 履约退货列表 */
  public static final String SEARCH_TYPE_ERP_ORDER_RETURN_LIST = "29";

  /** 分页获取取消单 */
  public static final String SEARCH_TYPE_GET_RETURN_OR_CANCEL_PAGE = "30";

  /** 分页获取供应商订单 */
  public static final String SEARCH_TYPE_GET_PAGE_SUPPLIER_ORDER_PAGE = "31";

  /** 分页消息中心 */
  public static final String SEARCH_TYPE_NOTICE_CENTER = "32";

  /** 分页查询可对账列表 */
  public static final String SEARCH_TYPE_ACCOUNT_PAGE = "33";

  /** 落地商订单开票列表 */
  public static final String SEARCH_TYPE_ORDER_INVOICE = "34";

  /** 可付款订单列表 */
  public static final String SEARCH_TYPE_ORDER_ALLOW_PAYMENT = "35";

  /** 付款单列表 */
  public static final String SEARCH_TYPE_ORDER_PAYMENT = "36";

  /** 客户回款单列表 */
  public static final String SEARCH_TYPE_CUSTOMER_RECEIVABLE = "37";

  /** 落地商合同列表 */
  public static final String SEARCH_TYPE_CONTRACT_PAGE = "38";

  /** 待审核物料列表 */
  public static final String SEARCH_TYPE_CHECK_PENDING_PRODUCT_PAGE = "39";

  /** 订单和供应商开票关联表 */
  public static final String SEARCH_TYPE_SUPPLIER_INVOICE_RELATION_PAGE = "40";

  /** 进项票列表 */
  public static final String SEARCH_TYPE_CHECK_ORDER_INVOICE_PAGE = "41";

  /** 物料库存列表(前台) */
  public static final String SEARCH_TYPE_SUPPLIER_PRODUCT_STOCK_LIST = "46";

  /** 物料调价列表(前台) */
  public static final String SEARCH_TYPE_SUPPLIER_PRODUCT_PRICE_LIST = "47";

  /** 分页查询供应商可开票列表 */
  public static final String SEARCH_TYPE_SUPPLIER_ACCOUNT_PAGE = "48";

  /** 分页查询采购订单申请列表 */
  public static final String SEARCH_TYPE_PURCHASE_APPLY_FOR_ORDER_PAGE = "49";

  /** 分页查询内部供应商列表 */
  public static final String SEARCH_TYPE_SUPPLIER_INTERIOR_PAGE = "50";

  /** 分页查询一次性供应商列表 */
  public static final String SEARCH_TYPE_SUPPLIER_PROVISIONAL_PAGE = "51";

  /** 财务凭证列表 */
  public static final String SEARCH_TYPE_FINANCIAL_VOUCHER_PAGE = "52";

  /** 付款申请列表 */
  public static final String SEARCH_TYPE_PAYMENT_APPLICATION_PAGE = "53";

  /** 查询方案 - 采购价格库 */
  public static final String SEARCH_TYPE_PURCHASE_INFO_RECORD = "54";

  /** 查询方案 - 入住报备单分页列表*/
  public static final String CHECK_IN_REPORT_FORM = "55";

  /** 查询方案 - 入库单分页列表*/
  public static final String GO_DOWN_ENTRY_PAGE = "56";

  /** 查询方案 - 退库单分页列表*/
  public static final String CANCELLATION_FORM_PAGE = "57";

  /**
   * 品牌申请(前台)
   */
  public static final String SEARCH_TYPE_BRAND_APPLY = "58";

  /**
   * 物料管理 我的申请
   */
  public static final String SEARCH_TYPE_PRODUCT_APPLY = "59";

  /**
   * 退换货订单列表查询
   */
  public static final String SEARCH_TYPE_RETURN_EXCHANGE_ORDER = "60";

  /**
   * 库存管理
   */
  public static final String SEARCH_TYPE_INVENTORY = "61";

  /**
   * 库存安全管理查询
   */
  public static final String SEARCH_TYPE_INVENTORY_SAFE = "62";

  /**
   * 调拨单列表查询
   */
  public static final String SEARCH_TYPE_TRANSFER_ORDER = "63";

  /**
   * 组装拆卸单列表查询
   */
  public static final String SEARCH_TYPE_ASSEMBLY_DISASSEMBLY_ORDER = "64";

  /**
   * 查询方案-需付款订单列表
   */
  public static final String ORDER_NEED_PAYMENT_PAGE = "65";

  /** 分页查询采购订单申请列表V2 */
  public static final String SEARCH_TYPE_PURCHASE_APPLY_FOR_ORDER_PAGE_V2 = "66";

  /** 查询方案 - 入库单分页列表V2*/
  public static final String GO_DOWN_ENTRY_PAGE_V2 = "67";

  /** 查询方案 - 退库单分页列表V2**/
  public static final String CANCELLATION_FORM_PAGE_V2 = "68";


  // 商品上传图片分类
  public static final String PICTURE_TYPE_ZT = "1"; // 主图
  public static final String PICTURE_TYPE_XQ = "2"; // 详情
  // 供应商经营类型
  public static final TreeMap<String, String> SUPPLIER_MANAGETYPE_MAP =
      new TreeMap<String, String>();
  public static final String SUPPLIER_MANAGETYPE_CX = "1"; // 存续
  public static final String SUPPLIER_MANAGETYPE_ZY = "2"; // 在业
  public static final String SUPPLIER_MANAGETYPE_DX = "3"; // 吊销
  public static final String SUPPLIER_MANAGETYPE_ZX = "4"; // 注销
  public static final String SUPPLIER_MANAGETYPE_QR = "5"; // 迁入
  public static final String SUPPLIER_MANAGETYPE_QC = "6"; // 迁出
  public static final String SUPPLIER_MANAGETYPE_TY = "7"; // 停业
  public static final String SUPPLIER_MANAGETYPE_QS = "8"; // 清算

  /** 登录失败次数 */
  public static final int LOGIN_NUMBER = 3;

  /** 任务状态 */
  public static final TreeMap<String, String> MISSION_STATE_MAP = new TreeMap<>();
  public static final String MISSION_STATE_DEL = "0";
  public static final String MISSION_STATE_SUCCESS = "1"; // 成功
  public static final String MISSION_STATE_FAIL = "2"; // 失败
  public static final String MISSION_STATE_ING = "3"; // 进行中

  /** 用户授权 */
  public static final String USER_ALLOWTOSYSTEM_UN = "0"; // 未授权

  public static final String USER_ALLOWTOSYSTEM_OK = "1"; // 已授权

  /** 任务类型 */
  public static final String MISSION_TYPE_INQUIRY_SAVE = "询价导入";

  /** 电子商务公司erp编码 */
  public static final String OWNER_DZSW_CODE = "20503";

  /** 万聚编码 */
  public static final String GROUP_WANJU_CODE = "1025";
  /**
   * 总部编码
   */
  public static final String HEADQUARTERS_CODE = "1000";

  public static final String GROUP_WANJU_ID = "2c90e85f7aecb67e017aecfd5efe2886";

  /** 货期详询客服 */
  public static final String DELIVERY_CUSTOMER_SERVICE = "详询客服";

  public static final String INTERFACE_REQUEST_FAILED = "接口请求失败";

  /** 正常供应商最低分数界限 */
  public static final int NORMAL_SUPPLIER_SCORE = 89;

  /** 用户数据权限类型 */
  public static final TreeMap<String, String> USER_PERMISSIONS_DATA_TYPE = new TreeMap<>();

  /** 用户数据权限类型-合同 */
  public static final String USER_PERMISSION_CONTRACT = "1";

  /** 用户数据权限类型-询价 */
  public static final String USER_PERMISSION_INQUIRY = "2";

  /** 用户数据权限类型-拉黑合同 */
  public static final String USER_PERMISSION_BLOCK_SUPPLIER = "3";

  /** 用户数据权限类型-修改合同 */
  public static final String USER_PERMISSION_UPDATE_SUPPLIER = "4";

  /** 用户数据权限类型-供应商订单 */
  public static final String USER_PERMISSION_SUPPLIER_ORDER = "5";

  /** 用户数据权限类型-采购申请 */
  public static final String USER_PERMISSION_PURCHASE_APPLY = "6";

  /** 用户数据权限类型-退换单列表 */
  public static final String USER_PERMISSION_RETURN_EXCHANGE_ORDER = "60";

  /** 用户操作类型-导入价格库 */
  public static final String USER_PERMISSION_IMPORT_PRICE_LIBRARY = "7";

  /** 用户操作类型-采购价格库列表 */
  public static final String USER_PERMISSION_PRICE_LIBRARY = "8";

  /** 用户操作类型-进项票 */
  public static final String USER_PERMISSION_INVOICE = "9";

  /** 用户操作类型-采购申请单的采购员 */
  public static final String USER_PERMISSION_PURCHASE_APPLY_ORDER_PURCHASER = "10";

  /** 用户操作类型-导出采购申请列表 */
  public static final String USER_PERMISSION_EXPORT_APPLY_FOR_ORDER = "11";

  /** 用户操作类型-导出采购订单列表 */
  public static final String USER_PERMISSION_EXPORT_SUPPLIER_ORDER = "12";

  /** 用户操作类型-供应商列表修改负责人*/
  public static final String USER_PERMISSION_UPDATE_LEADER = "13";

  /** 用户操作类型-财务凭证列表导出权限*/
  public static final String USER_PERMISSION_EXPORT_FINANCIAL_VOUCHER = "14";

  /** 用户操作类型-付款申请列表导出权限*/
  public static final String USER_PERMISSION_EXPORT_PAYMENT_APPLY = "15";
  /** 用户操作类型-入库/退库单导出权限*/
  public static final String USER_PERMISSION_EXPORT_WAREHOUSE_RETURN = "16";

  /**
   *  用户操作类型-导出进项票
   *  6.7.0版本需求该点取消
   */
  public static final String USER_PERMISSION_EXPORT_INPUT_INVOICE = "17";
  /**
   *用户操作类型-库存列表导出
   */
  public static final String USER_PERMISSION_EXPORT_INVENTORY = "18";
  /**
   *用户操作类型-退换货订单列表导出
   */
  public static final String USER_PERMISSION_EXPORT_RETURN_EXCHANGE = "19";

  /** 用户操作类型-库存安全列表*/
  public static final String USER_PERMISSION_INVENTORY_SAFETY = "20";


  /** 用户操作类型-查看权限内全部组织订单*/
  public static final String USER_PERMISSION_VIEW_ALL_ORGANIZATION= "21";
  /**
   * 用户操作类型-组装拆卸单导出
   */
  public static final String USER_PERMISSION_EXPORT_ASSEMBLY_DISASSEMBLY = "22";

  /** 任务状态 */
  public static final TreeMap<String, String> DATA_PERMISSIONS_TYPE = new TreeMap<>();

  /** 拥有自身权限 */
  public static final String DATA_PERMISSIONS_TYPE_OWM = "1";

  /** 拥有部门权限 */
  public static final String DATA_PERMISSIONS_TYPE_DEPT = "2";

  /** 拥有部门权限及子权限 */
  public static final String DATA_PERMISSIONS_TYPE_DEPT_CHILDREN = "3";

  /** 拥有组织权限 */
  public static final String DATA_PERMISSIONS_TYPE_ORGANIZATION = "4";
  /** 仅允许导出本部门*/
  public static final String EXPORT_DEPT_TYPE = "2";

  /** 拥有自身权限 */
  public static final String DATA_PERMISSIONS_TYPE_OWM_STR = "为负责人";
  public static final String DATA_PERMISSIONS_TYPE_INQUIRY_STR = "为询价人";

  /** 拥有自身权限 */
  public static final String DATA_PERMISSIONS_TYPE_OWM_PURCHASE_STR = "为采购员";
  /**
   * 为创建人
   */
  public static final String DATA_PERMISSIONS_TYPE_CREATOR_STR = "为创建人";
  /** 为采购员 key*/
  public static final String EXPORT_OWM_PURCHASE_TYPE = "3";

  /** 拥有自身权限 */
  public static final String DATA_PERMISSIONS_TYPE_OWM_SUBMIT_STR = "为提交人或应处理人";

  /** 拥有部门权限 */
  public static final String DATA_PERMISSIONS_TYPE_DEPT_STR = "所在部门";

  /** 拥有部门权限及子权限 */
  public static final String DATA_PERMISSIONS_TYPE_DEPT_CHILDREN_STR = "所在部门及下级部门";

  /** 拥有组织权限 */
  public static final String DATA_PERMISSIONS_TYPE_ORGANIZATION_STR = "所在组织";

  /** 允许导入 */
  public static final String ALLOW_IMPORT_STR = "允许导入";

  /** 不允许导入 */
  public static final String NOT_ALLOW_IMPORT_STR = "不允许导入";

  /** 允许导出 */
  public static final String ALLOW_EXPORT_STR = "允许导出";
  public static final String ALLOW_EXPORT_KEY = "1";

  /** 不允许导出 */
  public static final String NOT_EXPORT_IMPORT_STR = "不允许导出";
  /**
   * 不允许操作
   */
  public static final String NOT_ALLOW_OPERATION = "不允许操作";
  public static final String NOT_EXPORT_IMPORT_KEY = "0";
  /** 仅仅允许导出本部门 */
  public static final String NOT_EXPORT_IMPORT_DEPT_STR = "仅允许导出本部门";

  /** 允许修改 */
  public static final String ALLOW_UPDATE_STR = "允许修改";
  /**
   * 允许操作
   */
  public static final String ALLOW_OPERATION = "允许操作";

  /** 不允许修改 */
  public static final String NOT_ALLOW_UPDATE_STR = "不允许修改";
  /** 导出全部 */
  public static final String EXPORT_ALL_STR = "导出全部";

  public static final Map<String,String> PERMISSIONS_STR_MAP = MapUtil.<String,String>builder(
      )
      .put(DATA_PERMISSIONS_TYPE_OWM_STR,"1")
      .put(DATA_PERMISSIONS_TYPE_OWM_PURCHASE_STR,"1")
      .put(DATA_PERMISSIONS_TYPE_OWM_SUBMIT_STR,"1")
      .put(DATA_PERMISSIONS_TYPE_DEPT_STR,"2")
      .put(DATA_PERMISSIONS_TYPE_DEPT_CHILDREN_STR,"3")
      .put(DATA_PERMISSIONS_TYPE_ORGANIZATION_STR,"4")
      .put(ALLOW_IMPORT_STR,"1")
      .put(NOT_ALLOW_IMPORT_STR,"0")
      .put(ALLOW_EXPORT_STR,"1")
      .put(NOT_EXPORT_IMPORT_STR,"0")
      .put(NOT_EXPORT_IMPORT_DEPT_STR,"2")
      .put(PurchaseApplyOperationPermissionsEnum.NOT_ALLOW.getDescription(),"5")
      .put(ALLOW_UPDATE_STR,"1")
      .put(NOT_ALLOW_UPDATE_STR,"0")
      .put(DATA_PERMISSIONS_TYPE_INQUIRY_STR,"1")
      .build();

  public static final Map<String,String> DEPT_MAP =MapUtil.<String,String>builder(
      )
      .put(DATA_PERMISSIONS_TYPE_DEPT,DATA_PERMISSIONS_TYPE_DEPT_STR)
      .put(DATA_PERMISSIONS_TYPE_DEPT_CHILDREN,DATA_PERMISSIONS_TYPE_DEPT_CHILDREN_STR)
      .put(DATA_PERMISSIONS_TYPE_ORGANIZATION,DATA_PERMISSIONS_TYPE_ORGANIZATION_STR)
      .build();
  public static final Map<String,String> OWM_MAP =MapUtil.<String,String>builder(
      )
      .put(DATA_PERMISSIONS_TYPE_OWM,DATA_PERMISSIONS_TYPE_OWM_STR)
      .putAll(DEPT_MAP)
      .build();

  public static final Map<String,String> INQUIRY_MAP =MapUtil.<String,String>builder(
      )
      .put(DATA_PERMISSIONS_TYPE_OWM,DATA_PERMISSIONS_TYPE_INQUIRY_STR)
      .putAll(DEPT_MAP)
      .build();

  public static final Map<String,String> PURCHASE_MAP =MapUtil.<String,String>builder(
      )
      .put(DATA_PERMISSIONS_TYPE_OWM,DATA_PERMISSIONS_TYPE_OWM_PURCHASE_STR)
      .put(DATA_PERMISSIONS_TYPE_ORGANIZATION,DATA_PERMISSIONS_TYPE_ORGANIZATION_STR)
      .build();

  public static final Map<String,String> SUBMIT_MAP =MapUtil.<String,String>builder(
      )
      .put(DATA_PERMISSIONS_TYPE_OWM,DATA_PERMISSIONS_TYPE_OWM_SUBMIT_STR)
      .putAll(DEPT_MAP)
      .build();

  public static final Map<String,String> IMPORT_EXPORT_MAP =MapUtil.<String,String>builder(
      )
      .put("1",ALLOW_IMPORT_STR)
      .put("0",NOT_ALLOW_IMPORT_STR)
      .build();

  public static final Map<String,String> UPDATE_LEADER_MAP =MapUtil.<String,String>builder(
      )
      .put("1",ALLOW_UPDATE_STR)
      .put("0",NOT_ALLOW_UPDATE_STR)
      .build();

  public static final Map<String,String> EXPORT_MAP =MapUtil.<String,String>builder(
      )
      .put(NOT_EXPORT_IMPORT_KEY,NOT_EXPORT_IMPORT_STR)
      .put(ALLOW_EXPORT_KEY,ALLOW_EXPORT_STR)
      .put(EXPORT_DEPT_TYPE,NOT_EXPORT_IMPORT_DEPT_STR)
      .put(EXPORT_OWM_PURCHASE_TYPE,DATA_PERMISSIONS_TYPE_OWM_PURCHASE_STR)
      .build();

  public static final Map<String,String> EXPORT_MAP_NAME_TO_KEY = MapUtil.<String,String>builder(
      )
      .put(NOT_EXPORT_IMPORT_STR,NOT_EXPORT_IMPORT_KEY)
      .put(ALLOW_EXPORT_STR,ALLOW_EXPORT_KEY)
      .put(NOT_EXPORT_IMPORT_DEPT_STR,EXPORT_DEPT_TYPE)
      .put(DATA_PERMISSIONS_TYPE_OWM_PURCHASE_STR,EXPORT_OWM_PURCHASE_TYPE)
      .put(DATA_PERMISSIONS_TYPE_DEPT_STR,EXPORT_DEPT_TYPE)
      .put(EXPORT_ALL_STR,ALLOW_EXPORT_KEY)
      .put(DATA_PERMISSIONS_TYPE_CREATOR_STR,EXPORT_OWM_PURCHASE_TYPE)
      .put(NOT_ALLOW_OPERATION,NOT_EXPORT_IMPORT_KEY)
      .put(ALLOW_OPERATION,ALLOW_EXPORT_KEY)
      .put(DATA_PERMISSIONS_TYPE_CREATOR_STR,EXPORT_OWM_PURCHASE_TYPE)
      .build();
  public static final Map<String,String> EXPORT_WAREHOUSE_RETURN_MAP = MapUtil.<String,String>builder(
  )
      .put(NOT_EXPORT_IMPORT_KEY,NOT_EXPORT_IMPORT_STR)
      .put(ALLOW_EXPORT_KEY,EXPORT_ALL_STR)
      .put(EXPORT_DEPT_TYPE,DATA_PERMISSIONS_TYPE_DEPT_STR)
      .put(EXPORT_OWM_PURCHASE_TYPE,DATA_PERMISSIONS_TYPE_OWM_PURCHASE_STR)
      .build();

  public static final Map<String,String> EXPORT_INVENTORY_SAFETY_MAP =
      MapUtil.<String,String>builder(
      )
      .put(NOT_EXPORT_IMPORT_KEY,NOT_ALLOW_OPERATION)
      .put(ALLOW_EXPORT_KEY,ALLOW_OPERATION)
      .build();

  public static final Map<String,String> EXPORT_ASSEMBLE_MAP = MapUtil.<String,String>builder(
      )
      .put(NOT_EXPORT_IMPORT_KEY,NOT_EXPORT_IMPORT_STR)
      .put(ALLOW_EXPORT_KEY,EXPORT_ALL_STR)
      .put(EXPORT_DEPT_TYPE,DATA_PERMISSIONS_TYPE_DEPT_STR)
      .put(EXPORT_OWM_PURCHASE_TYPE,DATA_PERMISSIONS_TYPE_CREATOR_STR)
      .build();

  public static final Map<String,String> PURCHASE_ORDER_MAP =MapUtil.<String,String>builder(
      )
      .put(DATA_PERMISSIONS_TYPE_OWM,DATA_PERMISSIONS_TYPE_OWM_PURCHASE_STR)
      .putAll(DEPT_MAP)
      .put(PurchaseApplyOperationPermissionsEnum.NOT_ALLOW.getKey(),
          PurchaseApplyOperationPermissionsEnum.NOT_ALLOW.getDescription())
      .build();



  /** 落地商账期 */
  @Deprecated
  public static final Map<String, String> LANDING_COMMERCIAL_ACCOUNT_PERIOD = new HashMap<>();

  /** 背靠背 */
  @Deprecated public static final String LANDING_COMMERCIAL_ACCOUNT_PERIOD_BACK_TO_BACK = "1";

  /** 周结 */
  @Deprecated public static final String LANDING_COMMERCIAL_ACCOUNT_PERIOD_EVERY_WEEK = "2";

  /** 半月结 */
  @Deprecated public static final String LANDING_COMMERCIAL_ACCOUNT_PERIOD_EVERY_HALF_MONTH = "3";

  /** 月结 */
  @Deprecated public static final String LANDING_COMMERCIAL_ACCOUNT_PERIOD_MONTHLY = "4";

  /** 季结 */
  @Deprecated public static final String LANDING_COMMERCIAL_ACCOUNT_PERIOD_EVERY_SEASON = "5";

  /** 半年结 */
  @Deprecated public static final String LANDING_COMMERCIAL_ACCOUNT_PERIOD_SEMIANNUALLY = "6";

  /** 年结 */
  @Deprecated public static final String LANDING_COMMERCIAL_ACCOUNT_PERIOD_EVERY_YEAR = "7";

  /** 账期 */
  public static final Map<String, String> ACCOUNT_PERIOD = new TreeMap<>();

  /** 账期 - 款到发货 */
  public static final String ACCOUNT_PERIOD_PAY_FIRST = "1";

  /** 账期 - 月结 */
  public static final String ACCOUNT_PERIOD_MONTH = "2";

  /** 账期 - 季结 */
  public static final String ACCOUNT_PERIOD_QUARTER = "3";

  /** 账期 - 半年结 */
  public static final String ACCOUNT_PERIOD_HALF_YEAR = "4";

  /** 账期 - 年结 */
  public static final String ACCOUNT_PERIOD_YEAR = "5";

  /** 账期 - 背靠背 */
  public static final String ACCOUNT_PERIOD_BACK_TO_BACK = "6";

  /** 账期 - 立即应付 */
  public static final String ACCOUNT_PERIOD_IMMEDIATELY = "0001";

  /** 账期 - 30天 */
  public static final String ACCOUNT_PERIOD_THIRTY = "0009";

  /** 7天之内 */
  public static final String WITHIN_7_DAYS = "0002";

  /** 15天之内 */
  public static final String WITHIN_15_DAYS = "0003";

  /** 45天之内 */
  public static final String WITHIN_45_DAYS = "0004";

  /** 60天之内 */
  public static final String WITHIN_60_DAYS = "0005";

  /** 90天之内 */
  public static final String WITHIN_90_DAYS = "0006";

  /** 180天之内 */
  public static final String WITHIN_180_DAYS = "0007";

  /** 360天之内 */
  public static final String WITHIN_360_DAYS = "0008";

  /** 退货单编号前缀 */
  public static final String TH_PREFIX = "TH";

  /** 取消单编号前缀 */
  public static final String QX_PREFIX = "QX";

  /** 钉钉机器人卡片消息类型 */
  public static final Map<Integer, String> DING_TALK_ROBOT_CAR_MSG_TYPE = new HashMap<>();

  /** 纸质合同 */
  public static final String PAPER_CONTRACT = "1";

  /** 电子合同 */
  public static final String ELECTRonIC_CONTRACT = "2";

  /** 落地商合同签订方式 */
  public static final Map<String, String> CONTRACT_SIGNING_MODE = new HashMap<>();

  /** 合同类型 - 框架合同 */
  public static final String FRAME_CONTRACT = "1";

  /** 合同类型 - 普通合同 */
  public static final String ORDINARY_CONTRACT = "2";

  /** 合同类型 */
  public static final Map<String, String> TYPE_OF_CONTRACT = new HashMap<>();

  @Deprecated
  /** 付款比例 100% （弃用，已改为存具体比例） */
  public static final String PAYMENT_RATIO_TYPE_100 = "1";

  public static final Map<String, String> PAYMENT_RATIO_TYPE = new HashMap<>();

  /**
   * 合作类型 - 无
   */
  public static final String COOPERATE_TYPE_NO = "0";

  /** 合作类型 - 供应商 */
  public static final String COOPERATE_TYPE_SUPPLIER = "1";

  /** 合作类型 - 落地商 */
  public static final String COOPERATE_TYPE_LANDER = "2";

  /** 合作类型 - 全部 */
  public static final String COOPERATE_TYPE_ALL = "3";

  /** 税率 */
  public static final String TAX_RATE_J0 = "J0";

  public static final String TAX_RATE_J1 = "J1";
  public static final String TAX_RATE_J2 = "J2";
  public static final String TAX_RATE_J3 = "J3";
  public static final String TAX_RATE_J4 = "J4";
  public static final String TAX_RATE_J5 = "J5";
  public static final String TAX_RATE_J6 = "J6";
  public static final String TAX_RATE_J7 = "J7";
  public static final String TAX_RATE_J8 = "J8";
  public static final String TAX_RATE_J9 = "J9";
  public static final String TAX_RATE_JA = "JA";

  public static final Map<String, BigDecimal> TAX_RATE_TYPE =
      new HashMap<String, BigDecimal>() {
        {
          put(TAX_RATE_J0, BigDecimal.ZERO);
          put(TAX_RATE_J1, new BigDecimal("0.17"));
          put(TAX_RATE_J2, new BigDecimal("0.16"));
          put(TAX_RATE_J3, new BigDecimal("0.13"));
          put(TAX_RATE_J4, new BigDecimal("0.12"));
          put(TAX_RATE_J5, new BigDecimal("0.11"));
          put(TAX_RATE_J6, new BigDecimal("0.10"));
          put(TAX_RATE_J7, new BigDecimal("0.09"));
          put(TAX_RATE_J8, new BigDecimal("0.06"));
          put(TAX_RATE_J9, new BigDecimal("0.03"));
          put(TAX_RATE_JA, new BigDecimal("0.01"));
        }
      };
  public static final Map<BigDecimal, String> TAX_RATE_TYPE_NUM =
      new HashMap<BigDecimal, String>() {
        {
          put(BigDecimal.ZERO, TAX_RATE_J0);
          put(new BigDecimal("0.17"), TAX_RATE_J1);
          put(new BigDecimal("0.16"), TAX_RATE_J2);
          put(new BigDecimal("0.13"), TAX_RATE_J3);
          put(new BigDecimal("0.12"), TAX_RATE_J4);
          put(new BigDecimal("0.11"), TAX_RATE_J5);
          put(new BigDecimal("0.10"), TAX_RATE_J6);
          put(new BigDecimal("0.09"), TAX_RATE_J7);
          put(new BigDecimal("0.06"), TAX_RATE_J8);
          put(new BigDecimal("0.03"), TAX_RATE_J9);
          put(new BigDecimal("0.01"), TAX_RATE_JA);
        }
      };
  public static final List<String> COOPERATE_TYPE_LIST =
      ListUtil.toList(COOPERATE_TYPE_SUPPLIER, COOPERATE_TYPE_LANDER, COOPERATE_TYPE_ALL);

  /** 供应商默认类目 */
  public static final String PRODUCT_LANDER_CATEGORY = "GH03";

  /** 默认大票组织编码 */
  public static final List<String> DEFAULT_BIG_TICKET_ORGANIZATION_CODE =
      ListUtil.toList(
          "10101",
          "10102",
          "1020101",
          "1020102",
          "*********",
          "*********",
          "1020104",
          "1020201",
          "1020202",
          "*********",
          "10202030201",
          "1020301",
          "1020302",
          "1020303",
          "10204",
          "1020501",
          "1020502",
          "1020601",
          "1020602",
          "1020603",
          "1020701",
          "1020702",
          "1020801",
          "*********",
          "*********",
          "1020901",
          "1020902",
          "1021001",
          "10211",
          "103010101",
          "103010401",
          "103010601",
          "1030201",
          "1030302",
          "10304",
          "10502",
          "10503",
          "10508",
          "10509",
          "106010101",
          "1060102",
          "1060103",
          "10602",
          "1070201");

  /** 过滤内部客户名称 */
  public static final List<String> EXCLUDE_INSIDE_CUSTOMER_NAME_LIST =
      ListUtil.toList(
          "咸亨国际科技股份有限公司",
          "北京咸亨国际通用设备有限公司",
          "内蒙古咸亨国际通用设备有限公司",
          "武汉咸亨国际轨道交通设备有限公司",
          "咸亨国际（宁波）安全科技有限公司",
          "咸亨国际轨道交通设备（北京）有限公司",
          "济南英伦电气有限公司",
          "上海咸亨国际通用设备有限公司",
          "安徽咸亨国际通用设备有限公司",
          "聚智国际（杭州）能源设备有限公司",
          "杭州中科天维科技有限公司",
          "中科光绘（上海）科技有限公司",
          "广州咸亨国际通用设备有限公司",
          "广州咸亨电气设备有限公司",
          "江西福瑞尔电气有限公司",
          "江苏咸亨电气设备有限公司",
          "江苏咸亨国际科技发展有限公司",
          "沈阳咸亨科技有限公司",
          "成都咸亨电气有限公司",
          "重庆咸亨通用设备有限公司",
          "郑州咸亨国际通用设备有限公司",
          "西安咸亨国际通用设备有限公司",
          "绍兴咸亨电力设备有限公司",
          "武汉咸亨国际通用设备有限公司",
          "新疆咸亨国际通用设备有限公司",
          "乌鲁木齐万聚高科通用设备有限公司",
          "杭州咸亨国际精测科技有限公司",
          "杭州贝特设备制造有限公司",
          "简固机电设备（上海）有限公司",
          "绍兴简固机械设备制造有限公司",
          "贝特（杭州）工业机械有限公司",
          "杭州咸亨建筑装饰设计工程有限公司",
          "浙江万疆兴驰专用车辆有限公司",
          "浙江贝工设备制造有限公司",
          "咸亨国际（杭州）电气制造有限公司",
          "海宁市欧敬莱电气有限公司",
          "探博士电气技术（杭州）有限公司",
          "浙江咸亨创新产业中心有限公司",
          "杭州艾普莱标识制造有限公司",
          "杭州咸亨国际科研中心有限公司",
          "万聚国际（杭州）供应链有限公司",
          "万聚国际（杭州）工具有限公司",
          "汇聚国际（杭州）高科设备有限公司",
          "杭州咸亨国际应急救援装备有限公司",
          "杭州咸亨国际应急科技有限公司",
          "咸亨国际应急科技研究院（北京）有限公司",
          "咸亨国际电子商务有限公司",
          "咸亨国际（杭州）文化传媒有限公司",
          "杭州市下城区咸亨国际应急装备中心",
          "长沙咸亨赛孚科技有限公司",
          "咸亨国际（杭州）院前救护研究中心有限公司",
          "咸亨国际（杭州）电气科技研究院有限公司",
          "杭州咸亨国际计量中心有限公司",
          "杭州咸亨校准检测技术有限公司",
          "北京咸亨新能源科技有限公司",
          "咸亨国际（杭州）航空技术研究院有限公司",
          "咸亨国际（杭州）航空自动化有限公司",
          "上海戈宝实业有限公司",
          "TI Electric GmbH",
          "武汉咸亨国际能源科技有限公司",
          "武汉咸亨国际轨道科技有限公司",
          "杭州咸亨国际应急发展有限公司",
          "武汉咸亨赛孚实业有限公司",
          "咸亨国际赛孚（杭州）实业有限公司",
          "杭州市咸亨电力职业技能培训学校",
          "武汉咸亨国际电气有限公司",
          "嘉兴咸亨设备制造有限公司",
          "杭州咸亨国际精测科技有限公司（旧）",
          "兰州咸亨国际科技有限公司",
          "XP咸亨国际（杭州）电气科技研究院有限公司",
          "XP咸亨国际（杭州）航空自动化有限公司",
          "XP咸亨国际（杭州）电气制造有限公司",
          "XP探博士电气技术（杭州）有限公司",
          "XP浙江万疆兴驰专用车辆有限公司",
          "XP杭州咸亨国际科研中心有限公司",
          "XP杭州贝特设备制造有限公司",
          "XP贝特（杭州）工业机械有限公司",
          "XP杭州艾普莱标识制造有限公司",
          "XP杭州咸亨国际计量中心有限公司",
          "XP杭州咸亨校准检测技术有限公司",
          "探博士电气科技（杭州）有限公司",
          "安护电力技术（杭州）有限公司",
          "长沙亨特科技有限公司",
          "浙江浙创中和防爆科技有限公司",
          "咸亨电气技术（杭州）有限公司",
          "咸亨国际应急科技研究院（北京）有限公司（代开）",
          "贝特（杭州）工业机械有限公司海宁分公司",
          "DP咸亨国际电子商务有限公司",
          "DP咸亨国际科技股份有限公司");

  /** 过滤内部客户编码 */
  public static final List<String> EXCLUDE_INSIDE_CUSTOMER_CODE_LIST =
      ListUtil.toList(
          "XH00001",
          "XH00002",
          "XH00003",
          "XH00004",
          "XH00005",
          "XH00006",
          "XH00007",
          "XH00008",
          "XH00009",
          "XH00010",
          "XH00011",
          "XH00012",
          "XH00013",
          "XH00014",
          "XH00015",
          "XH00016",
          "XH00017",
          "XH00018",
          "XH00019",
          "XH00020",
          "XH00021",
          "XH00022",
          "XH00023",
          "XH00024",
          "XH00025",
          "XH00026",
          "XH00027",
          "XH00028",
          "XH00029",
          "XH00030",
          "XH00031",
          "XH00032",
          "XH00033",
          "XH00034",
          "XH00035",
          "XH00036",
          "XH00037",
          "XH00038",
          "XH00039",
          "XH00040",
          "XH00041",
          "XH00042",
          "XH00043",
          "XH00044",
          "XH00045",
          "XH00046",
          "XH00047",
          "XH00048",
          "XH00049",
          "XH00050",
          "XH00051",
          "XH00052",
          "XH00053",
          "XH00054",
          "XH00055",
          "XH00056",
          "XH00057",
          "XH00058",
          "XH00059",
          "XH00060",
          "XH00061",
          "XH00062",
          "XH00063",
          "XH00064",
          "XH00065",
          "XH00180",
          "XH00192",
          "XH00196",
          "XH00197",
          "XH00198",
          "XH00199",
          "XH00200",
          "XH00201",
          "XH00202",
          "XH00203",
          "XH00204",
          "XH00205",
          "XH00206",
          "XH00207",
          "XH00208",
          "XH00209",
          "XH00210",
          "XH00211",
          "XH00212",
          "XH00213",
          "XH00214",
          "XH00215",
          "DPXHNB00001",
          "DPXHNB00002");

  /** 默认 ERP 承运商编码 */
  public static final String DEFAULT_LOGISTICS = "00003";

  /** ERP 参数 是否开红票 - 是 */
  public static final String ERP_OPEN_RED_INVOICE_TYPE_YES = "01";

  /** ERP 参数 是否开红票 - 否 */
  public static final String ERP_OPEN_RED_INVOICE_TYPE_NO = "02";

  /** 履约信息状态 - 生效 */
  public static final String SUPPLIER_PERFORMANCE_STATUS_EFFECT = "1";

  /** 履约信息状态 - 关闭 */
  public static final String SUPPLIER_PERFORMANCE_STATUS_CLOSE = "2";

  /** 履约信息状态 */
  public static final Map<String, String> SUPPLIER_PERFORMANCE_STATUS_MAP = new TreeMap<>();

  /** 履约信息合作模式 - 服务商 */
  public static final String SUPPLIER_PERFORMANCE_COOPERATION_TYPE_SERVICE = "1";

  /** 履约信息合作模式 - 报备商 */
  public static final String SUPPLIER_PERFORMANCE_COOPERATION_TYPE_REPORTER = "2";

  /** 履约信息状态 */
  public static final Map<String, String> SUPPLIER_PERFORMANCE_COOPERATION_TYPE_MAP =
      new TreeMap<>();

  /** 合同来源类型 - SRM新增 */
  public static final String CONTRACT_SOURCE_SRM = "1";

  /** 合同来源类型 */
  public static final Map<String, String> CONTRACT_SOURCE_TYPE = new HashMap<>();

  /** 合同签章状态 - 已签章 */
  public static final String SIGNATURE_STATUS_YES = "1";

  /** 合同签章状态 - 未签章 */
  public static final String SIGNATURE_STATUS_NO = "2";

  /** 落地商合同签章状态 */
  public static final Map<String, String> SIGNATURE_STATUS_TYPE = new HashMap<>();

  /** 合作类型 - 落地商 */
  public static final String TYPE_OF_COOPERATION = "落地商";

  /** 落地商合同关联履约信息的状态 - 已关联 */
  public static final String CONTRACT_ASSOCIATION_PERFORMANCE_STATUS_YES = "1";

  /** 落地商合同关联履约信息的状态 - 未关联 */
  public static final String CONTRACT_ASSOCIATION_PERFORMANCE_STATUS_NO = "2";

  /** 物料是否关联主图 - 已关联 */
  public static final String PRODUCT_RELEVANCY_MAIN_PICTURE_YES = "1";

  /** 物料是否关联主图 - 未关联 */
  public static final String PRODUCT_RELEVANCY_MAIN_PICTURE_NO = "2";

  public static final Map<String, String> PRODUCT_RELEVANCY_MAIN_PICTURE_STATE =
      new HashMap<String, String>() {
        {
          put(PRODUCT_RELEVANCY_MAIN_PICTURE_YES, "已关联");
          put(PRODUCT_RELEVANCY_MAIN_PICTURE_NO, "未关联");
        }
      };

  /** 物料是否关联详情图 - 已关联 */
  public static final String PRODUCT_RELEVANCY_DETAILS_PICTURE_YES = "1";

  /** 物料是否关联详情图 - 未关联 */
  public static final String PRODUCT_RELEVANCY_DETAILS_PICTURE_NO = "2";

  public static final Map<String, String> PRODUCT_RELEVANCY_DETAILS_PICTURE_STATE =
      new HashMap<String, String>() {
        {
          put(PRODUCT_RELEVANCY_DETAILS_PICTURE_YES, "已关联");
          put(PRODUCT_RELEVANCY_DETAILS_PICTURE_NO, "未关联");
        }
      };

  /** 物料是否补充扩展属性 - 已补充 */
  public static final String PRODUCT_EXPAND_YES = "1";

  /** 物料是否补充扩展属性 - 未补充 */
  public static final String PRODUCT_EXPAND_NO = "2";

  /** 物料是否补充扩展属性 - 无需填写 */
  public static final String PRODUCT_EXPAND_NEED_NOT = "0";

  public static final Map<String, String> PRODUCT_EXPAND_TYPE =
      new HashMap<String, String>() {
        {
          put(PRODUCT_EXPAND_YES, "已补充");
          put(PRODUCT_EXPAND_NO, "未补充");
          put(PRODUCT_EXPAND_NEED_NOT, "无需填写");
        }
      };

  /** 物料类型 - 落地商物料 */
  public static final String PRODUCT_TYPE_LANDING_MERCHANT = "2";

  /** 物料类型 - 供应商 */
  public static final String PRODUCT_TYPE_SUPPLY_MERCHANT = "1";

  public static final Map<String, String> PRODUCT_TYPE =
      new HashMap<String, String>() {
        {
          put(PRODUCT_TYPE_LANDING_MERCHANT, "电商供应商物料");
          put(PRODUCT_TYPE_SUPPLY_MERCHANT, "供应商物料");
        }
      };

  /** 物料类型 1_供应商 2_落地商 */
  public static final String PRODUCT_TYPE_SUPPLIER = "1";

  public static final String PRODUCT_TYPE_FLOOR = "2";

  /** 上架项目导入模板锁定密码 */
  public static final String PROJECT_IMPORT_EXCEL_LOCKED_PASSWORD = "1234567";

  /** 合作类型 */
  /** ERP 付款单查询返回单据状态 - 创建 */
  public static final String ERP_PAYMENT_RESULT_STATUS_A = "A";

  /** ERP 付款单查询返回单据状态 - 审核中 */
  public static final String ERP_PAYMENT_RESULT_STATUS_B = "B";

  /** ERP 付款单查询返回单据状态 - 已审核 */
  public static final String ERP_PAYMENT_RESULT_STATUS_C = "C";

  /** ERP 付款单查询返回单据状态 - 重新审核 */
  public static final String ERP_PAYMENT_RESULT_STATUS_D = "D";

  /** ERP 付款单查询返回单据状态 - 暂存 */
  public static final String ERP_PAYMENT_RESULT_STATUS_Z = "Z";

  /** 图片关系 类似 */
  public static final String PRODUCT_TYPE_LIKE = "2";

  /** 图片关系 匹配 */
  public static final String PRODUCT_TYPE_MATCHING = "1";

  /** 落地商履约信息导入模板oss地址 */
  public static final String LANDING_MERCHANT_PERFORMANCE_INFO_OOS_ADDRESS =
      "srm/model/落地商履约信息导入.xlsx";
  /**
   * 国网商城平台编码
   */
  public static final String GW_PLATFORM_CODE = "ESG";
  /**
   * 导入状态 - 出现异常
   */
  public static final String IMPORT_STATE_EXCEPTION = "2";
  public static final String TWO = "2";

  static {
    LANDING_COMMERCIAL_ACCOUNT_PERIOD.put(
        LANDING_COMMERCIAL_ACCOUNT_PERIOD_BACK_TO_BACK, "背靠背（2天）");
    LANDING_COMMERCIAL_ACCOUNT_PERIOD.put(LANDING_COMMERCIAL_ACCOUNT_PERIOD_EVERY_WEEK, "周结（7天）");
    LANDING_COMMERCIAL_ACCOUNT_PERIOD.put(
        LANDING_COMMERCIAL_ACCOUNT_PERIOD_EVERY_HALF_MONTH, "半月结（15天）");
    LANDING_COMMERCIAL_ACCOUNT_PERIOD.put(LANDING_COMMERCIAL_ACCOUNT_PERIOD_MONTHLY, "月结（31天）");
    LANDING_COMMERCIAL_ACCOUNT_PERIOD.put(
        LANDING_COMMERCIAL_ACCOUNT_PERIOD_EVERY_SEASON, "季结（90天）");
    LANDING_COMMERCIAL_ACCOUNT_PERIOD.put(
        LANDING_COMMERCIAL_ACCOUNT_PERIOD_SEMIANNUALLY, "半年结（180天）");
    LANDING_COMMERCIAL_ACCOUNT_PERIOD.put(LANDING_COMMERCIAL_ACCOUNT_PERIOD_EVERY_YEAR, "年结（365天）");
  }

  static {
    TYPE_OF_CONTRACT.put(FRAME_CONTRACT, "框架合同");
    TYPE_OF_CONTRACT.put(ORDINARY_CONTRACT, "普通合同");
  }

  static {
    PAYMENT_RATIO_TYPE.put(PAYMENT_RATIO_TYPE_100, "100%");
  }

  static {
    CONTRACT_SIGNING_MODE.put(PAPER_CONTRACT, "纸质合同");
    CONTRACT_SIGNING_MODE.put(ELECTRonIC_CONTRACT, "电子合同");
  }

  static {
    SEX_MAP.put("1", "女");
    SEX_MAP.put("2", "男");
  }

  static {
    STATE_MAP.put(STATE_OK, "正常");
    STATE_MAP.put(STATE_LOCKED, "停用");
    STATE_MAP.put(STATE_DELETE, "删除");
  }

  static {
    YESORNO.put(YES, "是");
    YESORNO.put(NO, "否");
  }

  static {
    ROLEMAP.put(ROLEMAP_GENERALMANAGER, "经理");
  }

  static {
    COMMONSTATE.put(COMMONSTATE_DELETE, "删除");
    COMMONSTATE.put(COMMONSTATE_OK, "正常");
    COMMONSTATE.put(COMMONSTATE_LOCK, "停用");
    COMMONSTATE.put(COMMONSTATE_CHECKING, "审核中");
    COMMONSTATE.put(COMMONSTATE_BLACKLIST, "黑名单");
    COMMONSTATE.put(COMMONSTATE_TEMPORARYSTORAGE, "暂存");
  }

  static {
    // 启用
    STATE_TO_ERP_OPERATION_TYPE_MAP.put(COMMONSTATE_OK, "banNo");
    // 禁用
    STATE_TO_ERP_OPERATION_TYPE_MAP.put(COMMONSTATE_LOCK, "banYes");
    // 删除
    STATE_TO_ERP_OPERATION_TYPE_MAP.put(COMMONSTATE_DELETE, "del");
    // 拉黑 - 禁用
    STATE_TO_ERP_OPERATION_TYPE_MAP.put(COMMONSTATE_BLACKLIST, "banYes");
  }

  static {
    FILE_TYPE_TO_NAME.put(FILE_TYPE_SCXK, "生产许可");
    FILE_TYPE_TO_NAME.put(FILE_TYPE_ZLZS, "专利证书");
    FILE_TYPE_TO_NAME.put(FILE_TYPE_SPZCZS, "商品注册证书");
    FILE_TYPE_TO_NAME.put(FILE_TYPE_CPSYBG, "产品试验报告");
    FILE_TYPE_TO_NAME.put(FILE_TYPE_DSFJCBG, "第三方检测报告");
    FILE_TYPE_TO_NAME.put(FILE_TYPE_ISOZLRZTX, "ISO质量认证体系");
    FILE_TYPE_TO_NAME.put(FILE_TYPE_ISO14001HJGLTX, "ISO14001环境管理体系");
    FILE_TYPE_TO_NAME.put(FILE_TYPE_OHSAS18001ZYJKAQTX, "OHSAS18001职业健康安全管理体系");
    FILE_TYPE_TO_NAME.put(FILE_TYPE_CMSZLGLTXRZZS, "CMS测量管理体系认证证书");

  }

  static {
    RECEIVETYPE_TYPEMAP.put(RECEIVETYPE_TYPEMAP_WAIT, "未接收");
    RECEIVETYPE_TYPEMAP.put(RECEIVETYPE_TYPEMAP_DONE, "已接收");
  }

  static {
    SMSMODEL.put(SMSMODEL_REGISTER, "注册短信");
    SMSMODEL.put(SMSMODEL_FINDPWD, "找回密码");
  }

  static {
    PLATFORM_TYPE.put(PLATFORM_TYPE_BEFORE, "前台");
    PLATFORM_TYPE.put(PLATFORM_TYPE_AFTER, "后台");
  }

  static {
    AUDIT_STATE.put(AUDIT_STATE_PURCHASEIN, "采购审核中");
    AUDIT_STATE.put(AUDIT_STATE_PURCHASEFALUT, "采购审核未通过");
    AUDIT_STATE.put(AUDIT_STATE_MANAGEIN, "经理审核中");
    AUDIT_STATE.put(AUDIT_STATE_MANAGEFAULT, "经理审核未通过");
    AUDIT_STATE.put(AUDIT_STATE_MANAGESUCCESS, "审核已通过");
    AUDIT_STATE.put(AUDIT_STATE_CANCEL, "撤回中");
  }

  static {
    AUDIT_TYPE.put(AUDIT_TYPE_PURCHASESUCCESS, "采购审核通过");
    AUDIT_TYPE.put(AUDIT_TYPE_PURCHASEFAULT, "采购审核未通过");
    AUDIT_TYPE.put(AUDIT_TYPE_MANAGESUCCESS, "经理审核通过");
    AUDIT_TYPE.put(AUDIT_TYPE_MANAGEFAULT, "经理审核未通过");
    AUDIT_TYPE.put(AUDIT_TYPE_AGREESUCCESS, "协议审核通过");
    AUDIT_TYPE.put(AUDIT_TYPE_AGREEFAULT, "协议审核未通过");
  }

  static {
    APPLY_STATE.put(APPLY_STATE_WAIT, "待申请");
    APPLY_STATE.put(APPLY_STATE_ING, "申请中");
    APPLY_STATE.put(APPLY_STATE_FAULT, "申请失败");
    APPLY_STATE.put(APPLY_STATE_SUCCESS, "申请通过");
  }

  static {
    APPLY_STATE.put(APPLY_TYPE_FAULT, "申请失败");
    APPLY_STATE.put(APPLY_TYPE_SUCCESS, "申请通过");
  }

  static {
    RESOURCE_TYPE.put(RESOURCE_TYPE_REGISTER, "注册");
    RESOURCE_TYPE.put(RESOURCE_TYPE_EDIT, "修改");
    RESOURCE_TYPE.put(RESOURCE_TYPE_INPUT, "录入");
    RESOURCE_TYPE.put(RESOURCE_TYPE_AGREEMENT, "协议");
    RESOURCE_TYPE.put(RESOURCE_TYPE_SPADD, "商品新增");
    RESOURCE_TYPE.put(RESOURCE_TYPE_SPERR, "商品纠错");
    RESOURCE_TYPE.put(RESOURCE_TYPE_SHIELD, "拉黑");
  }

  static {
    AGREECHECK_STATE.put(AGREECHECK_STATE_NONE, "未上传");
    AGREECHECK_STATE.put(AGREECHECK_STATE_MANAGEIN, "审核中");
    AGREECHECK_STATE.put(AGREECHECK_STATE_MANAGEFAULT, "审核未通过");
    AGREECHECK_STATE.put(AGREECHECK_STATE_MANAGESUCCESS, "审核已通过");
    AGREECHECK_STATE.put(AGREECHECK_STATE_TEMP, "暂存");
  }

  static {
    AGREERESOURCE_TYPE.put(AGREERESOURCE_TYPE_ADD, "初次上传");
    AGREERESOURCE_TYPE.put(AGREERESOURCE_TYPE_EDIT, "协议修改");
  }

  static {
    FBSOURCE_TYPE.put(FBSOURCE_TYPE_BASE, "基本信息审核");
    FBSOURCE_TYPE.put(FBSOURCE_TYPE_AGREEMENT, "协议审核");
  }

  static {
    FBAGREESOURCE.put(FBAGREESOURCE_SUPPLIER, "供应商");
    FBAGREESOURCE.put(FBAGREESOURCE_PURCHASE, "采购");
  }

  static {
    ENTERPRISENATURE.put(ENTERPRISENATURE_SC, "生产型");
    ENTERPRISENATURE.put(ENTERPRISENATURE_FU, "服务型");
    ENTERPRISENATURE.put(ENTERPRISENATURE_SM, "商贸型");
  }

  static {
    PICTURESOURCE.put(PICTURESOURCE_SRM, "本地");
    PICTURESOURCE.put(PICTURESOURCE_MDM, "mdm");
  }

  static {
    SUPPLIERTYPE.put(SUPPLIERTYPE_CHINA, "国内");
    SUPPLIERTYPE.put(SUPPLIERTYPE_ABROAD, "国际");
    SUPPLIERTYPE.put(SUPPLIERTYPE_PERSONAL, "个人");
    SUPPLIERTYPE.put(SUPPLIER_TYPE_INTERNAL, "内部");
    SUPPLIERTYPE.put(SUPPLIER_TYPE_PROVISIONAL, "一次性");
  }

  static {
    INVOICETYPE.put(INVOICETYPE_VAT, "增值税专用发票");
    INVOICETYPE.put(INVOICETYPE_NORMAL, "普通发票");
  }

  static {
    SETTLE_CURRENCY_MAP.put("CNY", "人民币");
    SETTLE_CURRENCY_MAP.put("HKD", "香港元");
    SETTLE_CURRENCY_MAP.put("EUR", "欧元");
    SETTLE_CURRENCY_MAP.put("JPY", "日元");
    SETTLE_CURRENCY_MAP.put("TWD", "新台币元");
    SETTLE_CURRENCY_MAP.put("GBP", "英镑");
    SETTLE_CURRENCY_MAP.put("USD", "美元");
  }

  static {
    TAX_RATE_MAP.put("0004", "9%增值税");
    TAX_RATE_MAP.put("SL01_SYS", "17%增值税");
    TAX_RATE_MAP.put("SL02_SYS", "13%增值税");
    TAX_RATE_MAP.put("SL04_SYS", "0%增值税");
    TAX_RATE_MAP.put("SL05_SYS", "11%增值税");
    TAX_RATE_MAP.put("SL06_SYS", "6%增值税");
    TAX_RATE_MAP.put("SL07_SYS", "3%增值税");
    TAX_RATE_MAP.put("SL08_SYS", "16%增值税");
    TAX_RATE_MAP.put("SL09_SYS", "1%增值税");
    TAX_RATE_MAP.put("SL45_SYS", "12%增值税");
    TAX_RATE_MAP.put("SL62_SYS", "10%增值税");
  }

  static {
    BRAND_MANAGE_TYPE_TO_NAME.put(BRAND_MANAGE_TYPE_BRAND, "品牌商");
    BRAND_MANAGE_TYPE_TO_NAME.put(BRAND_MANAGE_TYPE_STORE, "集货商");
  }

  static {
    BRAND_AUTHORIZATION_TYPE_TO_NAME.put(BRAND_AUTHORIZATION_TYPE_BRAND, "品牌授权");
    BRAND_AUTHORIZATION_TYPE_TO_NAME.put(BRAND_AUTHORIZATION_TYPE_COMPANY, "公司授权");
  }

  static {
    UPLOAD_STATUS_TO_NAME.put(UPLOAD_STATUS_UPLOADED, "已上传");
    UPLOAD_STATUS_TO_NAME.put(UPLOAD_STATUS_NOT_UPLOADED, "未上传");
  }

  static {
    SUPPLIER_CHANGE_RECODE_TO_NAME.put(SUPPLIER_CHANGE_RECODE_SUCCESS, "通过");
    SUPPLIER_CHANGE_RECODE_TO_NAME.put(SUPPLIER_CHANGE_RECODE_CHECK, "待审核");
    SUPPLIER_CHANGE_RECODE_TO_NAME.put(SUPPLIER_CHANGE_RECODE_REJECT, "驳回");
    SUPPLIER_CHANGE_RECODE_TO_NAME.put(SUPPLIER_CHANGE_RECODE_OK, "正常");
  }

  static {
    SUPPLIER_USER_ROLE_TO_NAME.put(SUPPLIER_USER_ROLE_ADMIN, "管理员");
    SUPPLIER_USER_ROLE_TO_NAME.put(SUPPLIER_USER_ROLE_ORDINARY, "采购");
    SUPPLIER_USER_ROLE_TO_NAME.put(USER_ROLE_FINANCE, "财务");
    SUPPLIER_USER_ROLE_TO_NAME.put(SUPPLIER_USER_ROLE_SALESMAN, "业务员");
    SUPPLIER_USER_ROLE_TO_NAME.put(SUPPLIER_USER_ROLE_COMMERCE_ADMIN, "电商供应商管理员");
  }

  static {
    GROUPTYPE_MAP.put(GROUPTYPE_MAP_ORGANIZATION, "组织");
    GROUPTYPE_MAP.put(GROUPTYPE_MAP_DEPT, "部门");
  }

  static {
    GROUP_TYPE_PARAM_MAP.put(GROUPTYPE_MAP_DEPT, "deptId");
    GROUP_TYPE_PARAM_MAP.put(GROUPTYPE_MAP_ORGANIZATION, "groupId");
  }

  static {
    GROUP_TYPE_SQL_PARAM_MAP.put(GROUPTYPE_MAP_DEPT, "dept_id");
    GROUP_TYPE_SQL_PARAM_MAP.put(GROUPTYPE_MAP_ORGANIZATION, "group_id");
  }

  static {
    SUPPLIER_SYN_STATE.put(SUPPLIER_SYN_STATE_NOXE, "未上传");
    SUPPLIER_SYN_STATE.put(SUPPLIER_SYN_STATE_NOERPCODE, "缺失ERP编码");
    SUPPLIER_SYN_STATE.put(SUPPLIER_SYN_STATE_FALSE, "同步失败");
    SUPPLIER_SYN_STATE.put(SUPPLIER_SYN_STATE_SUCCESS, "同步成功");
    SUPPLIER_SYN_STATE.put(SUPPLIER_SYN_STATE_TEMP, "暂存");
  }

  static {
    USERCHECKTYPE_MAP.put(USERCHECKTYPE_ADD, "新增");
    USERCHECKTYPE_MAP.put(USERCHECKTYPE_UPDATE, "修改");
    USERCHECKTYPE_MAP.put(USERCHECKTYPE_SHIELD, "拉黑");
    USERCHECKTYPE_MAP.put(USERCHECKTYPE_SUPPLIER_LEVEL, "修改供应商等级");
  }

  static {
    SUPPLIERCHECKTYPE_MAP.put(SUPPLIERCHECKTYPE_MAP_ADD, "新增");
    SUPPLIERCHECKTYPE_MAP.put(SUPPLIERCHECKTYPE_MAP_UPDATE, "修改");
    SUPPLIERCHECKTYPE_MAP.put(SUPPLIERCHECKTYPE_MAP_SHIELD, "拉黑");
    SUPPLIERCHECKTYPE_MAP.put(SUPPLIERCHECKTYPE_MAP_LEVEL, "修改供应商等级");
  }

  static {
    PRODUCTCHECKTYPE_MAP.put(PRODUCTCHECKTYPE_MAP_ADD, "新增");
    PRODUCTCHECKTYPE_MAP.put(PRODUCTCHECKTYPE_MAP_UPDATE, "修改");
  }

  static {
    SEARCH_TYPE.put(SEARCH_TYPE_CONTRACT, "合同");
    SEARCH_TYPE.put(SEARCH_TYPE_SUPPLIER, "供应商");
    SEARCH_TYPE.put(SEARCH_TYPE_INQUIRY, "询价");
    SEARCH_TYPE.put(SEARCH_TYPE_SUPPLIER_CHECK_RECORD, "供应商审核记录(前台)");
    SEARCH_TYPE.put(SEARCH_TYPE_SUPPLIER_USER, "供应商账户");
    SEARCH_TYPE.put(SEARCH_TYPE_SUPPLIER_ABOARD, "国际供应商");
    SEARCH_TYPE.put(SEARCH_TYPE_SUPPLIER_BLACK, "黑名单供应商");
    SEARCH_TYPE.put(SEARCH_TYPE_SUPPLIER_MY, "我的申请");
    SEARCH_TYPE.put(SEARCH_TYPE_SUPPLIER_WAIT, "待我审核");
    SEARCH_TYPE.put(SEARCH_TYPE_SUPPLIER_CHECKED, "审核记录");
    SEARCH_TYPE.put(SEARCH_TYPE_MEETING, "会议");
    SEARCH_TYPE.put(SEARCH_TYPE_USER, "用户");
    SEARCH_TYPE.put(SEARCH_TYPE_DEPART, "部门");
    SEARCH_TYPE.put(SEARCH_TYPE_ORG, "组织");
    SEARCH_TYPE.put(SEARCH_TYPE_SUPPLIER_FRONT, "供应商待审核(前台)");
    SEARCH_TYPE.put(SEARCH_TYPE_SUPPLIER_BRAND, "品牌(前台)");
    SEARCH_TYPE.put(SEARCH_TYPE_SUPPLIER_BRAND_AUTHORIZATION, "品牌授权(前台)");
    SEARCH_TYPE.put(SEARCH_TYPE_SUPPLIER_PRODUCT_LIST, "物料列表(前台)");
    SEARCH_TYPE.put(SEARCH_TYPE_SUPPLIER_BATCH_ADD, "批量新增");
    SEARCH_TYPE.put(SEARCH_TYPE_SUPPLIER_ORDER, "供应商订单");
    SEARCH_TYPE.put(SEARCH_TYPE_SUPPLIER_ORDERLIST, "履约订单");
    SEARCH_TYPE.put(SEARCH_TYPE_SUPPLIER_FILING, "报备单");
    SEARCH_TYPE.put(SEARCH_TYPE_SUPPLIER_ACCOUNT, "对账单");
    SEARCH_TYPE.put(SEARCH_TYPE_SUPPLIER_TEMP, "供应商模板");
    SEARCH_TYPE.put(SEARCH_TYPE_SUPPLIER_CHINA, "国内供应商");
    SEARCH_TYPE.put(SEARCH_TYPE_SUPPLIER_ABROAD, "国外供应商");
    SEARCH_TYPE.put(SEARCH_TYPE_SUPPLIER_PERSON, "个人供应商");
    SEARCH_TYPE.put(SEARCH_TYPE_ERP_ORDER_LIST, "履约订单列表");
    SEARCH_TYPE.put(SEARCH_TYPE_ERP_ORDER_RETURN_LIST, "履约退货列表");
    SEARCH_TYPE.put(SEARCH_TYPE_GET_RETURN_OR_CANCEL_PAGE, "分页退货取消");
    SEARCH_TYPE.put(SEARCH_TYPE_GET_PAGE_SUPPLIER_ORDER_PAGE, "分页获取供应商订单");
  }

  static {
    SUPPLIER_MANAGETYPE_MAP.put(SUPPLIER_MANAGETYPE_CX, "存续");
    SUPPLIER_MANAGETYPE_MAP.put(SUPPLIER_MANAGETYPE_ZY, "在业");
    SUPPLIER_MANAGETYPE_MAP.put(SUPPLIER_MANAGETYPE_DX, "吊销");
    SUPPLIER_MANAGETYPE_MAP.put(SUPPLIER_MANAGETYPE_ZX, "注销");
    SUPPLIER_MANAGETYPE_MAP.put(SUPPLIER_MANAGETYPE_QR, "迁入");
    SUPPLIER_MANAGETYPE_MAP.put(SUPPLIER_MANAGETYPE_QC, "迁出");
    SUPPLIER_MANAGETYPE_MAP.put(SUPPLIER_MANAGETYPE_TY, "停业");
    SUPPLIER_MANAGETYPE_MAP.put(SUPPLIER_MANAGETYPE_QS, "清算");
  }

  static {
    MISSION_STATE_MAP.put(MISSION_STATE_DEL, "删除");
    MISSION_STATE_MAP.put(MISSION_STATE_SUCCESS, "成功");
    MISSION_STATE_MAP.put(MISSION_STATE_FAIL, "失败");
    MISSION_STATE_MAP.put(MISSION_STATE_ING, "进行中");
  }

  static {
    USER_PERMISSIONS_DATA_TYPE.put(USER_PERMISSION_CONTRACT, "user_permission_contract");
    USER_PERMISSIONS_DATA_TYPE.put(USER_PERMISSION_INQUIRY, "user_permission_inquiry");
    USER_PERMISSIONS_DATA_TYPE.put(
        USER_PERMISSION_BLOCK_SUPPLIER, "user_permission_block_supplier");
    USER_PERMISSIONS_DATA_TYPE.put(
        USER_PERMISSION_UPDATE_SUPPLIER, "user_permission_update_supplier");
  }

  static {
    DATA_PERMISSIONS_TYPE.put(DATA_PERMISSIONS_TYPE_OWM, "permission_own");
    DATA_PERMISSIONS_TYPE.put(DATA_PERMISSIONS_TYPE_DEPT, "permission_dept");
    DATA_PERMISSIONS_TYPE.put(DATA_PERMISSIONS_TYPE_DEPT_CHILDREN, "permission_dept_children");
    DATA_PERMISSIONS_TYPE.put(DATA_PERMISSIONS_TYPE_ORGANIZATION, "permission_organization");
  }

  static {
    //    ACCOUNT_PERIOD.put(ACCOUNT_PERIOD_PAY_FIRST, "款到发货");
    //    ACCOUNT_PERIOD.put(ACCOUNT_PERIOD_MONTH, "月结");
    //    ACCOUNT_PERIOD.put(ACCOUNT_PERIOD_QUARTER, "季结");
    //    ACCOUNT_PERIOD.put(ACCOUNT_PERIOD_HALF_YEAR, "半年结");
    //    ACCOUNT_PERIOD.put(ACCOUNT_PERIOD_YEAR, "年结");
    //    ACCOUNT_PERIOD.put(ACCOUNT_PERIOD_BACK_TO_BACK, "背靠背");
    ACCOUNT_PERIOD.put(ACCOUNT_PERIOD_IMMEDIATELY, "立即应付（0天）");
    ACCOUNT_PERIOD.put(WITHIN_7_DAYS, "7 天之内（7天）");
    ACCOUNT_PERIOD.put(WITHIN_15_DAYS, "15 天之内（15天）");
    ACCOUNT_PERIOD.put(WITHIN_45_DAYS, "45 天之内（45天）");
    ACCOUNT_PERIOD.put(WITHIN_60_DAYS, "60 天之内（60天）");
    ACCOUNT_PERIOD.put(WITHIN_90_DAYS, "90 天之内（90天）");
    ACCOUNT_PERIOD.put(WITHIN_180_DAYS, "180 天之内（180天）");
    ACCOUNT_PERIOD.put(WITHIN_360_DAYS, "360 天之内（360天）");
    ACCOUNT_PERIOD.put(ACCOUNT_PERIOD_THIRTY, "30 天之内 （30天）");
  }

  static {
    DING_TALK_ROBOT_CAR_MSG_TYPE.put(1, "sampleActionCard");
    DING_TALK_ROBOT_CAR_MSG_TYPE.put(2, "sampleActionCard2");
    DING_TALK_ROBOT_CAR_MSG_TYPE.put(3, "sampleActionCard3");
    DING_TALK_ROBOT_CAR_MSG_TYPE.put(4, "sampleActionCard4");
    DING_TALK_ROBOT_CAR_MSG_TYPE.put(5, "sampleActionCard5");
    DING_TALK_ROBOT_CAR_MSG_TYPE.put(6, "sampleActionCard6");
  }

  static {
    SUPPLIER_PERFORMANCE_STATUS_MAP.put(SUPPLIER_PERFORMANCE_STATUS_EFFECT, "生效");
    SUPPLIER_PERFORMANCE_STATUS_MAP.put(SUPPLIER_PERFORMANCE_STATUS_CLOSE, "关闭");
  }

  static {
    SUPPLIER_PERFORMANCE_COOPERATION_TYPE_MAP.put(
        SUPPLIER_PERFORMANCE_COOPERATION_TYPE_SERVICE, "服务商");
    SUPPLIER_PERFORMANCE_COOPERATION_TYPE_MAP.put(
        SUPPLIER_PERFORMANCE_COOPERATION_TYPE_REPORTER, "报备商");
  }

  static {
    CONTRACT_SOURCE_TYPE.put(CONTRACT_SOURCE_SRM, "SRM新增");
  }

  static {
    SIGNATURE_STATUS_TYPE.put(SIGNATURE_STATUS_YES, "已签章");
    SIGNATURE_STATUS_TYPE.put(SIGNATURE_STATUS_NO, "未签章");
  }

  /** 是否包含 - 是 */
  public static final String IS_CONTAIN_YES = "1";

  /** 是否包含 - 否 */
  public static final String IS_CONTAIN_NO = "0";

  /** 报备类型 - 商品报备 */
  public static final String FILING_TYPE_PRODUCT = "1";

  /** 报备类型 - 客户订单报备 */
  public static final String FILING_TYPE_ORDER_NO = "2";

  public static final Map<String, String> FILING_TYPE =
      new HashMap<String, String>() {
        {
          put(FILING_TYPE_PRODUCT, "商品报备");
          put(FILING_TYPE_ORDER_NO, "客户订单报备");
        }
      };

  /** 咸亨国际各系统响应code - 成功 */
  public static final Integer HTTP_RESPONSE_CODE_YES = 0;

  /** 咸亨国际各系统响应code - 异常 */
  public static final Integer HTTP_RESPONSE_CODE_NO = 1;

  /** 物料检测费枚举 -- 不含检测费 */
  public static final String TEST_FEE_IS_NOT_INCLUDED = "0";

  /** 物料检测费枚举 -- 含第三方检测费 */
  public static final String INCLUDING_THREE_PARTY_TEST_FEE = "1";

  /** 物料检测费枚举 -- 含出厂检测费 */
  public static final String INCLUDING_FACTORY_INSPECTION_FEE = "2";

  /** 落地商订单在某个时间点前自动发货 key 表示 小时，value 表示分钟 */
  public static final Pair<Integer, Integer> AUTO_WAREHOUSING_BEFORE = new Pair<>(17, 30);

  /** 存放钉钉消息的表存放 待办类型 */
  public static final String SAVE_MESSAGE_TYPE_DING_TODO = "2";

  /** 存放钉钉消息的表存放 卡片类型 */
  public static final String SAVE_MESSAGE_TYPE_DING_CARD = "1";

  /** 品牌MPM状态-审核中 */
  public static final String BRAND_MPM_STATE_RUNNING = "1";

  /** 品牌MPM状态-驳回 */
  public static final String BRAND_MPM_STATE_REJECT = "2";

  /** 品牌MPM状态-通过 */
  public static final String BRAND_MPM_STATE_PASS = "3";

  /** 供应商发票审核状态 */
  public static final String ORDER_INVOICE_STATE_ING = "1"; // 审核中

  public static final String ORDER_INVOICE_STATE_TEMP = "2"; // 暂存
  public static final String ORDER_INVOICE_STATE_PASS = "3"; // 已开票
  public static final String ORDER_INVOICE_STATE_REJECT = "4"; // 驳回
  public static final String ORDER_INVOICE_STATE_NOT_DONE = "5"; // 未开票
  public static final String ORDER_INVOICE_STATE_PART_DONE = "6"; // 部分开票
  public static final String ORDER_INVOICE_STATE_OFFSET = "7"; // 已冲销
  public static final String ORDER_INVOICE_STATE_HAND = "8"; // 开票中

  /**
   *   已冲销，未开票----》全部变为未开票   7,5->5
   *   暂存，驳回，审核中，部分开票----》全部变为开票中  2,4,1,6->8
   *   已开票----》全部变为已开票 3->3
   */
  public static final TreeMap<String, String> ORDER_INVOICE_STATE_TYPE_MAP = new TreeMap<>();
  static {
    ORDER_INVOICE_STATE_TYPE_MAP.put(ORDER_INVOICE_STATE_ING, ORDER_INVOICE_STATE_HAND);
    ORDER_INVOICE_STATE_TYPE_MAP.put(ORDER_INVOICE_STATE_TEMP, ORDER_INVOICE_STATE_HAND);
    ORDER_INVOICE_STATE_TYPE_MAP.put(ORDER_INVOICE_STATE_REJECT, ORDER_INVOICE_STATE_HAND);
    ORDER_INVOICE_STATE_TYPE_MAP.put(ORDER_INVOICE_STATE_PART_DONE, ORDER_INVOICE_STATE_HAND);
    ORDER_INVOICE_STATE_TYPE_MAP.put(ORDER_INVOICE_STATE_OFFSET, ORDER_INVOICE_STATE_NOT_DONE);
  }

  public static final Map<String, String> ORDER_SUPPLIER_INVOICE_STATE_TYPE_NEW =
      ImmutableMap.<String, String>builder()
          .put(ORDER_INVOICE_STATE_HAND, "开票中")
          .put(ORDER_INVOICE_STATE_PASS, "已开票")
          .put(ORDER_INVOICE_STATE_NOT_DONE, "未开票")
          .build();

  public static final Map<String, String> ORDER_SUPPLIER_INVOICE_STATE_TYPE_OLD =
      ImmutableMap.<String, String>builder()
          .put(ORDER_INVOICE_STATE_ING, "开票中") // 待确认
          .put(ORDER_INVOICE_STATE_TEMP, "开票中")
          .put(ORDER_INVOICE_STATE_PASS, "已开票") // 已开票
          .put(ORDER_INVOICE_STATE_REJECT, "开票中")
          .put(ORDER_INVOICE_STATE_NOT_DONE, "未开票")
          .put(ORDER_INVOICE_STATE_PART_DONE, "开票中")
          .put(ORDER_INVOICE_STATE_OFFSET, "未开票")
          .put(ORDER_INVOICE_STATE_HAND,"开票中")
          .build();

  public static final Map<String, String> ORDER_SUPPLIER_INVOICE_STATE_TYPE =
      ImmutableMap.<String, String>builder()
          .put(ORDER_INVOICE_STATE_ING, "审核中") // 待确认
          .put(ORDER_INVOICE_STATE_TEMP, "暂存")
          .put(ORDER_INVOICE_STATE_PASS, "已开票") // 已开票
          .put(ORDER_INVOICE_STATE_REJECT, "驳回")
          .put(ORDER_INVOICE_STATE_NOT_DONE, "未开票")
          .put(ORDER_INVOICE_STATE_PART_DONE, "部分开票")
          .put(ORDER_INVOICE_STATE_OFFSET, "已冲销")
          .build();

  public static final String APPROVAL_TEMPLATE_ORDER = "1"; // 订单报备单审批模板
  public static final String APPROVAL_TEMPLATE_PRODUCT = "2"; // 商品报备单审批模板

  public static final String SUPPLIER_ORDER_REFUSE_UNPASS = "1"; // 驳回
  public static final String SUPPLIER_ORDER_REFUSE_PASS = "2"; // 通过

  /** 审核中 */
  public static final String PAYMENT_APPLY_EXAMINE = "1";

  /** 通过 */
  public static final String PAYMENT_APPLY_PASS = "2";

  /** 驳回 */
  public static final String PAYMENT_APPLY_UNPASS = "3";

  /** 已放弃 */
  public static final String PAYMENT_APPLY_GIVEUP = "4";

  // 申请类型 1 预付申请 2延长申请 3加急申请 4提款申请 5冻结申请 6退款申请 7 解冻申请
  /** 预付申请 */
  public static final String PAYMENT_TYPE_ADVANCE = "1";

  /** 2延长申请 */
  public static final String PAYMENT_TYPE_EXTEND = "2";

  /** 3加急申请 */
  public static final String PAYMENT_TYPE_URGENT = "3";

  /** 4提款申请 */
  public static final String PAYMENT_TYPE_DRAW = "4";

  /** 5冻结申请 */
  public static final String PAYMENT_TYPE_FREEZE = "5";

  /** 6 退款申请 */
  public static final String PAYMENT_TYPE_RETURN = "6";

  /** 7 解冻申请 */
  public static final String PAYMENT_TYPE_THAW = "7";

  /** 万聚组织id */
  public static final String WAN_JU_GROUP_ID = "2c90e85f7aecb67e017aecfd5efe2886";

  /** 入库单 - 发票状态-冲销 */
  public static final String PURCHASE_ORDER_INVOICE_RELATION_INVOICE_STATE_REVERSAL = "7";

  /** 暂存 */
  public static final Integer SAP_INVOICE_STAGING = 1;

  /** 提交 */
  public static final Integer SAP_INVOICE_SUBMIT = 2;

  // 采购订单发票类型
  /** 增值税专用 */
  public static final String INVOICE_ADD_TAX_ZY = "1";

  /** 增值税普通 */
  public static final String INVOICE_ADD_TAX_PT = "2";

  /** 其他 */
  public static final String INVOICE_OTHER = "3";

  /**
   * 需要开红票标识
   */
  public static final String NEED_RED_TICKET = "1";



  /** 货币码映射表 */
  public static final Map<String, String> MONEY_CODE_MAP =
      ImmutableMap.<String, String>builder()
          .put("CNY", "人民币")
          .put("USD", "美元")
          .put("GBP", "英镑")
          .put("JPY", "日元")
          .put("TWD", "新台币")
          .put("EUR", "欧元")
          .put("HKD", "港币")
          .build();

  /** 项目类别-委外加工 / 外协 */
  public static final String PROJECT_TYPE_WW = "L";

  /** 项目类别-寄售 */
  public static final String PROJECT_TYPE_JS = "K";

  /** 项目类别-标准 */
  public static final String PROJECT_TYPE_STANDARD = "";


  /** 项目类别表 */
  public static final Map<String, String> PROJECT_TYPE_MAP =
      ImmutableMap.<String, String>builder()
          .put(PROJECT_TYPE_STANDARD, "标准")
          .put(PROJECT_TYPE_JS, "寄售")
          .put(PROJECT_TYPE_WW, "委外加工")
          .build();

  /** 布尔值对应中文汉字表 */
  public static final Map<Boolean, String> BOOLEAN_TO_TEXT_MAP =
      ImmutableMap.<Boolean, String>builder().put(true, "是").put(false, "否").build();

  /** 供应商订单客户回款状态表 */
  public static final Map<String, String> SUPPLIER_ORDER_SUPPLIER_OPEN_INVOICE_STATE_MAP =
      ImmutableMap.<String, String>builder()
          .put(STATE_OK, "已完成")
          .put(STATE_NO, "未完成")
          .put("", "")
          .build();

  /**
   * SAP 上线的时间
   */
  public static long SAP_ONLINE_TIME = 1709222400000L;

  public static final String PURCHASE_APPLY_FOR_ORDER_TEMPLATE =
      "srm/model/万聚采购申请导出模板6.7.0.docx";

  public static final String USER_NAME_GUO_JIA_LEI = "郭佳蕾";
  public static final String USER_NAME_LAN_LIN_HUI = "蓝林慧";
  public static final String USER_NAME_FENG_JIA_LE = "冯家乐";


  /** 合同附件审批 人员 */
  public static final List<String> APPROVAL_OF_CONTRACT_ATTACHMENTS_PERSON =
      Arrays.asList(USER_NAME_GUO_JIA_LEI);

  /** 合同到期提醒 */
  public static final List<String> CONTRACT_EXPIRATION_REMINDERS =
      Arrays.asList(
          USER_NAME_GUO_JIA_LEI,
          USER_NAME_LAN_LIN_HUI);


  /** 合同到期提醒 天数 */
  public static final int IN_CONTRACT_EXPIRATION_EXPIRE = 20;

  public static final int IN_CONTRACT_EXPIRATION_EXPIRE_VALUE = 0;
  public static final String COUNTRY_ZHONG_GUO = "中国";

  /**
   * 冲销状态 0否 1是
   */
  public static final Integer WRITE_OFF_STATE_NO = 0;
  public static final Integer WRITE_OFF_STATE_YES = 1;
  /**
   * 是否查询红票数量
   */
  public static final Integer IS_QUERY_RED_INVOICE_NUMBER_NO = 0;
  public static final Integer IS_QUERY_RED_INVOICE_NUMBER_YES = 1;


  /** 缺省 */
  public static final String DEFAULT_ID = "default";

  /** 缺省 */
  public static final String DEFAULT_STRING = "缺省";
  /**
   * 采购价格库-价格类型-签约成本价
   */
  public static final String PURCHASE_PRICE_TYPE_SIGN_COST = "0";
  /**
   * 采购价格库-价格类型-首次成交价
   */
  public static final String PURCHASE_PRICE_TYPE_FIRST_DEAL = "1";
  public static final Map<String,String> PURCHASE_PRICE_TYPE_MAP = ImmutableMap.<String, String>builder()
      .put(PURCHASE_PRICE_TYPE_SIGN_COST, "签约成本价")
      .put(PURCHASE_PRICE_TYPE_FIRST_DEAL, "首次成交价")
      .build();
  /**
   * 发票录入-订单类型-电商供应商订单
   */
  public static final String INVOICE_INPUT_ORDER_TYPE_ELECTRONIC = "0";
  /**
   * 发票录入-订单类型-供应商订单
   */
  public static final String INVOICE_INPUT_ORDER_TYPE_SUPPLIER = "1";
  /**
   * 报备单-常规供货-是
   */
  public static final String REGULAR_SUPPLY_YES = "1";
  /**
   * 报备单-常规供货-否
   */
  public static final String REGULAR_SUPPLY_NO = "0";

  /**
   * 有无保证金的分界线
   */
  public static final BigDecimal DEPOSIT_LINE = BigDecimal.ZERO;

  /**
   * 供应商折扣比例修改字段--当前折扣比例
   */
  public static final String SUPPLIER_DISCOUNT_CURRENT = "当前折扣比例";

  /**
   * excel 导出常量无
   */
  public static final String EXCEL_EXPORT_NO = "无";

  /**
   * 合同归档状态 归档 1
   */
  public static final String CONTRACT_FILING_STATE_YES = "1";

  /**
   * 是否有仓储。值为1表示有，0表示无
   */
  public static final Map<String, String> IS_WAREHOUSING_TYPE =
      ImmutableMap.<String, String>builder()
          .put(STATE_OK, "有")
          .put(STATE_NO, "无")
          .build();

  /**
   * 常规供货。值为1表示是，0表示否
   */
  public static final Map<String, String> REGULAR_SUPPLY_TYPE =
      ImmutableMap.<String, String>builder()
          .put(REGULAR_SUPPLY_YES, "是")
          .put(REGULAR_SUPPLY_NO, "否")
          .build();
  /**
   * 是否直发：1是，0否
   */
  public static final Map<String, String> DIRECT_SHIPMENT_TYPE =
      ImmutableMap.<String, String>builder()
          .put(STATE_OK, "是")
          .put(STATE_NO, "否")
          .build();
  /**
   * 导入状态
   */
  public static final Map<String, String> IMPORT_STATE_TYPE =
      ImmutableMap.<String, String>builder()
          .put(STATE_NO, "进行中")
          .put(STATE_OK, "已完成")
          .put(IMPORT_STATE_EXCEPTION, "出现异常")
          .build();
  /**
   * 商业汇票
   */
  public static final String COMMERCIAL = "A";
  /**
   * 银行汇票
   */
  public static final String DRAFT = "B";
  /**
   * 供应链金额
   */
  public static final String CHAINED_FINANCIALL = "C";
  /**
   * 银行转账(含网银、转账支票、现金支票)
   */
  public static final String TRANSFER = "D";
  /**
   * 其他
   */
  public static final String OHTER = "E";
  public static final Map<String, String> SAP_PAYMENT_TYPE =
      ImmutableMap.<String, String>builder()
          .put(PayTypeSAPEnums.COMMERCIAL.getCode(), COMMERCIAL)
          .put(PayTypeSAPEnums.DRAFT.getCode(), DRAFT)
          .put(PayTypeSAPEnums.CHAINED_FINANCIALL.getCode(), CHAINED_FINANCIALL)
          .put(PayTypeSAPEnums.TRANSFER.getCode(), TRANSFER)
          .put(PayTypeSAPEnums.OHTER.getCode(), OHTER)
          .build();

  /**
   * 凭证生成方式：系统发起
   */
  public static final String VOUCHER_TYPE_BY_SY = "1";

  /**
   * 凭证生成方式：填写单号
   */
  public static final String VOUCHER_TYPE_BY_CODE = "2";

  /**
   * 是否急单：是 Y,否 N
   */
  public static final String IS_WORRY_ORDER = "Y";
  public static final String IS_NOT_WORRY_ORDER = "N";

  /**
   * 国内，海外，个人供应商集合
   */
  public static final List<String> SUPPLIER_TYPE_CHINA_ABROAD_PERSONAL_LIST =
      ListUtil.toList(SUPPLIERTYPE_CHINA, SUPPLIERTYPE_ABROAD, SUPPLIERTYPE_PERSONAL);
  /**
   * 国内，海外，供应商集合
   */
  public static final List<String> SUPPLIER_TYPE_CHINA_ABROAD_LIST =
      ListUtil.toList(SUPPLIERTYPE_CHINA, SUPPLIERTYPE_ABROAD);

  /**
   * 平台对接模式。1-专区
   */
  public static final String PLATFORM_MODE_AREA = "1";
  /**
   * 平台对接模式。2-API
   */
  public static final String PLATFORM_MODE_API = "2";
  /**
   * 平台对接模式。3-线下
   */
  public static final String PLATFORM_MODE_OFFLINE = "3";

  /**
   * 1.平台消息变更
   */
  public static final String PLATFORM_MODIFIED_TYPE = "1";

  /**
   * 2.平台,招投标项目信息,负责采购,自定义字段变更
   */
  public static final String PLATFORM_PROJECT_MODIFIED_ALL_TYPE = "2";

  /**
   * 任务名称：同步客户平台列表
   */
  public static final String MISSION_TYPE_NAME_CUSTOMER_PLATFORM = "同步客户平台列表";
  /**
   * 前台用户角色 1 管理员
   */
  public static final String FRONT_USER_ROLE_ADMIN = "1";

  /**
   * 前台用户角色 2 普通
   */
  public static final String FRONT_USER_ROLE_ORDINARY = "2";

  /**
   * 配置类型：1.采购订单提交校验配置
   */
  public static final String CONFIG_TYPE_SUPPLIER_ORDER_SUBMIT = "1";

  /**
   * 任务名称：同步库位管理列表
   */
  public static final String MISSION_TYPE_NAME_INVENTORY_LOCATION = "同步库位管理列表";


  /** 账期模板 - 平台编码与网址对应关系 */
  public static final TreeMap<String, String> PLATFORM_CODE_AND_URL_MAP = new TreeMap<>();

  static {
    PLATFORM_CODE_AND_URL_MAP.put("10", "https://new.njsc365.com");
    PLATFORM_CODE_AND_URL_MAP.put("119", "https://new.njsc365.com");
    PLATFORM_CODE_AND_URL_MAP.put("121", "https://new.njsc365.com");
    PLATFORM_CODE_AND_URL_MAP.put("WZ0270", "https://new.njsc365.com");
    PLATFORM_CODE_AND_URL_MAP.put("WZ0271", "https://new.njsc365.com");
    PLATFORM_CODE_AND_URL_MAP.put("32", "https://emp.iccec.cn/");
    PLATFORM_CODE_AND_URL_MAP.put("3201", "https://emp.iccec.cn/");
    PLATFORM_CODE_AND_URL_MAP.put("92", "https://emp.iccec.cn/");
    PLATFORM_CODE_AND_URL_MAP.put("110", "https://sso.aecc-mall.com/login?service=https://www.aecc-mall.com/caslogin");
    PLATFORM_CODE_AND_URL_MAP.put("100", "http://www.cgdgmall.com/#/homePageLf");
    PLATFORM_CODE_AND_URL_MAP.put("123", "https://zgzc.gcycloud.cn/#/mall");
    PLATFORM_CODE_AND_URL_MAP.put("95", "https://zgzc.gcycloud.cn/#/mall");
  }

  /**
   * 需付款列表-发货状态：1.未发货（两个时间都没有）
   */
  public static final String NEED_PAYMENT_NOT_SHIPPED = "1";

  /**
   * 需付款列表-发货状态：2：部分发货：（有首次发货时间且没有全部发货时间）
   */
  public static final String NEED_PAYMENT_PARTIAL_SHIPPED = "2";

  /**
   * 需付款列表-发货状态：3：已发货：（有全部发货时间）
   */
  public static final String NEED_PAYMENT_SHIPPED = "3";


  // 品牌原厂
  public static final String BRAND_BUSINESS_TYPE_ORIGINAL_FACTORY = "1";

  // 品牌代理商
  public static final String BRAND_BUSINESS_TYPE_AGENT = "2";
}
