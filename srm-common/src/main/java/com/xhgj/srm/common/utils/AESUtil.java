package com.xhgj.srm.common.utils;
import lombok.extern.slf4j.Slf4j;
import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

/**
 * <AUTHOR>
 * @date 2023/9/5 09:43:43
 * @description
 */
@Slf4j
public class AESUtil {

  //初始向量（偏移）
  public static final String VIPARA = "abcdefghij123456";   //AES 为16bytes. DES 为8bytes
  //编码方式
  public static final String bm = "UTF-8";

  //私钥  （密钥）16 位数，32 位数对jdk 版本要求高，不建议推荐
  private static final String ASE_KEY="a59c5b3d37114f62";   //AES固定格式为128/192/256 bits.即：16/24/32bytes。DES固定格式为128bits，即8bytes。

  /**
   * Aes加密
   * @param cleartext
   * @return
   */
  public static String encrypt(String cleartext) {
    try {
      IvParameterSpec zeroIv = new IvParameterSpec(VIPARA.getBytes());
      //两个参数，第一个为私钥字节数组， 第二个为加密方式 AES或者DES
      SecretKeySpec key = new SecretKeySpec(ASE_KEY.getBytes(), "AES");
      //实例化加密类，参数为加密方式，要写全
      Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding"); //PKCS5Padding比PKCS7Padding效率高，PKCS7Padding可支持IOS加解密
      //初始化，此方法可以采用三种方式，按加密算法要求来添加。（1）无第三个参数（2）第三个参数为SecureRandom random = new SecureRandom();中random对象，随机数。(AES不可采用这种方法)（3）采用此代码中的IVParameterSpec
      cipher.init(Cipher.ENCRYPT_MODE, key, zeroIv);
      //加密后的字节数组
      byte[] encryptedData = cipher.doFinal(cleartext.getBytes(bm));
      //对加密后的字节数组进行base64编码
      byte[] base64Data = org.apache.commons.codec.binary.Base64.encodeBase64(encryptedData);
      //将base64编码后的字节数组转化为字符串并返回
      return new String(base64Data);
    } catch (Exception e) {
      e.printStackTrace();
      return "";
    }
  }

  /**
   *  Aes解密
   * @param encrypted 加密串
   * @return
   */
  public static String decrypt(String encrypted) {
    try {
      //---------------------------------------base64解码---------------------------------------
      //将字符串转化为base64编码的字节数组
      byte[] encryptedBase64Bytes = encrypted.getBytes();
      //将base64编码的字节数组转化为在加密之后的字节数组
      byte[] byteMi = org.apache.commons.codec.binary.Base64.decodeBase64(encryptedBase64Bytes);
      IvParameterSpec zeroIv = new IvParameterSpec(VIPARA.getBytes());
      SecretKeySpec key = new SecretKeySpec(
          ASE_KEY.getBytes(), "AES");
      Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
      //与加密时不同MODE:Cipher.DECRYPT_MODE
      cipher.init(Cipher.DECRYPT_MODE, key, zeroIv);
      byte[] decryptedData = cipher.doFinal(byteMi);
      return new String(decryptedData,bm);
    } catch (Exception e) {
      log.error(e.toString());
      return "";
    }
  }

/*  public static void main(String[] args) {
   System.out.println(decrypt("gzj09HVZBzHrPEY3rMvTxH2vOF+jyLAjxbmsUfECxno="));
  }*/

}
