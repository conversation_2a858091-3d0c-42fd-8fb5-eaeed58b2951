package com.xhgj.srm.common.domain;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotBlank;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023-03-06 16:21
 */
@Data
public class ExportOrderInvoiceParams {

  @ApiModelProperty(value = "用户 id", required = true)
  @NotBlank(message = "用户 id 必传")
  private String userId;

  @ApiModelProperty(value = "组织编码", required = true)
  @NotBlank(message = "组织编码 必传")
  private String userGroup;
  @ApiModelProperty("方案 id")
  private String schemeId;
  @ApiModelProperty("发票申请id")
  private List<String> orderIdList;
  @ApiModelProperty("客户订单号")
  private String orderNo;
  @ApiModelProperty("订单状态")
  private String orderState;
  @ApiModelProperty("下单平台")
  private String platform;
  @ApiModelProperty("下单金额")
  private String price;
  @ApiModelProperty("客户名称")
  private String customer;
  @ApiModelProperty("下单时间")
  private String createTime;
  @ApiModelProperty("开始时间")
  private String createTimeBegin;
  @ApiModelProperty("结束时间")
  private String createTimeEnd;
  @ApiModelProperty("开票状态")
  private List<String> invoicingState;
  @ApiModelProperty("发票类型")
  private String invoiceType;
  @ApiModelProperty("发票抬头")
  private String title;
  @ApiModelProperty("申请人")
  private String enterPriseName;
  @ApiModelProperty("开票申请单号")
  private String invoiceApplicationNum;


}
