package com.xhgj.srm.common.utils;

import cn.hutool.core.util.StrUtil;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTJc;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTP;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTPPr;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTc;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2023-03-26 15:35
 */
@Slf4j
public class WordUtil {


  /**
   * 替换表格中的占位符
   */
  public static void replaceTable(XWPFDocument doc, Map<String, String> params,String fontType) {
    // 获取文档中所有的表格
    Iterator<XWPFTable> iterator = doc.getTablesIterator();
    XWPFTable table;
    List<XWPFTableRow> rows;
    List<XWPFTableCell> cells;
    List<XWPFParagraph> paras;
    while (iterator.hasNext()) {
      table = iterator.next();
      if (table.getRows().size() > 1) {
        //判断表格是需要替换还是需要插入，判断逻辑有${为替换，
        rows = table.getRows();
        for (XWPFTableRow row : rows) {
          cells = row.getTableCells();
          for (XWPFTableCell cell : cells) {
            String text = cell.getText();
            if (matcher(text).find()) {
              paras = cell.getParagraphs();
              for (XWPFParagraph para : paras) {
                replaceInParaTable(para, params,fontType);
              }
            }
          }
        }
      }
    }
  }

  /**
   * 替换段落里面的变量
   * @param doc 要替换的文档
   * @param params 参数
   */
  public static void replaceInPara(XWPFDocument doc, Map<String, String> params) {
    Iterator<XWPFParagraph> iterator = doc.getParagraphsIterator();
    XWPFParagraph para;
    while (iterator.hasNext()) {
      para = iterator.next();
      replaceInPara(para, params);
    }
  }

  /**
   * 替换段落里面的变量
   *
   * @param para   要替换的段落
   * @param params 参数
   */
  public static void replaceInPara(XWPFParagraph para, Map<String, String> params) {
    List<XWPFRun> runs;
    Matcher matcher;
    String runText = "";
    int fontSize = 0;
    UnderlinePatterns underlinePatterns = null;
    String paragraphText = para.getParagraphText();
    if (matcher(para.getParagraphText()).find()) {
      runs = para.getRuns();
      if (runs.size() > 0) {
        int j = runs.size();
        for (int i = 0; i < j; i++) {
          XWPFRun run = runs.get(0);
          if (fontSize == 0) {
            fontSize = run.getFontSize();
          }
          if(underlinePatterns==null){
            underlinePatterns=run.getUnderline();
          }
          String i1 = run.toString();
          runText += i1;
          para.removeRun(0);
        }
      }
      matcher = matcher(runText);
      if (matcher.find()) {
        while ((matcher = matcher(runText)).find()) {
          String group = matcher.group(1);
          runText = matcher.replaceFirst(String.valueOf(params.get("${"+matcher.group(1)+"}")));
        }
        //直接调用XWPFRun的setText()方法设置文本时，在底层会重新创建一个XWPFRun，把文本附加在当前文本后面，
        //所以我们不能直接设值，需要先删除当前run,然后再自己手动插入一个新的run。
        //para.insertNewRun(0).setText(runText);//新增的没有样式

        XWPFRun run = para.createRun();
        run.setText(runText,0);
        run.setFontSize(fontSize);
        run.setUnderline(underlinePatterns);
        run.setFontFamily("黑体");//字体
        run.setFontSize(9);//字体大小
        run.setBold(true); //加粗
        //run.setColor("FF0000");
        //默认：宋体（wps）/等线（office2016） 5号 两端对齐 单倍间距
        //run.setBold(false);//加粗
        //run.setCapitalized(false);//我也不知道这个属性做啥的
        //run.setCharacterSpacing(5);//这个属性报错
        //run.setColor("BED4F1");//设置颜色--十六进制
        //run.setDoubleStrikethrough(false);//双删除线
        //run.setEmbossed(false);//浮雕字体----效果和印记（悬浮阴影）类似
        //run.setFontFamily("宋体");//字体
        //run.setFontFamily("华文新魏", FontCharRange.cs);//字体，范围----效果不详
        //run.setFontSize(14);//字体大小
        //run.setImprinted(false);//印迹（悬浮阴影）---效果和浮雕类似
        //run.setItalic(false);//斜体（字体倾斜）
        //run.setKerning(1);//字距调整----这个好像没有效果
        //run.setShadow(true);//阴影---稍微有点效果（阴影不明显）
        //run.setSmallCaps(true);//小型股------效果不清楚
        //run.setStrike(true);//单删除线（废弃）
        //run.setStrikeThrough(false);//单删除线（新的替换Strike）
        //run.setSubscript(VerticalAlign.SUBSCRIPT);//下标(吧当前这个run变成下标)---枚举
        //run.setTextPosition(20);//设置两行之间的行间距
        //run.setUnderline(UnderlinePatterns.DASH_LONG);//各种类型的下划线（枚举）
        //run0.addBreak();//类似换行的操作（html的  br标签）
        //run0.addTab();//tab键
        //run0.addCarriageReturn();//回车键
        //注意：addTab()和addCarriageReturn() 对setText()的使用先后顺序有关：比如先执行addTab,再写Text这是对当前这个Text的Table，反之是对下一个run的Text的Tab效果


      }
    }

  }
  public static void setRowsText(XWPFTableRow row,String ...params){
    List<XWPFTableCell> tableCells = row.getTableCells();
    for (int i = 0; i < tableCells.size(); i++) {
      XWPFTableCell tableCell = tableCells.get(i);
      if (StrUtil.isBlank(tableCell.getText())) {
        XWPFParagraph xwpfParagraph = tableCell.getParagraphs().get(0);
        setText(xwpfParagraph,params[i],"accpetTemp");
      }
    }
  }

  /**
   * 水平居中垂直居中
   * @param row
   * @param params
   */
  public static void setRowsTextCenter(XWPFTableRow row,String ...params){
    List<XWPFTableCell> tableCells = row.getTableCells();
    for (int i = 0; i < tableCells.size(); i++) {
      XWPFTableCell tableCell = tableCells.get(i);
      if (StrUtil.isBlank(tableCell.getText())) {
        XWPFParagraph xwpfParagraph = tableCell.getParagraphs().get(0);
        // 设置文本内容和样式
        setText(xwpfParagraph, params[i], "accpetTemp");

        // 设置居中对齐
        // 设置水平方向居中
        xwpfParagraph.setAlignment(ParagraphAlignment.CENTER);

        // 设置垂直方向居中
        tableCell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
      }
    }
  }

  /**
   * 添加表格行
   * @param table 表格
   * @param source 复制的原表格行
   * @param rows 需要几行
   * @param insertRowIndex 从第几行插入
   */
  public static void addRows(XWPFTable table, int source, int rows, int insertRowIndex){
    try{
      //获取表格的总行数
      int index = table.getNumberOfRows();
      //循环添加行和和单元格
      for(int i=1;i<=rows;i++) {
        //获取要复制样式的行
        XWPFTableRow sourceRow = table.getRow(source);
        //添加新行
        XWPFTableRow targetRow = table.insertNewTableRow(insertRowIndex++);
        //复制行的样式给新行
        targetRow.getCtRow().setTrPr(sourceRow.getCtRow().getTrPr());
        //获取要复制样式的行的单元格
        List<XWPFTableCell> sourceCells = sourceRow.getTableCells();
        //循环复制单元格
        for (XWPFTableCell sourceCell : sourceCells) {
          //添加新列
          XWPFTableCell newCell = targetRow.addNewTableCell();
          //复制单元格的样式给新单元格
          newCell.getCTTc().setTcPr(sourceCell.getCTTc().getTcPr());
          //设置垂直居中
          newCell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);//垂直居中
          //复制单元格的居中方式给新单元格
          CTPPr pPr = sourceCell.getCTTc().getPList().get(0).getPPr();
          if(pPr!=null&&pPr.getJc()!=null&&pPr.getJc().getVal()!=null){
            CTTc cttc = newCell.getCTTc();
            CTP ctp = cttc.getPList().get(0);
            CTPPr ctppr = ctp.getPPr();
            if (ctppr == null) {
              ctppr = ctp.addNewPPr();
            }
            CTJc ctjc = ctppr.getJc();
            if (ctjc == null) {
              ctjc = ctppr.addNewJc();
            }
            ctjc.setVal(pPr.getJc().getVal()); //水平居中
          }

          //得到复制单元格的段落
          List<XWPFParagraph> sourceParagraphs = sourceCell.getParagraphs();
          if (StringUtils.isEmpty(sourceCell.getText())) {
            continue;
          }
          //拿到第一段
          XWPFParagraph sourceParagraph = sourceParagraphs.get(0);
          //得到新单元格的段落
          List<XWPFParagraph> targetParagraphs = newCell.getParagraphs();
          //判断新单元格是否为空
          if (StringUtils.isEmpty(newCell.getText())) {
            //添加新的段落
            XWPFParagraph ph = newCell.addParagraph();
            //复制段落样式给新段落
            ph.getCTP().setPPr(sourceParagraph.getCTP().getPPr());
            //得到文本对象
            XWPFRun run = ph.getRuns().isEmpty() ? ph.createRun() : ph.getRuns().get(0);
            //复制文本样式
            run.setFontFamily(sourceParagraph.getRuns().get(0).getFontFamily());
          } else {
            XWPFParagraph ph = targetParagraphs.get(0);
            ph.getCTP().setPPr(sourceParagraph.getCTP().getPPr());
            XWPFRun run = ph.getRuns().isEmpty() ? ph.createRun() : ph.getRuns().get(0);
            run.setFontFamily(sourceParagraph.getRuns().get(0).getFontFamily());
          }
        }
      }
    }catch (Exception e){
      log.error(e.getMessage(),e);
    }
  }


  /**
   * 替换段落里面的变量（这里因为表格的特殊性，这里只适用于表格）
   * @param para 要替换的段落
   * @param params 参数
   */
  private static void replaceInParaTable(XWPFParagraph para, Map<String, String> params,String fontType) {
    String paragraphText = para.getParagraphText();
    if (matcher(paragraphText).find()) {
      List<XWPFRun> runs = para.getRuns();
      for (int i = runs.size()-1; i >= 0 ; i--) {
        // 直接调用XWPFRun的setText()方法设置文本时，在底层会重新创建一个XWPFRun，把文本附加在当前文本后面，
        // 所以我们不能直接设值，需要先删除当前run,然后再自己手动插入一个新的run。
        para.removeRun(i);
      }
      setText(para, params.get(paragraphText), fontType);
    }
  }

  private static void setText(XWPFParagraph para, String runText, String fontType) {
    XWPFRun runX = para.insertNewRun(0);
    runX.setText(runText);
    // 设置字体样式
    if ("accpetTemp".equals(fontType)) {
      runX.setFontFamily("宋体");
      runX.setFontSize(10);
      runX.setBold(false);
    }
    if ("supplierAccpetTemp".equals(fontType)) {
      runX.setFontFamily("宋体");
      runX.setFontSize(12);
      runX.setBold(true);
    }
  }

  /**
   * 合并runs中的内容
   * @param para 要替换的段落
   * @return
   */
  private static List<XWPFRun> replaceText(XWPFParagraph para) {
    List<XWPFRun> runs = para.getRuns();
    String str = "";
    boolean flag = false;
    for (int i = 0; i < runs.size(); i++) {
      XWPFRun run = runs.get(i);
      String runText = run.toString();
      if (flag || runText.equals("${")) {
        str = str + runText;
        flag = true;
        para.removeRun(i);
        if (runText.equals("}")) {
          flag = false;
          para.insertNewRun(i).setText(str);
          str = "";
        }
        i--;
      }
    }
    return runs;
  }


  /**
   * 正则匹配字符串
   */
  private static Matcher matcher(String str) {
    Pattern pattern = Pattern.compile("\\$\\{(.+?)\\}", Pattern.CASE_INSENSITIVE);
    Matcher matcher = pattern.matcher(str);
    return matcher;
  }

  /**
   * 删除表格中的空白行
   */
  public static void removeEmptyRows(XWPFTable table,int index) {
    List<XWPFTableRow> rows = table.getRows();
    for (int i = rows.size() - 1; i >= index; i--) {
      XWPFTableRow row = rows.get(i);
      if (row.getTableCells().stream().allMatch(cell -> StrUtil.isBlank(cell.getText()))) {
        table.removeRow(i);
      }
    }
  }
}
