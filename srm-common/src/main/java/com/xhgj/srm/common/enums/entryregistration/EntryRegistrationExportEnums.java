package com.xhgj.srm.common.enums.entryregistration;

import cn.hutool.core.collection.ListUtil;
import java.util.List;

/**
 * <AUTHOR>
 */
public enum EntryRegistrationExportEnums {
  /**
   * 常规导出
   */
  NORMAL_EXPORT(
      ListUtil.toList("报备单号", "报备单状态", "业务员", "报备时间","项目大类", "项目全称", "平台名称",
          "合作类型", "合作单位名称", "合作时间", "合作品牌", "合作区域", "合作比例", "合作联系人",
          "联系电话", "职务", "联系地址", "保证金", "账期", "付款条件", "付款方式", "发票类型",
          "税率", "仓储", "保底金额", "SCP账号使用人", "手机号", "邮箱", "准入说明"),
      "srm/model/供应商入驻导出6.7.0.xlsx",
      "srm/upload/batch", "入驻报备单%s.xlsx"),;

  EntryRegistrationExportEnums(List<String> tempFields, String tempFileUrl, String uploadPath,
                               String fileName) {
    this.uploadPath = uploadPath;
    this.tempFields = tempFields;
    this.tempFileUrl = tempFileUrl;
    this.fileName = fileName;
  }

  public List<String> getTempFields() {
    return tempFields;
  }

  public String getTempFileUrl() {
    return tempFileUrl;
  }

  public String getFileName() {
    return fileName;
  }

  public String getUploadPath() {
    return uploadPath;
  }

  private List<String> tempFields;

  private String tempFileUrl;

  private String uploadPath;

  /**
   * 文件名
   */
  private String fileName;
}
