package com.xhgj.srm.common.domain.dingding;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/8/31 15:49
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class DingTalkRobotCarMasParam {

  /**
   * 标题
   */
  private String title;

  /**
   * 内容
   */
  private String text;

  /** 链接标题 */
  private String singleTitle;
  /** 链接 */
  private String singleURL;
  /** 链接标题1 */
  private String actionTitle1;

  private String actionURL1;
  /** 链接标题2 */
  private String actionTitle2;

  private String actionURL2;
  /** 链接标题3 */
  private String actionTitle3;

  private String actionURL3;
  /** 链接标题4 */
  private String actionTitle4;

  private String actionURL4;
  /** 链接标题5 */
  private String actionTitle5;

  private String actionURL5;
  /** 链接标题6 */
  private String actionTitle6;

  private String actionURL6;
}
