package com.xhgj.srm.common.utils.runner;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;
import redis.clients.jedis.JedisPool;

/**
 * JedisUtil 初始化
 *
 * <AUTHOR>
 * @since 2020/9/16 14:41
 */
@Component
@Slf4j
public class JedisUtilInitRunner implements ApplicationRunner {
  @Autowired JedisPool jedisPool;

  @Override
  public void run(ApplicationArguments args) {
    log.info("=== initJedisUtil...");
    JedisUtil.setJedisPool(jedisPool);
    JedisUtil.setInstance(new JedisUtil());
    log.info("=== initJedisUtil successfully");
  }
}
