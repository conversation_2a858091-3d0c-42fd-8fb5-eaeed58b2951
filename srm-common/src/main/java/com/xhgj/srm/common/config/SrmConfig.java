package com.xhgj.srm.common.config;

import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2021/2/23 16:34
 */
@Configuration
@Data
@RefreshScope
@ConfigurationProperties(prefix = "srm")
public class SrmConfig {

  private String uploadUrl;
  private String apiUrl;
  private String sysShortName;
  private String bucketNameFtp;
  private String meetingTempUrl;
  private String batchUrl;
  private String bucketName;
  private String universalCode;
  private String dockUrl;
  private String platformOrderUrl;

  private String omsOrderUrl;
  private String omsUrl;
  private String omsPlatformUrl;
  private String omsPlatformPortalUrl;
  private String platformUrl;
  private String mdmCustomerPlatformUrl;

  /** 钉钉机器人的 appkey */
  private String dingTalkRobotAppKey;
  /** 钉钉Key */
  private String appKey;
  /** 钉钉秘钥 */
  private String appSecret;
  /** 跳转到 srm 供应商订单入库 */
  private String stockInputUrl;
  /** srm manage服务 */
  private String srmManageUrl;
  /** 跳转到 srm 供应商订单修改 */
  private String updateSupplierOrderReturnUrl;
  /** oms admin 地址 */
  private String omsAdminUrl;
  /** mdm 审批事件 地址 */
  private String mdmApprovalEventUrl;
  /** 申请付款通知人的手机号 */
  private List<String> paymentNoticeList;
  /** 订单对账钉钉通知手机号 */
  private List<String> accountNoticeList;
  /** SRM 后台登录地址 */
  private String srmAdminLogin;
  /** SRM 进项票详情 */
  private String srmInputTicketDetail;
  /** 确认通过进项票回调 key */
  private String orderOpenInvoiceCallBackKey;
  /** 拒单回调 key */
  private String refuseOrderCallbackKey;
  /** 前往进项票的 H5 */
  private String orderOpenInvoiceH5Url;
  /** 前往进项票钉钉卡片的 H5 */
  private String orderSupplierInvoiceH5Url;
  /** 前往订单合同 H5 */
  private String contractFileH5Url;
  /** 发送进项开票的新版通知和待办 */
  private List<String> orderOpenInvoiceDingTaskAndNewNotice;
  /** 发送进项开票的新版通知和待办 */
  private Map<String,String> orderOpenInvoiceDingTaskAndNewNoticeMap;
  /** 老进项开票的通知固定发送人 */
  private List<String> orderOpenInvoiceDingOldNoticeFixed;
  /**
   * 报备单钉钉审批发起人（侯夏琳的钉钉id）
   */
  private String dingTalkApprovalOriginatorUserId;
  /**
   * 订单报备单的模板Code
   */
  private String dingTalkApprovalTemplateOrder;
  /**
   * 商品报备单的模板Code
   */
  private String dingTalkApprovalTemplateProduct;

  /**
   * 采购价格库审批模板
   */
  private String dingTalkApprovalTemplatePurchaseInfoRecord;

  /**
   * 供应商订单超时钉钉提醒
   */
  private String dingTalkSupplierOrderTimeoutReminder;

  /**
   * 供应商审核的模板Code
   */
  private String dingTalkApprovalTemplateSupplier;
  /**
   * 合同附件上传审核人
   */
  private Set<String> contractFileUploadExamineUserCode;
  /**
   * 默认采购 中文名
   */
  private String defaultPurchase;
}
