package com.xhgj.srm.common.utils;

import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhiot.boot.core.common.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

@Slf4j
@Component
public class MissionUtil {
  private final Lock lock = new ReentrantLock();
    public String getMissionCode(String code) {
      lock.lock();
      try {
        String uCode = StrUtil.isNotBlank(code) ? code : Constants.USERNAME;
        return "RW" + uCode + DateUtils.formatTimeStampToPureDateTime(System.currentTimeMillis());
      } finally {
        lock.unlock();
      }
    }
}
