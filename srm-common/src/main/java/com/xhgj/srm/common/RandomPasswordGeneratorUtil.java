package com.xhgj.srm.common;

import java.security.SecureRandom;

/**
 * <AUTHOR>
 */
public class RandomPasswordGeneratorUtil {
  // 定义字符集：大写字母、小写字母、数字
  private static final String CHARACTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";

  // SecureRandom 提供更好的随机性
  private static final SecureRandom random = new SecureRandom();

  public static String generateRandomPassword(int length) {
    StringBuilder password = new StringBuilder(length);

    // 循环生成指定长度的密码
    for (int i = 0; i < length; i++) {
      int randomIndex = random.nextInt(CHARACTERS.length());
      password.append(CHARACTERS.charAt(randomIndex));
    }

    return password.toString();
  }

  public static void main(String[] args) {
    // 生成6位随机密码
    String password = generateRandomPassword(6);
    System.out.println("随机生成的密码: " + password);
  }
}
