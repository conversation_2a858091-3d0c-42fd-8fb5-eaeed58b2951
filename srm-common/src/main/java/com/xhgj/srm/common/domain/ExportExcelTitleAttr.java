package com.xhgj.srm.common.domain;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/3/5
 */
@Data
public class ExportExcelTitleAttr {

  /**
   * 单元格表头内容
   */
  private String title;

  /**
   * 标题单元格宽
   */
  private int cellWidth;

  /**
   * 该标题是否需要合并
   */
  private boolean needRegion;

  /**
   * 背景颜色
   */
  private short backgroundColor;
  /**
   * 需要合并时的起始行号 从0开始算 包括本行
   */
  private Integer bIndex;

  /**
   * 需要合并时的结束行号 从0开始算 包括本行
   */
  private Integer eIndex;

  /**
   * 需要合并时的开始列号，从0开始算 包括本列
   */
  private Integer bCol;

  /**
   * 需要合并时的结束行号，从0开始算 包括本列
   */
  private Integer eCol;

  /**
   * 所在的行号 如果需要合并只填最小的行号
   */
  private int rowIndex;

  /**
   * 所在的列号 如果需要合并只填最小的列号
   */
  private int colIndex;


  /**
   * 需要设置合并的才会设置行号和列号，所以needRegion默认为true
   * @param title 单元格表头内容
   * @param cellWidth 标题单元格宽
   * @param backgroundColor 背景颜色
   * @param bIndex 需要合并时的起始行号
   * @param eIndex 需要合并时的结束行号
   * @param bCol 需要合并时的开始列号
   * @param eCol 需要合并时的结束行号
   * @param rowIndex 所在的行号
   * @param colIndex 所在的列号
   */
  public ExportExcelTitleAttr(String title, int cellWidth, short backgroundColor, Integer bIndex,
      Integer eIndex, Integer bCol, Integer eCol, int rowIndex, int colIndex) {
    this.title = title;
    this.cellWidth = cellWidth;
    this.needRegion = eCol - bCol >= 1;
    this.backgroundColor = backgroundColor;
    this.bIndex = bIndex;
    this.eIndex = eIndex;
    this.bCol = bCol;
    this.eCol = eCol;
    this.rowIndex = rowIndex;
    this.colIndex = colIndex;
  }

  /**
   * 不设置合并的默认将needRegion设为false
   * @param title 单元格表头内容
   * @param cellWidth 标题单元格宽
   * @param backgroundColor 背景颜色
   * @param rowIndex 所在的行号
   * @param colIndex 所在的列号
   */
  public ExportExcelTitleAttr(String title, int cellWidth, short backgroundColor, int rowIndex, int colIndex) {
    this.title = title;
    this.cellWidth = cellWidth;
    this.needRegion = false;
    this.backgroundColor = backgroundColor;
    this.rowIndex = rowIndex;
    this.colIndex = colIndex;
  }
}
