package com.xhgj.srm.common.utils;

import com.xhgj.srm.common.dto.UnzipFileVO;
import lombok.extern.slf4j.Slf4j;
import java.io.BufferedOutputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

/**
 * 解析文件工具类
 *
 * <AUTHOR>
 * @since 2022/7/12
 */
@Slf4j
public class UnZipUtils {

  /**
   * 获取指定文件夹下所有文件，不含文件夹里的文件
   *
   * @param dirFile 文件夹
   * @return
   */
  public static List<File> getAllFile(File dirFile) {
    // 如果文件夹不存在或着不是文件夹，则返回 null
    if (Objects.isNull(dirFile) || !dirFile.exists() || dirFile.isFile())
      return null;

    File[] childrenFiles = dirFile.listFiles();
    if (Objects.isNull(childrenFiles) || childrenFiles.length == 0)
      return null;

    List<File> files = new ArrayList<>();
    for (File childFile : childrenFiles) {
      // 如果是文件，直接添加到结果集合
      if (childFile.isFile()) {
        files.add(childFile);
      }
      //以下几行代码取消注释后可以将所有子文件夹里的文件也获取到列表里
      //            else {
      //                // 如果是文件夹，则将其内部文件添加进结果集合
      //                List<File> cFiles = getAllFile(childFile);
      //                if (Objects.isNull(cFiles) || cFiles.isEmpty()) continue;
      //                files.addAll(cFiles);
      //            }
    }
    return files;
  }


  public static Map<String, List<UnzipFileVO>> unzip(InputStream inputStream) throws IOException {
    Map<String, List<UnzipFileVO>> zipMap = new HashMap<>();
    if (Objects.isNull(inputStream)) {
      return zipMap;
    }
    try (ZipInputStream zipInputStream = new ZipInputStream(inputStream, Charset.forName("GBK"))) {
      ZipEntry entry;
      while (true) {
        try {
          ZipEntry nextEntry = zipInputStream.getNextEntry();
          if (nextEntry == null) {
            return zipMap;
          }
          entry = nextEntry;
        } catch (Exception e) {
          log.error("解压文件异常", e);
          return zipMap;
        }
        // 如果是目录，继续解压目录内部的内容，但不把目录本身存储
        if (entry.isDirectory()) {
          // 目录本身不做处理，但继续解压目录下的文件
          zipInputStream.closeEntry();
          continue;
        }
        String entryName = entry.getName();
        String parentDir = getParentDirectory(entryName);
        String fileName = entryName.substring(entryName.lastIndexOf("/") + 1);
        // Process file entry
        File tempFile = createTempFile(zipInputStream);
        UnzipFileVO unzipFileVO = new UnzipFileVO(1, fileName, tempFile, parentDir);
        zipMap.computeIfAbsent(parentDir, k -> new ArrayList<>()).add(unzipFileVO);
        zipInputStream.closeEntry();
      }
    }
  }

  private static String getParentDirectory(String entryName) {
    // 倒数第一个斜杆 + 倒数第二个斜杆之间的内容即为父目录
    int lastSlashIndex = entryName.lastIndexOf("/");
    if (lastSlashIndex == -1) {
      return "";
    }
    int secondLastSlashIndex = entryName.substring(0, lastSlashIndex).lastIndexOf("/");
    if (secondLastSlashIndex == -1) {
      // 如果没有第二个斜杆，则取第一个斜杆之前的内容
      return entryName.substring(0, lastSlashIndex);
    }
    return entryName.substring(secondLastSlashIndex + 1, lastSlashIndex);
  }

  private static File createTempFile(ZipInputStream zipInputStream) throws IOException {
    // Create a temporary file
    File tempFile = File.createTempFile("unzipped-", ".tmp");
    tempFile.deleteOnExit(); // Ensure the temporary file is deleted on exit

    try (BufferedOutputStream outputStream = new BufferedOutputStream(new FileOutputStream(tempFile))) {
      byte[] buffer = new byte[1024];
      int length;
      while ((length = zipInputStream.read(buffer)) > 0) {
        outputStream.write(buffer, 0, length);
      }
    }
    return tempFile;
  }

  private static byte[] readZipEntry(ZipInputStream zipInputStream) throws IOException {
    // Use a ByteArrayOutputStream to collect the data
    ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
    byte[] buffer = new byte[1024];
    int length;
    while ((length = zipInputStream.read(buffer)) > 0) {
      byteArrayOutputStream.write(buffer, 0, length);
    }
    return byteArrayOutputStream.toByteArray(); // Convert the collected data to a byte array
  }
}
