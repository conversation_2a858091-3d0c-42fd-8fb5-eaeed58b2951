package com.xhgj.srm.common.config;

import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.config.BootConfig;
import java.io.IOException;
import java.util.Objects;
import javax.annotation.Resource;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;

/**
 * Created by Geng Shy on 2023/11/21
 */
@Configuration
public class RedissonConfig {

  @Resource
  private BootConfig bootConfig;

  @Bean
  public RedissonClient redisson() throws IOException {
    // TODO 该代码需要优化
    final String yaml_file_name_test = "redisson-single-test.yml";
    final String yaml_file_name_pre = "redisson-single-pre.yml";
    final String yaml_file_name_prod = "redisson-single-prod.yml";
    final String env_test = "test";
    final String env_pre = "pre";
    final String env_prod = "prod";
    String env = bootConfig.getEnv();
    String yamlFileName = null;
    if (Objects.equals(env, env_test)) {
      yamlFileName = yaml_file_name_test;
    }
    if (Objects.equals(env, env_pre)) {
      yamlFileName = yaml_file_name_pre;
    }
    if (Objects.equals(env, env_prod)) {
      yamlFileName = yaml_file_name_prod;
    }
    if (yamlFileName == null) {
      throw new CheckException("未配置Redisson配置文件");
    }
    return Redisson.create(Config.fromYAML(new ClassPathResource(yamlFileName).getInputStream()));
  }
}
