package com.xhgj.srm.common.config;

import java.util.List;
import java.util.stream.Stream;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.MapperConfig;

/**
 * mapstruct配置中心
 * SOURCE 指定源对象属性 TARGET指定目标对象属性
 *
 * 设置nullValueMappingStrategy = NullValueMappingStrategy.RETURN_DEFAULT
 *   |--> 控制 '空' 参数的映射结果 : 默认情况下 null 会返回
 * @param <SOURCE>
 * @param <TARGET>
 */
@MapperConfig()
public interface BaseMappingConfig<SOURCE, TARGET> {
	/**
	 * 映射同名属性
	 */
	TARGET sourceToTarget(SOURCE var1);

	/**
	 * 反向，映射同名属性
	 */
	@InheritInverseConfiguration(name = "sourceToTarget")
	SOURCE targetToSource(TARGET var1);

	/**
	 * 映射同名属性，集合形式
	 */
	@InheritConfiguration(name = "sourceToTarget")
	List<TARGET> sourceToTarget(List<SOURCE> var1);

	/**
	 * 反向，映射同名属性，集合形式
	 */
	@InheritConfiguration(name = "targetToSource")
	List<SOURCE> targetToSource(List<TARGET> var1);

	/**
	 * 映射同名属性，集合流形式
	 */
	List<TARGET> sourceToTarget(Stream<SOURCE> stream);

	/**
	 * 反向，映射同名属性，集合流形式
	 */
	List<SOURCE> targetToSource(Stream<TARGET> stream);
}
