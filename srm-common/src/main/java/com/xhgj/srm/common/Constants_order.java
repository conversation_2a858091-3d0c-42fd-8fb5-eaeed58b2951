package com.xhgj.srm.common;

import cn.hutool.core.util.StrUtil;
import com.xhiot.boot.core.common.util.StringUtils;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import org.apache.commons.collections4.BidiMap;
import org.apache.commons.collections4.bidimap.DualHashBidiMap;

public class Constants_order {

  /**
   * 接单通知
   */
  public static final String ORDER_NOTCIE_ORDER_RECEIVING = "您有新的供应商订单{}待接单！";
  public static final String ORDER_NOTCIE_ORDER_RECEIVING_LANDING = "您有新的落地商订单{}待接单！";
  /**
   * 催单通知
   */
  public static final String ORDER_NOTCIE_ORDER_REMINDER = "您有新的供应商订单{}长时间未确认！";
  /**
   * 退货通知
   */
  public static final String ORDER_NOTCIE_ORDER_RETURN = "您有新的供应商订单{}有退货单生成！";
  /**
   * 取消通知
   */
  public static final String ORDER_NOTCIE_ORDER_CANCEL = "您有新的供应商订单{}有取消单生成！";

  /**
   * 提醒收货通知
   */
  public static final String ORDER_NOTCIE_REMIND_RECEIPT = "{}退货单请及时确认收货！";

  /**
   * 订单信息有调整
   */
  public static final String ORDER_NOTCIE_UPDATE_ORDER_INFO = "{}订单信息有调整！";

  /**
   * 物料调价驳回
   */
  public static final String PRODUCT_PRICE_REJECT = "{}物料调价驳回！";

  /**
   * 订单状态
   */
  public static final TreeMap<String, String> ORDER_STATE_MAP = new TreeMap<>();
  public static final String ORDER_STATE_WAIT = "1"; //待履约
  public static final String ORDER_STATE_PROCESS = "2"; //履约中
  public static final String ORDER_STATE_ACCEPTING = "3"; //待验收
  public static final String ORDER_STATE_ACCEPTED = "4"; //已完成
  public static final String ORDER_STATE_CANCEL = "5"; //已取消
  // 20230201 SCP5.0.0 去掉已退货字段
  public static final String ORDER_STATE_RETURN = "6"; //已退货
  public static final String ORDER_STATE_WITHDRAW = "7"; //已撤回

  static {
    ORDER_STATE_MAP.put(ORDER_STATE_WAIT, "待履约");
    ORDER_STATE_MAP.put(ORDER_STATE_PROCESS, "履约中");
    ORDER_STATE_MAP.put(ORDER_STATE_ACCEPTING, "待验收");
    ORDER_STATE_MAP.put(ORDER_STATE_ACCEPTED, "已完成");
    ORDER_STATE_MAP.put(ORDER_STATE_CANCEL, "已取消");
    ORDER_STATE_MAP.put(ORDER_STATE_RETURN, "已退货");
    ORDER_STATE_MAP.put(ORDER_STATE_WITHDRAW, "已撤回");
  }

  /**
   * 订单查询状态
   */
  public static final String ORDER_SEARCH_STATE_ALL = "0"; //完成
  public static final String ORDER_SEARCH_STATE_NONE = "100"; //不存在
  public static final String ORDER_SEARCH_STATE_PENDING_AND_IN_PROGRESS = "5"; //待履约和履约中


  /**
   * 发货单状态
   */
  public static final TreeMap<String, String> DELIVERY_STATE_MAP = new TreeMap<>();
  public static final String DELIVERY_STATE_WAIT = "1"; //待收货
  public static final String DELIVERY_STATE_DONE = "2"; //已签收

  static {
    DELIVERY_STATE_MAP.put(DELIVERY_STATE_WAIT, "待收货");
    DELIVERY_STATE_MAP.put(DELIVERY_STATE_DONE, "已签收");
  }

  /**
   * 客户开票状态
   */
  public static final TreeMap<String, String> INVOICE_STATE_MAP = new TreeMap<>();
  /**
   * 已开票
   */
  public static final String INVOICE_STATE_DONE = "1"; //

  /**
   * 未开票
   */
  public static final String INVOICE_STATE_UN = "2"; //
  /**
   * 开票中
   */
  public static final String INVOICE_STATE_HAND = "3";

  static {
    INVOICE_STATE_MAP.put(INVOICE_STATE_DONE, "已开票");
    INVOICE_STATE_MAP.put(INVOICE_STATE_UN, "未开票");
    INVOICE_STATE_MAP.put(INVOICE_STATE_HAND, "开票中");
  }

  /**
   * 回款进度
   */
  public static final TreeMap<String, String> CUSTOMER_RECEIVABLE_RETURN_PROGRESS = new TreeMap<>();
  /**
   * 未回款
   */
  public static final String RETURN_PROGRESS_NO = "0";

  /**
   * 部分回款
   */
  public static final String RETURN_PROGRESS_PART = "1";
  /**
   * 全部回款
   */
  public static final String RETURN_PROGRESS_ALL = "2";

  static {
    CUSTOMER_RECEIVABLE_RETURN_PROGRESS.put(RETURN_PROGRESS_NO, "未回款");
    CUSTOMER_RECEIVABLE_RETURN_PROGRESS.put(RETURN_PROGRESS_PART, "部分回款");
    CUSTOMER_RECEIVABLE_RETURN_PROGRESS.put(RETURN_PROGRESS_ALL, "全部回款");
  }

  /**
   * 客户回款
   */
  public static final TreeMap<String, String> CUSTOMER_PAYBACK_MAP = new TreeMap<>();
  /**
   * 未完成
   */
  public static final String CUSTOMER_PAYBACK_UN = "1";
  /**
   * 已完成
   */
  public static final String CUSTOMER_PAYBACK_CONFIRM = "2";

  static {
    CUSTOMER_PAYBACK_MAP.put(CUSTOMER_PAYBACK_CONFIRM, "已完成");
    CUSTOMER_PAYBACK_MAP.put(CUSTOMER_PAYBACK_UN, "未完成");
  }

  /**
   * 回款进度 -> 客户回款状态映射表
   */
  public static final TreeMap<String, String> CUSTOMER_RECEIVABLE_RETURN_PROGRESS_TO_PAYBACK_MAP = new TreeMap<>();

  static {
    // 未回款 -> 未完成
    CUSTOMER_RECEIVABLE_RETURN_PROGRESS_TO_PAYBACK_MAP.put(RETURN_PROGRESS_NO, CUSTOMER_PAYBACK_UN);
    // 部分回款 -> 未完成
    CUSTOMER_RECEIVABLE_RETURN_PROGRESS_TO_PAYBACK_MAP.put(RETURN_PROGRESS_PART,
        CUSTOMER_PAYBACK_UN);
    // 全部回款 -> 已完成
    CUSTOMER_RECEIVABLE_RETURN_PROGRESS_TO_PAYBACK_MAP.put(RETURN_PROGRESS_ALL,
        CUSTOMER_PAYBACK_CONFIRM);
  }

  /**
   * 根据回款进度获取对应的客户回款状态（中文）
   *
   * @param customerReturnProgress 回款进度
   * @param defaultPaybackState    默认回款状态（若传入，回款进度参数传入空值或非法值时回款状态会取这个值）
   */
  public static String getCustomerPaybackStateNameByReturnProgress(String customerReturnProgress,
      String defaultPaybackState) {
    String paybackState;
    if (!StringUtils.isNullOrEmpty(customerReturnProgress)) {
      paybackState = Constants_order.CUSTOMER_RECEIVABLE_RETURN_PROGRESS_TO_PAYBACK_MAP.get(
          customerReturnProgress);
    } else {
      paybackState = StrUtil.EMPTY;
    }
    String finalPaybackState;
    if (StringUtils.isNullOrEmpty(paybackState)) {
      // 回款进度如果为空，则尝试使用回款状态的默认值
      if (!StringUtils.isNullOrEmpty(defaultPaybackState)) {
        finalPaybackState = defaultPaybackState;
      } else {
        // 若没有配置默认值，则返回空
        finalPaybackState = StrUtil.EMPTY;
      }
    } else {
      finalPaybackState = paybackState;
    }
    return !StringUtils.isNullOrEmpty(finalPaybackState) ? Constants_order.CUSTOMER_PAYBACK_MAP.get(
        finalPaybackState) : StrUtil.EMPTY;
  }

  /**
   * 回款进度
   */
  public static final TreeMap<String, String> CUSTOMER_RETURNP_ROGRESS_MAP = new TreeMap<>();
  /**
   * 未回款
   */
  public static final String CUSTOMER_RETURNP_ROGRESS_UN = "0";
  /**
   * 部分回款
   */
  public static final String CUSTOMER_RETURNP_ROGRESS_PART = "1";
  /**
   * 全部回款
   */
  public static final String CUSTOMER_RETURNP_ROGRESS_CONFIRM = "2";

  static {
    CUSTOMER_RETURNP_ROGRESS_MAP.put(CUSTOMER_RETURNP_ROGRESS_UN, "未回款");
    CUSTOMER_RETURNP_ROGRESS_MAP.put(CUSTOMER_RETURNP_ROGRESS_PART, "部分回款");
    CUSTOMER_RETURNP_ROGRESS_MAP.put(CUSTOMER_RETURNP_ROGRESS_CONFIRM, "全部回款");
  }

  /**
   * 平台类型
   */
  // TODO: 2023/10/20 预计一个本版之后此平台集合只需要留下监狱平台的。
  public static final Map<String, String> ORDER_PLATFORM_MAP = new HashMap<>();

  public static final String ORDER_PLATFORM_ZCY = "4"; //政采云
  public static final String ORDER_PLATFORM_NJSC = "10"; //能建
  public static final String ORDER_PLATFORM_ZHDS = "25"; //中核电商
  public static final String ORDER_PLATFORM_ZGJJ = "32"; //中国交建
  public static final String ORDER_PLATFORM_JSSC = "36"; //江苏省采
  public static final String ORDER_PLATFORM_XASW = "39"; //西安水务
  public static final String ORDER_PLATFORM_GWSC = "ESG"; //国网商城
  public static final String ORDER_PLATFORM_XQDS = "46"; //管网新系统
  public static final String ORDER_PLATFORM_RT = "49"; //融通商城
  public static final String ORDER_PLATFORM_ZGDJ = "59"; //中国电建
  public static final String ORDER_PLATFORM_GWSG = "44"; //国网省管
  public static final String ORDER_PLATFORM_QSYHT = "65"; //乔司一号通
  public static final String ORDER_PLATFORM_LZSC = "66";  //乐筑商城
  public static final String ORDER_PLATFORM_NS_BD = "72";  //南水北调

  public static final String ORDER_PLATFORM_HIRD_PRISON = "73";  //第三监狱
  public static final String ORDER_PLATFORM_FIVE_PRISON = "74";  //第五监狱
  public static final String ORDER_PLATFORM_AHSASC = "75";  //安徽水安商城
  public static final String ORDER_PLATFORM_LDSC = "76";  //隆道商城
  public static final String ORDER_PLATFORM_LHJY = "80";  //临海监狱
  public static final String ORDER_PLATFORM_DLJY = "81";  //第六监狱
  public static final String ORDER_PLATFORM_SNDS = "79";  //山能电商
  public static final String ORDER_PLATFORM_DENZJY = "82";  //第二女子监狱
  public static final String ORDER_PLATFORM_DSJY = "85";  //第四监狱
  public static final String ORDER_PLATFORM_ZTSJ = "78";  //中铁四局
  public static final String ORDER_PLATFORM_ZZJCY = "86";  //中治集采易
  public static final String ORDER_PLATFORM_WLY = "77";  //五粮液
  public static final String ORDER_PLATFORM_ZJSNZJY = "90";  //浙江省女子监狱
  public static final String ORDER_PLATFORM_JYZC = "22";  //江阴政采
  public static final String ORDER_PLATFORM_ZJJLB = "92";  //中交建劳保
  public static final String ORDER_PLATFORM_ZYZC = "89";  //中原招采
  public static final String ORDER_PLATFORM_DEJY = "99";  //第二监狱
  public static final String ORDER_PLATFORM_SDYG = "107";  //山东阳光
  public static final String ORDER_PLATFORM_LFSC = "100";  //绿发商城
  public static final String ORDER_PLATFORM_PMJD = "98";  //平煤集团
  public static final String ORDER_PLATFORM_ZQ = "ZQ001";  //管网专区
  public static final String ORDER_PLATFORM_ZQ002 = "ZQ002";  //管网物资专区
  public static final String ORDER_PLATFORM_ZGHF = "110";//中国航发




  static {
    ORDER_PLATFORM_MAP.put(ORDER_PLATFORM_ZCY, "政采云");
    ORDER_PLATFORM_MAP.put(ORDER_PLATFORM_NJSC, "能建");
    ORDER_PLATFORM_MAP.put(ORDER_PLATFORM_ZHDS, "中核电商");
    ORDER_PLATFORM_MAP.put(ORDER_PLATFORM_ZGJJ, "中国交建");
    ORDER_PLATFORM_MAP.put(ORDER_PLATFORM_JSSC, "江苏省采");
    ORDER_PLATFORM_MAP.put(ORDER_PLATFORM_XASW, "西安水务");
    ORDER_PLATFORM_MAP.put(ORDER_PLATFORM_GWSC, "国网商城");
    ORDER_PLATFORM_MAP.put(ORDER_PLATFORM_XQDS, "管网新系统");
    ORDER_PLATFORM_MAP.put(ORDER_PLATFORM_RT, "融通商城");
    ORDER_PLATFORM_MAP.put(ORDER_PLATFORM_ZGDJ, "中国电建");
    ORDER_PLATFORM_MAP.put(ORDER_PLATFORM_GWSG, "国网省管");
    ORDER_PLATFORM_MAP.put(ORDER_PLATFORM_QSYHT, "乔司一号通");
    ORDER_PLATFORM_MAP.put(ORDER_PLATFORM_LZSC, "乐筑商城");
    ORDER_PLATFORM_MAP.put(ORDER_PLATFORM_NS_BD, "南水北调");
    ORDER_PLATFORM_MAP.put(ORDER_PLATFORM_HIRD_PRISON, "第三监狱");
    ORDER_PLATFORM_MAP.put(ORDER_PLATFORM_FIVE_PRISON, "第五监狱");
    ORDER_PLATFORM_MAP.put(ORDER_PLATFORM_AHSASC, "安徽水安商城");
    ORDER_PLATFORM_MAP.put(ORDER_PLATFORM_LDSC, "隆道商城");
    ORDER_PLATFORM_MAP.put(ORDER_PLATFORM_LHJY, "临海监狱");
    ORDER_PLATFORM_MAP.put(ORDER_PLATFORM_DLJY, "第六监狱");
    ORDER_PLATFORM_MAP.put(ORDER_PLATFORM_SNDS, "山能电商");
    ORDER_PLATFORM_MAP.put(ORDER_PLATFORM_DENZJY, "第二女子监狱");
    ORDER_PLATFORM_MAP.put(ORDER_PLATFORM_DSJY, "第四监狱");
    ORDER_PLATFORM_MAP.put(ORDER_PLATFORM_ZTSJ, "中铁四局");
    ORDER_PLATFORM_MAP.put(ORDER_PLATFORM_ZZJCY, "中治集采易");
    ORDER_PLATFORM_MAP.put(ORDER_PLATFORM_WLY, "五粮液");
    ORDER_PLATFORM_MAP.put(ORDER_PLATFORM_ZJSNZJY, "浙江省女子监狱");
    ORDER_PLATFORM_MAP.put(ORDER_PLATFORM_JYZC, "江阴政采");
    ORDER_PLATFORM_MAP.put(ORDER_PLATFORM_ZJJLB, "中交建劳保");
    ORDER_PLATFORM_MAP.put(ORDER_PLATFORM_ZYZC, "中原招采");
    ORDER_PLATFORM_MAP.put(ORDER_PLATFORM_DEJY, "第二监狱");
    ORDER_PLATFORM_MAP.put(ORDER_PLATFORM_SDYG, "山东阳光");
    ORDER_PLATFORM_MAP.put(ORDER_PLATFORM_LFSC, "绿发商城");
    ORDER_PLATFORM_MAP.put(ORDER_PLATFORM_PMJD, "平煤集团");
    ORDER_PLATFORM_MAP.put(ORDER_PLATFORM_ZQ, "管网专区");
    ORDER_PLATFORM_MAP.put(ORDER_PLATFORM_ZQ002, "管网物资专区");
    ORDER_PLATFORM_MAP.put(ORDER_PLATFORM_ZGHF, "中国航发");
  }

  public static final String ORDER_PLATFORM_ZDY = "ZDY";


  /**
   * 报备单状态类型
   */
  public static final TreeMap<String, String> FILING_STATE_MAP = new TreeMap<>();
  public static final String FILING_STATE_ING = "1"; //报备中
  public static final String FILING_STATE_DONE = "2"; //已派单
  public static final String FILING_STATE_CANCEL = "3"; //已撤回
  public static final String FILING_STATE_IN_REVIEW = "4"; //审核中
  public static final String FILING_STATE_REJECT = "5"; // 审核驳回
  public static final String FILING_STATE_OVERDUE = "6"; // 已过期

  static {
    FILING_STATE_MAP.put(FILING_STATE_ING, "报备中");
    FILING_STATE_MAP.put(FILING_STATE_DONE, "已派单");
    FILING_STATE_MAP.put(FILING_STATE_CANCEL, "已撤回");
    FILING_STATE_MAP.put(FILING_STATE_IN_REVIEW, "审核中");
    FILING_STATE_MAP.put(FILING_STATE_REJECT, "审核驳回");
    FILING_STATE_MAP.put(FILING_STATE_OVERDUE, "已过期");
  }

  /**
   * 退货/取消单状态
   */
  public static final TreeMap<String, String> ORDER_RETURN_TYPE_MAP = new TreeMap<>();
  public static final String ORDER_RETURN_TYPE = "1"; //退货单
  public static final String ORDER_CANCEL_TYPE = "2"; //取消单

  static {
    ORDER_RETURN_TYPE_MAP.put(ORDER_RETURN_TYPE, "退货单");
    ORDER_RETURN_TYPE_MAP.put(ORDER_CANCEL_TYPE, "取消单");
  }

  /**
   * 取消单状态
   */
  public static final TreeMap<String, String> ORDER_CANCEL_STATE_MAP = new TreeMap<>();
  public static final String ORDER_CANCEL_STATE_APPLY = "1"; //申请中
  public static final String ORDER_CANCEL_STATE_DONE = "2"; //已取消
  public static final String ORDER_CANCEL_STATE_REJECT = "3"; //已拒绝

  static {
    ORDER_CANCEL_STATE_MAP.put(ORDER_CANCEL_STATE_APPLY, "申请中");
    ORDER_CANCEL_STATE_MAP.put(ORDER_CANCEL_STATE_DONE, "已取消");
    ORDER_CANCEL_STATE_MAP.put(ORDER_CANCEL_STATE_REJECT, "已拒绝");
  }

  /**
   * 退货单状态
   */
  public static final TreeMap<String, String> ORDER_RETURN_STATE_MAP = new TreeMap<>();
  public static final String ORDER_RETURN_STATE_WAIT = "1"; //待审核
  public static final String ORDER_RETURN_STATE_PROCESS = "2"; //退货中
  public static final String ORDER_RETURN_STATE_DONE = "3"; //已退货
  public static final String ORDER_RETURN_STATE_REJECT = "4"; //已拒绝

  static {
    ORDER_RETURN_STATE_MAP.put(ORDER_RETURN_STATE_WAIT, "待审核");
    ORDER_RETURN_STATE_MAP.put(ORDER_RETURN_STATE_PROCESS, "退货中");
    ORDER_RETURN_STATE_MAP.put(ORDER_RETURN_STATE_DONE, "已退货");
    ORDER_RETURN_STATE_MAP.put(ORDER_RETURN_STATE_REJECT, "已拒绝");
  }

  /**
   * 对账单状态
   */
  public static final TreeMap<String, String> ORDER_ACCOUNT_STATE_MAP = new TreeMap<>();
  public static final String ORDER_ACCOUNT_STATE_MAP_WAIT = "1"; //待确认
  public static final String ORDER_ACCOUNT_STATE_MAP_DONE = "2"; //已确认
  public static final String ORDER_ACCOUNT_STATE_MAP_PART_REFUND = "3"; //已部分回款
  public static final String ORDER_ACCOUNT_STATE_MAP_ALL_REFUND = "4"; //已回款
  public static final String ORDER_ACCOUNT_STATE_MAP_ALL_VOIDED = "5"; //已作废

  static {
    ORDER_ACCOUNT_STATE_MAP.put(ORDER_ACCOUNT_STATE_MAP_WAIT, "待确认");
    ORDER_ACCOUNT_STATE_MAP.put(ORDER_ACCOUNT_STATE_MAP_DONE, "已确认");
    ORDER_ACCOUNT_STATE_MAP.put(ORDER_ACCOUNT_STATE_MAP_PART_REFUND, "已部分回款");
    ORDER_ACCOUNT_STATE_MAP.put(ORDER_ACCOUNT_STATE_MAP_ALL_REFUND, "已回款");
    ORDER_ACCOUNT_STATE_MAP.put(ORDER_ACCOUNT_STATE_MAP_ALL_VOIDED, "已作废");
  }

  public static final String ORDER_ACCOUNT_PAGE_STATE_LY = "0"; //履约除待开票外订单

  /**
   * 回款状态
   */
  public static final TreeMap<String, String> ORDER_REFUND_STATE_MAP = new TreeMap<>();
  public static final String ORDER_REFUND_STATE_MAP_DONE = "1"; //已回款
  public static final String ORDER_REFUND_STATE_MAP_CANCEL = "2"; //已撤单

  static {
    ORDER_REFUND_STATE_MAP.put(ORDER_REFUND_STATE_MAP_DONE, "已回款");
    ORDER_REFUND_STATE_MAP.put(ORDER_REFUND_STATE_MAP_CANCEL, "已撤单");
  }


  /**
   * 对账类型
   */
  public static final TreeMap<String, String> ORDER_ACCOUNT_TYPE_MAP = new TreeMap<>();
  public static final String ORDER_ACCOUNT_TYPE_COMMIT = "1"; //提交
  public static final String ORDER_REFUND_STATE_MAP_TEMPORARY = "2"; //暂存

  static {
    ORDER_ACCOUNT_TYPE_MAP.put(ORDER_ACCOUNT_TYPE_COMMIT, "提交");
    ORDER_ACCOUNT_TYPE_MAP.put(ORDER_REFUND_STATE_MAP_TEMPORARY, "暂存");
  }

  /**
   * 退货单详情类型
   */
  public static final String ORDER_RETURN_DETAIL_TYPE_NORMAL = "1"; //退货
  public static final String ORDER_RETURN_DETAIL_TYPE_WAIT = "2"; //未发货取消
  public static final String ORDER_RETURN_DETAIL_TYPE_CANCEL = "3"; //取消


  /**
   * 订单详情发货状态
   */
  public static final TreeMap<String, String> ORDER_DETAIL_SHIP_STATE_MAP = new TreeMap<>();
  public static final String ORDER_DETAIL_SHIP_STATE_UN = "1"; //未发
  public static final String ORDER_DETAIL_SHIP_STATE_PART = "2"; //部分发
  public static final String ORDER_DETAIL_SHIP_STATE_ALL = "3"; //已发

  static {
    ORDER_DETAIL_SHIP_STATE_MAP.put(ORDER_DETAIL_SHIP_STATE_UN, "未发");
    ORDER_DETAIL_SHIP_STATE_MAP.put(ORDER_DETAIL_SHIP_STATE_PART, "部分发");
    ORDER_DETAIL_SHIP_STATE_MAP.put(ORDER_DETAIL_SHIP_STATE_ALL, "已发");
  }

  /**
   * 商家自配送的物流编码
   */
  public static final String SELF_DELIVERY = "XHZYWL";

  /** 保存物流信息的 relation type */
  public final static String EXPRESS_RELATION_TYPE = "9999";

  /** 验收单类型 - 两单 */
  public final static String ACCEPT_TYPE_TWO_ORDER = "1";
  /** 验收单类型 - 签收单 */
  public final static String ACCEPT_TYPE_ACCOUNT = "2";

  /** 新对账单状态 */
  public static final BidiMap<String,String> ORDER_ACCOUNT_STATUS_MAP = new DualHashBidiMap<>();

  /** 对账单状态 - 不可对账 */
  public static final String ORDER_ACCOUNT_STATUS_NOT_ALLOW = "1";

  /** 对账单状态 - 可对账 */
  public static final String ORDER_ACCOUNT_STATUS_ALLOW = "2";
  /**
   * 2022-11-30 22:00:00
   */
  public static final Long ORDER_FILTER_TIME = 1669816800000L;

  /** 对账单状态 - 对账中 */
  public static final String ORDER_ACCOUNT_STATUS_CONDUCT = "3";

  /** 对账单状态 - 对账完成 */
  public static final String ORDER_ACCOUNT_STATUS_COMPLETE = "4";
  /** 对账单状态 - 驳回待修改 */
  public static final String ORDER_ACCOUNT_STATE_REJECT = "5"; //驳回待修改

  static {
    ORDER_ACCOUNT_STATUS_MAP.put(ORDER_ACCOUNT_STATUS_NOT_ALLOW,"不可对账");
    ORDER_ACCOUNT_STATUS_MAP.put(ORDER_ACCOUNT_STATUS_ALLOW,"可对账");
    ORDER_ACCOUNT_STATUS_MAP.put(ORDER_ACCOUNT_STATUS_CONDUCT,"对账中");
    ORDER_ACCOUNT_STATUS_MAP.put(ORDER_ACCOUNT_STATUS_COMPLETE,"对账完成");
    ORDER_ACCOUNT_STATUS_MAP.put(ORDER_ACCOUNT_STATE_REJECT,"驳回待修改");
  }

  /** 订单付款状态 */
  public static final BidiMap<String,String> ORDER_PAYMENT_STATUS_MAP = new DualHashBidiMap<>();

  /** 不可付款 */
  public static final String CAN_NOT_PAYMENT_TYPE = "1";
  /** 待申请 */
  public static final String WAIT_APPLY_PAYMENT_TYPE = "2";
  /** 付款中 */
  public static final String CONDUCT_PAYMENT_TYPE = "3";
  /**
   * 部分付款
   */
  public static final String PART_PAYMENT_TYPE = "33";
  /** 付款完成 */
  public static final String COMPLETE_PAYMENT_TYPE = "4";
  /** 线下付款 */
  public static final String OFFLINE_PAYMENT_TYPE = "5";
  static {
    ORDER_PAYMENT_STATUS_MAP.put(CAN_NOT_PAYMENT_TYPE,"不可付款");
    ORDER_PAYMENT_STATUS_MAP.put(WAIT_APPLY_PAYMENT_TYPE,"待申请");
    ORDER_PAYMENT_STATUS_MAP.put(CONDUCT_PAYMENT_TYPE,"付款中");
    ORDER_PAYMENT_STATUS_MAP.put(COMPLETE_PAYMENT_TYPE,"付款完成");
    ORDER_PAYMENT_STATUS_MAP.put(OFFLINE_PAYMENT_TYPE,"线下付款");
    ORDER_PAYMENT_STATUS_MAP.put(PART_PAYMENT_TYPE,"部分付款");
  }

  /** 付款单中间表类型 - 订单 id */
  public static final String ORDER_TO_PAYMENT_TYPE_ORDER_ID = "1";

  /** 付款单中间表类型 - ERP 付款单号 */
  public static final String ORDER_TO_PAYMENT_TYPE_ERP_PAYMENT_NO = "2";

  /**
   * 落地商订单开票类型 - 单个订单开票
   */
  public static final String ORDER_INVOICE_TYPE_SINGLE = "1";
  /**
   * 落地商订单开票类型 - 订单合并开票
   */
  public static final String ORDER_INVOICE_TYPE_MERGE = "2";



  //中国交建-工业品
  public static final String ORDER_PLATFORM_ZGJJ_IS = "3201";
  /** 需要区分大小类型的订单 小类型-大类型 */


  public static final TreeMap<String, String> DOCKING_ORDER_KIND_TYPE = new TreeMap<>();
  static {
    DOCKING_ORDER_KIND_TYPE.put(ORDER_PLATFORM_ZGJJ, ORDER_PLATFORM_ZGJJ);
    DOCKING_ORDER_KIND_TYPE.put(ORDER_PLATFORM_ZGJJ_IS, ORDER_PLATFORM_ZGJJ);
  }

/*  *//**
   * 物流方式 - 商家配送Code
   *//*
  public static final String EXPRESS_CODE = "XHZYWL";
  *//**
   * 物流方式 - 商家配送Name
   *//*
  public static final String EXPRESS_COMPANY= "商家配送";*/

  /**
   * 订单验收凭证审核状态 - 审核中
   */
  public static final String ORDER_ACCEPT_PENDING_AUDITING = "1";
  /**
   * 订单验收凭证审核状态 - 驳回
   */
  public static final String ORDER_ACCEPT_REJECT = "2";
  /**
   * 订单验收凭证审核状态 - 已确认
   */
  public static final String ORDER_ACCEPT_CONSENT = "3";

  /**
   * 订单验收凭证状态 - 待上传
   */
  public static final String ORDER_ACCEPT_PENDING_UPLOADING = "4";

  /**
   * 签收凭证
   */
  public static final TreeMap<String, String> SIGN_VOUCHER_MAP = new TreeMap<>();
  static {
    SIGN_VOUCHER_MAP.put(ORDER_ACCEPT_PENDING_AUDITING, "审核中");
    SIGN_VOUCHER_MAP.put(ORDER_ACCEPT_REJECT, "驳回");
    SIGN_VOUCHER_MAP.put(ORDER_ACCEPT_CONSENT, "已确认");
    SIGN_VOUCHER_MAP.put(ORDER_ACCEPT_PENDING_UPLOADING, "待上传");
  }

  /**
   * 金蝶erp订单
   */
  public static final String PUCHASE_TYPE_ERP = "1";
  /**
   * 标准订单
   */
  public static final String PUCHASE_TYPE_SAP = "2";

  /**
   * 进项票关联类型 - 订单
   */
  public static final String INVOICE_ASSOCIATION_TYPE_ORDER = "订单";
  /**
   * 进项票关联类型 - 入库单
   */
  public static final String INVOICE_ASSOCIATION_TYPE_WAREHOUSING_ENTRY = "入库单";
  /**
   * 进项票关联类型 - 明细
   */
  public static final String INVOICE_ASSOCIATION_TYPE_DETAILS = "物料明细";

  /**
   * 华能商城平台
   */
  public static final String ORDER_PLATFORM_HNSC = "华能商城";

  /**
   * 华能商城平台Code
   */
  public static final String ORDER_PLATFORM_HNSC_CODE = "127";

  /**
   * 国铁商城平台Code
   * @param args
   */
  public static final String ORDER_PLATFORM_GTSC_CODE = "131";

  /** 付款单状态 */
  public static final BidiMap<String,String> ORDER_PAYMENT_STATUS_NEW_MAP = new DualHashBidiMap<>();

  /** 驳回 */
  public static final String OVER_RULE_PAYMENT_TYPE = "1";
  /** 审核中 */
  public static final String UNDER_REVIEW_PAYMENT_TYPE = "2";
  /** 付款中 */
  public static final String DURING_PAYMENT_TYPE = "3";
  /** 付款完成 */
  public static final String FINISHED_PAYMENT_TYPE = "4";
  /** 已放弃 */
  public static final String WAIVED_PAYMENT_TYPE = "5";

  static {
    ORDER_PAYMENT_STATUS_NEW_MAP.put(OVER_RULE_PAYMENT_TYPE,"驳回");
    ORDER_PAYMENT_STATUS_NEW_MAP.put(UNDER_REVIEW_PAYMENT_TYPE,"审核中");
    ORDER_PAYMENT_STATUS_NEW_MAP.put(DURING_PAYMENT_TYPE,"付款中");
    ORDER_PAYMENT_STATUS_NEW_MAP.put(FINISHED_PAYMENT_TYPE,"付款完成");
    ORDER_PAYMENT_STATUS_NEW_MAP.put(WAIVED_PAYMENT_TYPE,"已放弃");
  }
  /**
   * check 付款单状态
   */
  public static final List<String> CHECK_PAYMENT_STATUS = Arrays.asList(Constants_order.OVER_RULE_PAYMENT_TYPE,
      Constants_order.UNDER_REVIEW_PAYMENT_TYPE);

  public static void main(String[] args) {
    String dockingType = "32";
    String subType = "";
    if(Constants_order.DOCKING_ORDER_KIND_TYPE.containsKey(dockingType)){
      subType = Constants_order.DOCKING_ORDER_KIND_TYPE.get(dockingType);
    }
    System.out.println(subType);
  }
}

