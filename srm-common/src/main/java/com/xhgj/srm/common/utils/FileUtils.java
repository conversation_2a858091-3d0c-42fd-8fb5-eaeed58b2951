package com.xhgj.srm.common.utils;

import cn.hutool.core.util.StrUtil;

/**
 * <AUTHOR>
 * @since 2020/6/11 14:47
 */
public class FileUtils {

  /**
   * 在文件名后加入字符串，eg: updateSomething.xls -> updateSomething202006111450.xls
   *
   * @param fileName 文件名
   * @param insertStr 要插入的字符串
   * @return 插入后的文件名
   */
  public static String getFileNameWithInsertStr(String fileName, String insertStr) {
    if (StrUtil.isEmpty(fileName)) {
      return StrUtil.EMPTY;
    }
    if (StrUtil.isEmpty(insertStr)) {
      return fileName;
    }
    int suffixIndex = fileName.lastIndexOf(".");
    // .xls
    String suffix = fileName.substring(suffixIndex);
    String pureFileName = fileName.substring(0, suffixIndex);
    pureFileName += insertStr;
    return pureFileName + suffix;
  }
}
