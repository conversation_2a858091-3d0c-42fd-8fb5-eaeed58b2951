package com.xhgj.srm.common.utils;/**
 * @since 2025/2/18 19:28
 */

import cn.hutool.core.util.StrUtil;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 *<AUTHOR>
 *@date 2025/2/18 19:28:07
 *@description
 */
public class FileNameUtils {

  // 最大文件名长度
  private static final int MAX_FILE_NAME_LENGTH = 75;

  public static String encodeKeepChinese(String fileName) {
    if (StrUtil.isBlank(fileName)) {
      return fileName;
    }
    StringBuilder result = new StringBuilder();
    for (char c : fileName.toCharArray()) {
      if (isChinese(c) || isSafeChar(c)) {
        // 直接保留中文和安全字符
        result.append(c);
      } else {
        // 对其他字符进行URL编码
        try {
          result.append("_");
        } catch (Exception e) {
          throw new RuntimeException("编码失败", e);
        }
      }
    }
    // 超过100，截取后面的部分
    if (result.length() > MAX_FILE_NAME_LENGTH) {
      return result.substring(result.length() - MAX_FILE_NAME_LENGTH);
    }
    return result.toString();
  }

  /**
   * 判断是否为安全字符（无需编码）
   */
  private static boolean isSafeChar(char c) {
    // OSS允许直接使用的字符：A-Z, a-z, 0-9, -, _, ., !, ~, *, ', (, ), /
    return (c >= 'A' && c <= 'Z')
        || (c >= 'a' && c <= 'z')
        || (c >= '0' && c <= '9')
        || c == '-' || c == '_' || c == '.' || c == '!'
        || c == '~' || c == '*' || c == '\'' || c == '('
        || c == ')' || c == '/';
  }

  /**
   * 判断是否为中文字符
   */
  private static boolean isChinese(char c) {
    int codePoint = (int) c;
    // 匹配范围：
    // 1. 基本汉字：4E00-9FFF
    // 2. 中文标点：3000-303F
    // 3. 全角符号：FF00-FFEF（包括（）、！等）
    return (codePoint >= 0x4E00 && codePoint <= 0x9FFF)    // 汉字
        || (codePoint >= 0x3000 && codePoint <= 0x303F)   // 中文标点
        || (codePoint >= 0xFF00 && codePoint <= 0xFFEF);  // 全角符号
  }

  public static void main(String[] args) {
    String fileName = "0-30,011.10_万聚国际（杭州）供应链有限公司_20250218113322";
    System.out.println(encodeKeepChinese(fileName));
  }

}
