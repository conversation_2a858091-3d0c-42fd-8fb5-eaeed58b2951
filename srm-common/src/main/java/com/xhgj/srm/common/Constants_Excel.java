package com.xhgj.srm.common;

import cn.hutool.core.collection.ListUtil;
import com.xhgj.srm.common.domain.ExcelTitleAttr;
import com.xhgj.srm.common.domain.ExportExcelTitleAttr;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import com.xhgj.srm.common.enums.supplier.SupplierLevelEnum;
import org.apache.poi.ss.usermodel.IndexedColors;

/**
 * <AUTHOR>
 * @since 2020/3/24 13:36
 */
public class Constants_Excel {

  /** 预导入 Excel 模板 */
  public static final Map<String, ExcelTitleAttr> PRODUCT_YDR_EXCEL_TITLE_MAP =
      new LinkedHashMap<>();

  /**
   * 导出供应商订单模板
   */
  public static final List<ExportExcelTitleAttr> EXPORT_SUPPLIER_ORDER_TITLE_LIST =
      new ArrayList<>();


  static {
    // 0
    PRODUCT_YDR_EXCEL_TITLE_MAP.put("物料编码", new ExcelTitleAttr(20));
    PRODUCT_YDR_EXCEL_TITLE_MAP.put("名称", new ExcelTitleAttr(30));
    PRODUCT_YDR_EXCEL_TITLE_MAP.put("四级类目名称", new ExcelTitleAttr(20));
    PRODUCT_YDR_EXCEL_TITLE_MAP.put("品牌名称", new ExcelTitleAttr(20));
    // 5
    PRODUCT_YDR_EXCEL_TITLE_MAP.put("基本单位名称", new ExcelTitleAttr(20));
    PRODUCT_YDR_EXCEL_TITLE_MAP.put("型号规格", new ExcelTitleAttr(20));
    PRODUCT_YDR_EXCEL_TITLE_MAP.put("起订量", new ExcelTitleAttr(20));

    //设置导出供应商订单模板
    //设置第一行所有合并的数据
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("基础资料", 30,
        IndexedColors.YELLOW.index, 0, 0, 0, 16,0, 0));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("供方联系人", 30,
        IndexedColors.LIGHT_GREEN.index, 0, 0, 17, 20,0, 17));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("收件信息", 30,
        IndexedColors.LIGHT_ORANGE.index, 0, 0, 21, 23,0, 21));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("金额信息", 30,
        IndexedColors.SKY_BLUE.index, 0, 0, 24, 30,0, 24));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("关联单据信息", 30,
        IndexedColors.PINK.index, 0, 0, 31, 37,0, 31));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("物料明细", 30,
        IndexedColors.SEA_GREEN.index, 0, 0, 38, 75,0, 38));
    //设置第二行无需合并的数据
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("采购订单号", 30,
        IndexedColors.YELLOW.index, 1, 0));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("订单类型", 30,
        IndexedColors.YELLOW.index, 1, 1));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("是否自采订单", 30,
        IndexedColors.YELLOW.index, 1, 2));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("是否纯赠品订单", 30,
        IndexedColors.YELLOW.index, 1, 3));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("订单状态", 30,
        IndexedColors.YELLOW.index, 1, 4));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("创建人姓名", 30,
        IndexedColors.YELLOW.index, 1, 5));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("创建时间", 30,
        IndexedColors.YELLOW.index, 1, 6));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("采购组织", 30,
        IndexedColors.YELLOW.index, 1, 7));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("采购部门", 30,
        IndexedColors.YELLOW.index, 1, 8));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("采购员", 30,
        IndexedColors.YELLOW.index, 1, 9));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("供应商主数据编码", 30,
        IndexedColors.YELLOW.index, 1, 10));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("供应商名称", 30,
        IndexedColors.YELLOW.index, 1, 11));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("是否厂直发", 30,
        IndexedColors.YELLOW.index, 1, 12));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("货币码", 30,
        IndexedColors.YELLOW.index, 1, 13));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("订单汇率", 30,
        IndexedColors.YELLOW.index, 1, 14));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("订单备注", 30,
        IndexedColors.YELLOW.index, 1, 15));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("合同附件", 30,
        IndexedColors.YELLOW.index, 1, 16));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("联系人", 30,
        IndexedColors.LIGHT_GREEN.index, 1, 17));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("联系方式", 30,
        IndexedColors.LIGHT_GREEN.index, 1, 18));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("电子邮件", 30,
        IndexedColors.LIGHT_GREEN.index, 1, 19));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("传真", 30,
        IndexedColors.LIGHT_GREEN.index, 1, 20));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("收件人", 30,
        IndexedColors.LIGHT_ORANGE.index, 1, 21));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("收件人联系方式", 30,
        IndexedColors.LIGHT_ORANGE.index, 1, 22));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("收件地址", 30,
        IndexedColors.LIGHT_ORANGE.index, 1, 23));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("采购件数", 30,
        IndexedColors.SKY_BLUE.index, 1, 24));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("订货金额", 30,
        IndexedColors.SKY_BLUE.index, 1, 25));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("退货/取消金额", 30,
        IndexedColors.SKY_BLUE.index, 1, 26));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("入库进度", 30,
        IndexedColors.SKY_BLUE.index, 1, 27));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("已开票金额", 30,
        IndexedColors.SKY_BLUE.index, 1, 28));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("已预付金额", 30,
        IndexedColors.SKY_BLUE.index, 1, 29));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("已应付金额", 30,
        IndexedColors.SKY_BLUE.index, 1, 30));

    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("大票项目号", 30,
        IndexedColors.PINK.index, 1, 31));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("销售订单号", 30,
        IndexedColors.PINK.index, 1, 32));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("客户订单号", 30,
        IndexedColors.PINK.index, 1, 33));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("客户开票状态", 30,
        IndexedColors.PINK.index, 1, 34));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("客户回款状态", 30,
        IndexedColors.PINK.index, 1, 35));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("供应商开票状态", 30,
        IndexedColors.PINK.index, 1, 36));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("关联发票号", 30,
        IndexedColors.PINK.index, 1, 37));


    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("物料行id", 30,
        IndexedColors.SEA_GREEN.index, 1, 38));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("交货进度", 30,
        IndexedColors.SEA_GREEN.index, 1, 39));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("物料编码", 30,
        IndexedColors.SEA_GREEN.index, 1, 40));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("品牌", 30,
        IndexedColors.SEA_GREEN.index, 1, 41));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("物料名称", 30,
        IndexedColors.SEA_GREEN.index, 1, 42));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("物料描述", 30,
        IndexedColors.SEA_GREEN.index, 1, 43));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("规格型号", 30,
        IndexedColors.SEA_GREEN.index, 1, 44));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("订货数量", 30,
        IndexedColors.SEA_GREEN.index, 1, 45));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("单位", 30,
        IndexedColors.SEA_GREEN.index, 1, 46));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("仓库", 30,
        IndexedColors.SEA_GREEN.index, 1, 47));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("含税单价", 30,
        IndexedColors.SEA_GREEN.index, 1, 48));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("税率", 30,
        IndexedColors.SEA_GREEN.index, 1, 49));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("去税单价", 30,
        IndexedColors.SEA_GREEN.index, 1, 50));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("税额", 30,
        IndexedColors.SEA_GREEN.index, 1, 51));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("去税金额", 30,
        IndexedColors.SEA_GREEN.index, 1, 52));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("价税合计", 30,
        IndexedColors.SEA_GREEN.index, 1, 53));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("结算单价", 30,
        IndexedColors.SEA_GREEN.index, 1, 54));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("结算总价", 30,
        IndexedColors.SEA_GREEN.index, 1, 55));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("约定交货日期", 30,
        IndexedColors.SEA_GREEN.index, 1, 56));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("实际交货日期", 30,
        IndexedColors.SEA_GREEN.index, 1, 57));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("待发数量", 30,
        IndexedColors.SEA_GREEN.index, 1, 58));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("已发数量", 30,
        IndexedColors.SEA_GREEN.index, 1, 59));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("取消订货数量", 30,
        IndexedColors.SEA_GREEN.index, 1, 60));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("退货数量", 30,
        IndexedColors.SEA_GREEN.index, 1, 61));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("采购入库数量", 30,
        IndexedColors.SEA_GREEN.index, 1, 62));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("退库数量", 30,
        IndexedColors.SEA_GREEN.index, 1, 63));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("剩余入库数量", 30,
        IndexedColors.SEA_GREEN.index, 1, 64));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("实际交货数量", 30,
        IndexedColors.SEA_GREEN.index, 1, 65));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("已开票数量", 30,
        IndexedColors.SEA_GREEN.index, 1, 66));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("到货未开票数量", 30,
        IndexedColors.SEA_GREEN.index, 1, 67));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("物料行备注", 30,
        IndexedColors.SEA_GREEN.index, 1, 68));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("是否免费", 30,
        IndexedColors.SEA_GREEN.index, 1, 69));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("项目类别", 30,
        IndexedColors.SEA_GREEN.index, 1, 70));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("关联采购申请单号-采购申请物料行id", 30,
        IndexedColors.SEA_GREEN.index, 1, 71));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("采购申请类型", 30,
        IndexedColors.SEA_GREEN.index, 1, 72));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("采购申请业务员", 30,
        IndexedColors.SEA_GREEN.index, 1, 73));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("关联销售订单号", 30,
        IndexedColors.SEA_GREEN.index, 1, 74));
    EXPORT_SUPPLIER_ORDER_TITLE_LIST.add(new ExportExcelTitleAttr("关联项目编号", 30,
        IndexedColors.SEA_GREEN.index, 1, 75));
  }


  /** 预导出 Excel 模板：标题列表 */
  public static final List<String> PRODUCT_YDR_EXCEL_TITLE_LIST =
      new ArrayList<>(PRODUCT_YDR_EXCEL_TITLE_MAP.keySet());

  /** 预导出 Excel 模板：列宽列表（导出时用） */
  public static final List<Integer> PRODUCT_YDC_EXCEL_EXPORT_TITLE_WIDTH_LIST =
      PRODUCT_YDR_EXCEL_TITLE_MAP.values().stream()
          .map(ExcelTitleAttr::getCellWidth)
          .collect(Collectors.toList());

  /** 导入组织模板 */
  public static final List<String> IMPORT_GROUP_TITLE_LIST =
      ListUtil.toList("SRM组织编码*", "SRM组织名称*", "ERP组织编码*");

  /** 导出组织模板 */
  public static final List<String> EXPORT_GROUP_TITLE_LIST =
      ListUtil.toList("SRM组织编码*", "SRM组织名称*", "ERP组织编码*", "创建人", "创建时间");

  /** 导入组织模板 */
  public static final List<String> IMPORT_DEPARTMENT_TITLE_LIST =
      ListUtil.toList("SRM部门编码*", "SRM部门名称*", "上级部门编码", "ERP部门编码*", "所属组织编码*");

  /** 导出组织模板 */
  public static final List<String> EXPORT_DEPARTMENT_TITLE_LIST =
      ListUtil.toList("SRM部门编码", "SRM部门名称", "SRM上级部门编码", "SRM上级部门名称", "ERP部门编码", "所属组织编码");

  /** 导出国内供应商模板 */
  public static final List<String> EXPORT_SUPPLIER_IN_GROUP_CHINA_TITLE_LIST =
      ListUtil.toList(
          "MDM 编码",
          "企业名称",
          "组织编码",
          "负责人编码",
          "发票类型",
          "默认税率",
          "结算币别",
          "匹配度",
          "负责人名称",
          "负责人部门",
          "供应商等级",
          "创建日期",
          "账期",
          "开户银行",
          "银行账号",
          "账户名称",
          "开户行地址",
          "联行号",
          "Swiftcode");

  /** 导出国外供应商模板 */
  public static final List<String> EXPORT_SUPPLIER_IN_GROUP_ABROAD_TITLE_LIST =
      ListUtil.toList(
          "MDM 编码",
          "供应商名称",
          "组织编码",
          "负责人编码",
          "发票类型",
          "默认税率",
          "结算币别",
          "匹配度",
          "负责人名称",
          "负责人部门",
          "供应商等级",
          "创建日期",
          "开户银行",
          "银行账号",
          "账户名称",
          "开户行地址",
          "联行号",
          "Swiftcode");

  /** 导出个人供应商模板 */
  public static final List<String> EXPORT_SUPPLIER_IN_GROUP_PERSON_TITLE_LIST =
      ListUtil.toList("MDM 编码", "姓名", "联系方式", "组织编码", "负责人编码", "开户银行", "银行账号");

  /** 导入国内供应商模板 */
  public static final List<String> IMPORT_SUPPLIER_IN_GROUP_CHINA_TITLE_LIST =
      ListUtil.toList(
          "MDM编码*",
          "企业名称*",
          "组织编码*",
          "负责人编码",
          "发票类型",
          "默认税率",
          "结算币别",
          "账期",
          "供应商等级",
          "法人",
          "注册地址",
          "开户银行",
          "银行账号",
          "账户名称",
          "开户行地址",
          "联行号",
          "Swiftcode");

  /** 导入国外供应商模板 */
  public static final List<String> IMPORT_SUPPLIER_IN_GROUP_ABROAD_TITLE_LIST =
      ListUtil.toList(
          "MDM编码*",
          "供应商名称*",
          "组织编码*",
          "负责人编码",
          "发票类型",
          "默认税率",
          "结算币别",
          "供应商等级",
          "开户银行",
          "银行账号",
          "账户名称",
          "开户行地址",
          "联行号",
          "Swiftcode");

  /** 导入个人供应商模板 */
  public static final List<String> IMPORT_SUPPLIER_IN_GROUP_PERSON_TITLE_LIST =
      ListUtil.toList("MDM编码*", "姓名*", "联系方式*", "组织编码*", "负责人编码*", "开户银行", "银行账号");

  /** 导入用户模板 */
  public static final List<String> IMPORT_USER_TITLE_FIRST_LIST =
      ListUtil.toList("基本信息","数据范围","操作范围", "新增审核人ERP编码", "修改审核人ERP编码", "拉黑审核人ERP编码",
          "修改供应商等级审核人ERP编码", "分配组织部门1",	"分配组织部门2",	"分配组织部门3");

  /** 导入用户模板 */
  public static final List<String> IMPORT_RELATION_LARGE_TICKET_TITLE =
      ListUtil.toList("客户订单号*","供应商名称*","派单时间*","大票项目号*","销售订单号*","物料编码*","销售订单物料行id*");
  public static final List<String> IMPORT_USER_TITLE_TWO_LIST =
      ListUtil.toList(
          "姓名*",
          "手机号码*",
          "ERP编码*",
          "ERPID*",
          "角色编码*",
          "合同*",
          "询价*",
          "采购申请*",
          "采购订单*",
          "进项票*",
          "采购价格库*","退换货订单*",
          "供应商拉黑*",
          "供应商修改*",
          "负责人修改*",
          "采购价格库*",
          "采购申请修改/取消*","入库/退库单导出*",
          "采购申请列表*",
          "采购订单列表*","财务凭证列表*","付款申请列表*",
          "库存列表*","退换货订单*","库存安全列表*","组装拆卸单导出*",
//          "进项票导出*",
          SupplierLevelEnum.STRATEGIC.getAbbr(),
          SupplierLevelEnum.HIGH_QUALITY.getAbbr(),
          SupplierLevelEnum.GENERAL.getAbbr(),
          SupplierLevelEnum.SPORADIC.getAbbr(),
          SupplierLevelEnum.POTENTIAL.getAbbr(),
          SupplierLevelEnum.STRATEGIC.getAbbr(),
          SupplierLevelEnum.HIGH_QUALITY.getAbbr(),
          SupplierLevelEnum.GENERAL.getAbbr(),
          SupplierLevelEnum.SPORADIC.getAbbr(),
          SupplierLevelEnum.POTENTIAL.getAbbr(),
          SupplierLevelEnum.STRATEGIC.getAbbr(),
          SupplierLevelEnum.HIGH_QUALITY.getAbbr(),
          SupplierLevelEnum.GENERAL.getAbbr(),
          SupplierLevelEnum.SPORADIC.getAbbr(),
          SupplierLevelEnum.POTENTIAL.getAbbr(),
          SupplierLevelEnum.STRATEGIC.getAbbr(),
          SupplierLevelEnum.HIGH_QUALITY.getAbbr(),
          SupplierLevelEnum.GENERAL.getAbbr(),
          SupplierLevelEnum.SPORADIC.getAbbr(),
          SupplierLevelEnum.POTENTIAL.getAbbr(),
          "SRM组织编码*",
          "SRM部门编码*",
          "SRM组织编码",
          "SRM部门编码",
          "SRM组织编码",
          "SRM部门编码"
          );

  /** 导出用户模板 */
  public static final List<String> EXPORT_USER_TITLE_TWO_LIST =
      ListUtil.toList(
          "姓名*",
          "手机号码*",
          "ERP编码*",
          "ERPID*",
          "角色编码*",
          "角色名称*",
          "合同*"	,"询价*","采购申请*"	,"采购订单*"	,"进项票*",	"采购价格库*"	,"退换货订单*",
          "供应商拉黑*"	,"供应商修改*"	,"负责人修改*" ,
          "采购价格库*",	"采购申请修改/取消*","入库/退库单导出*"	,"采购申请列表*"	,"采购订单列表*","财务凭证列表*","付款申请列表*",
          "库存列表*","退换货订单*","库存安全列表*","组装拆卸单导出*",
//          "进项票导出*",
          SupplierLevelEnum.STRATEGIC.getAbbr(),
          SupplierLevelEnum.HIGH_QUALITY.getAbbr(),
          SupplierLevelEnum.GENERAL.getAbbr(),
          SupplierLevelEnum.SPORADIC.getAbbr(),
          SupplierLevelEnum.POTENTIAL.getAbbr(),
          SupplierLevelEnum.STRATEGIC.getAbbr(),
          SupplierLevelEnum.HIGH_QUALITY.getAbbr(),
          SupplierLevelEnum.GENERAL.getAbbr(),
          SupplierLevelEnum.SPORADIC.getAbbr(),
          SupplierLevelEnum.POTENTIAL.getAbbr(),
          SupplierLevelEnum.STRATEGIC.getAbbr(),
          SupplierLevelEnum.HIGH_QUALITY.getAbbr(),
          SupplierLevelEnum.GENERAL.getAbbr(),
          SupplierLevelEnum.SPORADIC.getAbbr(),
          SupplierLevelEnum.POTENTIAL.getAbbr(),
          SupplierLevelEnum.STRATEGIC.getAbbr(),
          SupplierLevelEnum.HIGH_QUALITY.getAbbr(),
          SupplierLevelEnum.GENERAL.getAbbr(),
          SupplierLevelEnum.SPORADIC.getAbbr(),
          SupplierLevelEnum.POTENTIAL.getAbbr(),
          "SRM组织编码*",
          "SRM组织名称",
          "SRM部门编码*",
          "SRM部门名称");

  public static final List<String> IMPORT_CORRECTION_CONTACT_TITLE_LIST =
      ListUtil.toList("组织编码*", "MDM编码*", "企业名称*", "姓名*", "性别", "联系方式*", "邮箱");

  /** 导出供应商订单明细模板 */
  public static final List<String> EXPORT_SUPPLIER_ORDER_DETAILS_TITLE_LIST =
      ListUtil.toList(
          "物料序号", "进度", "物料编码", "品牌", "商品名称", "规格型号", "数量", "单位", "单价", "待发数量", "已发数量", "退货数量",
          "取消数量", "采购入库数量", "退库数量", "实际结算数量", "描述");

  /** 前台导出供应商订单模板 */
  public static final List<String> EXPORT_SUPPLIER_ORDER_TITLE_LIST_SUPPLIER =
      ListUtil.toList(
          "采购订单号", "订单状态", "创建时间", "是否厂直发", "采购组织","采购员", "收件人", "采购件数", "订单金额", "入库进度", "物料序号", "进度",
          "物料编码", "品牌", "商品名称", "规格型号", "数量", "单位", "单价", "待发数量", "已发数量", "退货数量", "取消数量", "采购入库数量",
          "退库数量", "实际结算数量", "描述");

  /** 后台导出落地商模板 */
  public static final List<String> EXPORT_ORDER_TITLE_LIST =
      ListUtil.toList("客户订单号", "订单所属ERP", "订单状态", "下单时间", "派单时间",
          "最早发货时间", "下单平台", "下单金额", "销售金额","退货金额", "派单点数","原点数", "供应商名称", "客户名称", "收件人",
          "联系方式", "收货地址","签约抬头", "签收凭证","签收凭证通过时间","客户签收时间", "客户开票","客户开票日期", "客户回款", "回款比例",
          "最新到账时间", "最新核销时间", "最新回款方式","客户回款日期", "对账状态", "供应商开票","供应商开票时间","付款发起条件","付款条件满足日期",
          "对应账期","预计付款时间", "付款申请时间","付款状态","付款金额","付款完成时间", "大票项目号", "销售订单号", "采购订单号");

  public static final List<String> EXPORT_ORDER_TITLE_LIST_NEW =
      ListUtil.toList(
          "客户订单号",
          "订单状态",
          "下单平台",
          "客户下单时间",
          "助理派单时间",
          "首次发货时间",
          "签收凭证提交时间",
          "签收凭证确认时间",
          "客户最早开票日期",
          "客户回款完成日期",
          "进项票上传时间",
          "进项票确认时间",
          "申请付款时间",
          "实际付款时间");

  /** 后台导出落地商申请开票列表 */
  public static final List<String> EXPORT_ORDER_INVOICE_TITLE_LIST =
      ListUtil.toList(
          "申请单号", "下单金额", "申请人", "申请时间", "开票状态", "发票类型", "发票抬头", "税号", "开户银行", "银行账户", "电话", "地址",
          "票面信息", "收件人", "联系电话", "收件地址", "其他备注", "附件1", "附件2", "附件3", "附件4", "附件5", "附件6", "附件7",
          "附件8", "附件9", "附件10");

  /** 后台导出落地商物料明细 */
  public static final List<String> EXPORT_ORDER_PRODUCT_DETAIL =
      ListUtil.toList(
          "订单编号", "序号", "物料编码", "品牌", "商品名称", "型号", "数量", "单位", "单价", "折扣比例", "销售单价", "未发数量",
          "已发数量", "退货数量", "取消数量");

  public static final List<String> EXPORT_ORDER_PRODUCT_PERFORMANCE_INFO =
      ListUtil.toList(
          "供应商名称", "主数据编码", "平台名称", "状态", "联系人", "电话", "合作区域/项目","合作类型", "关联合同",
          "仓储","仓库面积","仓库地址","合作品牌/品类范围","保证金","账期","付款方式","票种税率","保底金额","违约金","当前履约周期",
          "当前履约金额", "当前折扣比例","准入说明","身份证","产品资质书", "业务负责人", "对接采购", "对接助理");

  /** 物料库存导入 * */
  public static final List<String> IMPORT_PRODUCT_STOCK_LIST =
      ListUtil.toList("物料编码", "物料名称", "规格型号", "单位", "库存所在地", "区域库存");
  /** 导入-合同信息  */
  public static final List<String> IMPORT_CONTRACT_LIST =
      ListUtil.toList("*合同号", "*下单平台", "*对方签约主体", "*合作类型",
          "*合同有效期", "*仓储","仓库地址与面积","应付款比例", "*付款条件", "账期", "*付款方式","保证金","保证金是否缴纳","*票种","*税率",
          "*保底金额", "违约金");

  public static final List<String> IMPORT_PRODUCT_FILING_PRODUCT_LIST =
      ListUtil.toList("商品编码*", "数量", "单价");

  /** 采购价格库导入模板 */
  public static final List<String> IMPORT_PURCHASE_INFO_RECORD_TITLE_LIST =
      ListUtil.toList(
          "*采购组织名称",
          "*采购部门名称",
          "*供应商主数据编码",
          "供应商名称",
          "*物料编码",
          "*计划交货时间（天数）",
          "*税率",
          "*含税单价",
          "*货币码");

  /** 导出采购价格库模板 */
  public static final List<String> EXPORT_PURCHASE_INFO_RECORD_TITLE_LIST =
      ListUtil.toList(
          "采购组织", "计划交货时间", "采购部门", "供应商名称", "物料编码", "物料名称", "品牌", "规格型号",
          "物料描述",
          "税率", "含税单价","价格类型", "单位",
          "货币码", "有效期起始日", "有效期截至日", "SAP记录编号");

  /** 导出申请财务凭证模板 */
  public static final List<String> EXPORT_APPLICATION_FINANCIAL_VOUCHERS =
      ListUtil.toList(
          "SAP财务凭证号",
          "SAP会计年度",
          "采购组织",
          "凭证类型",
          "付款申请金额",
          "记录日期",
          "期望付款日期",
          "付款方式",
          "付款冻结状态",
          "付款状态",
          "银行回单",
          "SAP凭证行项目",
          "采购订单号",
          "供应商",
          "进项发票号",
          "订货金额",
          "凭证关联金额",
          "已退款金额",
          "关联付款申请单号");

  /** 导出付款申请财务凭证模板 */
  public static final List<String> EXPORT_PAYMENT_APPLICATION_FINANCIAL_VOUCHERS =
      ListUtil.toList(
          "SAP财务凭证号",
          "SAP会计年度",
          "采购组织",
          "凭证类型",
          "付款申请金额",
          "记录日期",
          "期望付款日期",
          "付款方式",
          "付款冻结状态",
          "付款状态",
          "银行回单",
          "SAP凭证行项目",
          "采购订单号",
          "供应商",
          "进项发票号",
          "订货金额",
          "凭证关联金额",
          "已退款金额",
          "关联付款申请单号");

  /** 导出发票过账财务凭证模板 */
  public static final List<String> EXPORT_PAYMENT_APPLICATION_INVOICE_VOUCHERS =
      ListUtil.toList(
          "SAP财务凭证号",
          "SAP会计年度",
          "采购组织",
          "凭证类型",
          "进项发票号",
          "凭证金额",
          "记录日期",
          "账期",
          "预计付款日期",
          "付款方式",
          "采购订单号",
          "供应商",
          "订货金额",
          "凭证关联金额",
          "抵消预付金额",
          "已提款金额",
          "退款金额",
          "剩余可提款金额",
          "关联付款申请");

  /** 导出采购申请单模板 */
  public static final List<String> EXPORT_PURCHASE_APPLY_FOR_ORDER_TITLE_LIST =
      ListUtil.toList(
          "采购申请单号",
          "采购申请类型",
          "订货状态",
          "取消状态",
          "创建日期",
          "客户订单号",
          "销售订单号",
          "跟单员",
          "业务员",
          "销售组织",
          "采购组织",
          "项目编码",
          "项目名称",
          "关联采购订单",
          "售达方",
          "收货人",
          "联系方式",
          "收件地址",
          "发货方式",
          "申请单备注",
          "物料序号",
          "物料编码",
          "仓库",
          "品牌",
          "物料名称",
          "规格型号",
          "单位",
          "描述",
          "申请数量",
          "已订货数量",
          "销售单价",
          "销售需求数量",
          "MPM参考结算价",
          "计划需求日期",
          "采购员",
          "采购部门");

  /**
   * 导出进项票列表模板
   */
  public static final List<String> EXPORT_INPUT_INVOICE_LIST =
      ListUtil.toList("进项发票单号", "源单类型", "客户订单号", "下单平台", "采购订单号",
         "供应商MDM编码","供应商名称", "采购组织", "采购员",
//          "采购部门",
          "发票单提交时间", "提交方式",
          "发票单价税合计", "关联单据价税合计", "提交人", "应处理人", "发票状态", "上次审核人",
          "审核时间", "SAP发票凭证号", "SAP会计年度", "SAP财务凭证号", "发票种类", "发票号码",
          "发票代码", "开票日期", "合计金额", "合计税额", "价税合计", "发票附件");

  /**
   * 导出采购单物料信息列表模板
   */
  public static final List<String> EXPORT_PURCHASE_ORDER_PRODUCT_LIST =
      ListUtil.toList("采购订单号", "订单状态", "订单类型", "创建时间", "供应商名称",
          "采购员", "物料编码", "品牌", "物料名称", "物料描述",
          "规格型号", "单位", "订货数量", "含税单价", "税率", "去税单价",
          "金额", "交货日期", "已发数量", "采购入库数量", "仓库", "备注",
          "采购申请单号", "业务员", "跟单员", "销售订单号","项目编码",
          "项目名称", "是否预付款", "是否上传合同");


  /** 采购订单导入模板 */
  public static final List<String> IMPORT_PURCHASE_TITLE_LIST =
      ListUtil.toList(
          "基础资料",
          "供方联系人",
          "收件信息",
          "物料明细");

  /** 前台物料信息下载 */
  public static final List<String> DOWNLOAD_WEB_PRODUCT_INFO =
      ListUtil.toList(
          "序号*",
          "物料编码*",
          "品牌",
          "名称",
          "描述",
          "规格型号",
          "单位",
          "物料税率",
          "加价系数",
          "MPM结算价",
          "订货数量（最大只支持三位小数）",
          "含税单价（最大只支持六位小数）",
          "税率（请输入：1%、0%、3%、6%、9%、10%、11%、12%、13%、16%、17%）",
          "备注",
          "附加费（最大只支持两位小数。您可以通过导入附加费的方式调整结算价）");

  /** 前台物料信息下载 */
  public static final List<String> DOWNLOAD_RETURN_EXCHANGE_ORDER_INFO =
      ListUtil.toList(
          "行id",
          "*物料编码",
          "品牌",
          "物料名称",
          "描述",
          "规格型号",
          "单位",
          "*退货数量（最大只支持三位小数）",
          "入库原价（去税）",
          "*本次退货含税单价（最大只支持六位小数字）",
          "*税率（请输入：1%、0%、3%、6%、9%、10%、11%、12%、13%、16%、17%）",
          "原单总价（去税）（最大只支持六位小数）",
          "*批次",
          "备注");
  /**
   * 调拨单物料信息下载
   */
  public static final List<String> DOWNLOAD_TRANSFER_ORDER_INFO =
      ListUtil.toList(
          "行id",
          "物料编码*",
          "品牌",
          "物料名称",
          "描述",
          "规格型号",
          "单位",
          "调拨数量（最大三位小数）",
          "批次",
          "备注");
  /**
   * 组装拆卸单物料信息下载
   */
  public static final List<String> DOWNLOAD_ASSEMBLY_DISASSEMBLY_ORDER_INFO =
      ListUtil.toList(
          "行id",
          "物料编码*",
          "品牌",
          "物料名称",
          "描述",
          "规格型号",
          "单位",
          "税率",
          "未税单价",
          "数量（最大三位小数）",
          "含税结算单价（最大两位小数）",
          "仓库编码",
          "批次",
          "备注");



  /**
   * 退库单导出
   */
  public static final List<String> EXPORT_OUT_BOUND_LIST =
      ListUtil.toList("采购订单号","供应商名称","采购员","采购部门", "退库时间", "退库原因", "退库仓库", "仓库执行状态",
          "物流公司", "快递单号", "收件人", "收件地址","是否需要开红票", "SAP物料凭证号",
          "SAP退库单采购订单号", "SAP退库单物料凭证行项目", "物料编码", "品牌", "物料名称",
          "型号", "退库数量","对应入库单入库数量", "已开红票数量", "关联发票号", "单位", "退库单价",
          "退库金额", "批号", "冲销状态");

  /**
   * 入库单导出
   */
  public static final List<String> EXPORT_WAREHOUSE_WARRANT_LIST =
      ListUtil.toList("采购订单号", "供应商名称", "物料编码", "品牌", "物料名称", "型号", "单位",
          "入库数量", "退库数量", "含税单价", "含税总额", "未税总额", "结算单价", "税率", "批号",
          "可开票数量", "已开票数量", "已开票金额", "关联发票号", "未开票数量", "未开票金额",
          "入库时间", "入库仓库", "物流公司", "快递单号", "采购员", "采购申请单号", "项目编码",
          "业务员", "销售订单号", "采购部门", "订单类型", "SAP物料凭证号", "SAP物料凭证行项目",
          "冲销状态", "来源");

  /**
   * 入库单已开票数量导入模板
   */
  public static final List<String> IMPORT_WAREHOUSE_INVOICE_NUM_TITLE_LIST =
      ListUtil.toList("*采购订单号","*SAP物料凭证号","*入库单sap物料凭证行项目","*已开票数量");


  /**
   * 财务凭证导入模板
   */
  public static final List<String> IMPORT_FINANCIAL_VOUCHER_TITLE_LIST =
      ListUtil.toList("SAP财务凭证号*", "SAP会计年度*", "采购组织ERP编码*",
          "凭证类型*（发票过账凭证、预付款凭证、应付款凭证）", "记录日期*(格式为：20240615)",
          "凭证金额*",
          "账期*（立即应付、7天之内、15天之内、30天之内、45天之内、60天之内、90天之内、180天之内、360天之内）",
          "付款方式（支付宝、商业汇票、银行保函、报销、银行汇票、现金支付、银行转账(含网银、转账支票、现金支票)、备用金、其它）",
          "采购订单号*", "凭证关联金额*", "抵消预付金额", "已提款金额", "付款申请金额*",
          "期望付款日期(格式为：20240615)",
          "付款方式（支付宝、商业汇票、银行保函、报销、银行汇票、现金支付、银行转账(含网银、转账支票、现金支票)、备用金、其它）",
          "SAP凭证行项目*", "采购订单号*", "进项发票号（应付款凭证填写）", "凭证关联金额*", "*订单订货金额（整单金额，具体到两位小数）", "*供应商主数据编码");

  /**
   * 落地商订单明细导出模版标题
   */
  public static final List<String> EXPORT_ORDER_DETAIL_TITLE_LIST =
      ListUtil.toList("客户订单号", "订单所属ERP", "订单状态", "下单时间", "派单时间",
          "最早发货时间", "下单平台", "下单金额", "销售金额", "供应商名称", "客户名称", "收货地址",
          "物料编码", "品牌", "物料名称", "物料描述", "规格型号", "单位", "数量", "含税单价",
          "税率", "去税单价", "金额");


  /**
   * 物料库存模版标题
   */
  public static String PRODUCT_STOCK_PLATFORM_ADDRESS_TITLE = "库存所在地请按照sheet2-平台地址信息进行填写。物料编码请填写咸亨物料编码。区域库存数量请填写整数。";

  /**
   * 物料库存模版导入(sheet1)
   */
  public static Map<String, Integer> PRODUCT_STOCK_PLATFORM_ADDRESS_LIST = new LinkedHashMap<>();
  static {
    PRODUCT_STOCK_PLATFORM_ADDRESS_LIST.put("*物料编码",40);
    PRODUCT_STOCK_PLATFORM_ADDRESS_LIST.put("物料名称",40);
    PRODUCT_STOCK_PLATFORM_ADDRESS_LIST.put("规格型号",40);
    PRODUCT_STOCK_PLATFORM_ADDRESS_LIST.put("*库存所在地编码（多个地点使用英文逗号分开）",40);
    PRODUCT_STOCK_PLATFORM_ADDRESS_LIST.put("*区域库存数量",40);
  }
  /**
   * 物料库存模版导入(sheet2)
   */
  public static Map<String, Integer> PRODUCT_STOCK_PLATFORM_ADDRESS = new LinkedHashMap<>();
  static {
    PRODUCT_STOCK_PLATFORM_ADDRESS.put("地址名称",20);
    PRODUCT_STOCK_PLATFORM_ADDRESS.put("地址编码",20);
  }

  /**
   * 退库单已开红票数量导入模板
   */
  public static final List<String> IMPORT_RETURN_RED_INVOICE_NUM_TITLE_LIST =
      ListUtil.toList("*采购订单号","*SAP物料凭证号","*退库单sap物料凭证行项目","*已开红票数量");

  /**
   * 付款单导出
   */
  public static final List<String> EXPORT_ORDER_PAYMENT_LIST =
      ListUtil.toList("付款单号", "付款状态", "付款数","申请付款金额", "已付金额", "提交人",
          "提交时间", "是否自动提款", "客户订单号", "下单平台", "签收凭证", "供应商开票",
          "客户回款方式", "回款比例", "银行交易流水号", "汇票号", "付款发起条件", "付款满足日期",
          "对应账期", "预计付款日期","付款方式","供应商","订单实际订货金额","本次提款金额");

  /**
   * 落地商订单发货明细导出模版标题
   */
  public static final List<String> EXPORT_ORDER_DELIVERY_DETAIL_TITLE_LIST =
      ListUtil.toList("客户订单号", "订单状态", "下单平台", "供应商名称", "发货时间", "物流公司",
          "快递单号", "发货状态", "入库单号", "入库采购订单号", "序号", "物料编码", "品牌",
          "商品名称", "型号", "发货数量", "单位", "单价", "已退货数量");

  /**
   * 落地商订单退货明细导出模版标题
   */
  public static final List<String> EXPORT_ORDER_RETURN_DETAIL_TITLE_LIST =
      ListUtil.toList("客户订单号", "订单状态", "下单平台", "供应商名称", "退货单编号", "退货时间",
          "退货金额", "退货状态", "采购退料单号", "退货单采购订单号", "序号", "物料编码", "品牌",
          "商品名称", "规格型号", "退货数量", "单位", "单价", "发货明细id");
  /**
   * 库存列表导出模版标题
   */
  public static final List<String> EXPORT_INVENTORY_LIST_TITLE_LIST =
      ListUtil.toList("序号", "物料编码", "库房名称", "批号", "品牌", "名称", "型号", "描述（物料备注）", "单位",
          "库存总数（不含寄售数量）", "寄售库存数量", "批次锁库数量", "可用数量", "产品经理", "项目报价员", "采购订单号",
          "采购员", "供应商名称", "税率%", "未税单价", "金蝶批次", "期初备注", "四级类目", "三级类目", "二级类目",
          "一级类目", "采购部门", "在库时间(天)");
  /**
   * 退换单导出模版标题
   */
  public static final List<String> EXPORT_RETURN_EXCHANGE_LIST_TITLE_LIST =
      ListUtil.toList("退换货订单号", "订单状态", "项目类别", "退库原因", "创建时间", "供应商名称", "采购员", "采购部门",
          "物料编码", "品牌", "物料名称", "物料描述", "规格型号", "单位", "退货数量", "含税单价", "税率", "总价",
          "已退数量", "取消退货数量", "剩余待退数量", "仓库", "备注", "退货原单号", "批次", "是否开红票", "开票进度",
          "是否折价", "附件");
  /**
   * 库存安全管理导出标题
   */
  public static final List<String> EXPORT_INVENTORY_SAFE_LIST_TITLE_LIST =
      ListUtil.toList("组织", "库房名称", "物料编码", "名称", "品牌", "型号", "基本单位",
          "安全库存数量", "负责人", "上次提醒时间", "下次提醒时间", "消息通知");
  /**
   * 库存安全管理导入标题
   */
  public static final List<String> IMPORT_INVENTORY_SAFE_LIST_TITLE_LIST =
      ListUtil.toList("*库房编码", "*库房名称", "*物料编码", "*安全库存数量",
          "*负责人（工号后四位+姓名）", "消息通知（开启/关闭）");
  /**
   * 组装拆卸单导出模版标题
   */
  public static final List<String> EXPORT_ASSEMBLY_DISASSEMBLY_LIST_TITLE_LIST =
      ListUtil.toList("订单号", "单据类型", "订单状态", "序号", "物料编码", "品牌", "物料名称",
          "型号", "单位", "数量", "仓库", "批次", "创建人", "创建时间", "审核人", "审核时间", "仓库执行员",
          "仓库执行时间", "SAP物料凭证号", "SAP物料凭证年份");

  /**
   * 需付款列表导出导出模版标题
   */
  public static final List<String> EXPORT_ORDER_NEED_PAYMENT_LIST_TITLE_LIST =
      ListUtil.toList("客户订单号", "下单平台", "下单时间","发货状态", "首次发货时间", "全部发货时间", "签收凭证", "签收凭证通过时间",
          "客户开票", "客户开票日期", "供应商开票状态", "供应商开票时间", "客户签收时间", "客户回款", "客户回款方式", "回款比例", "银行交易流水号", "汇票号",
          "付款发起条件", "付款满足日期", "对应账期","预计付款日期","付款方式","供应商","订单实际订货金额","可提款金额","已提款金额","已退款金额","剩余可提款金额","关联付款单");
  /**
   * 退库单V2导出
   */
  public static final List<String> EXPORT_OUT_BOUND_LIST_V2 =
      ListUtil.toList("采购订单号", "供应商名称", "采购员", "采购部门", "退库时间", "退库原因",
          "退库仓库", "仓库执行状态", "物流公司", "快递单号", "收件人", "收件地址",
          "是否需要开红票", "SAP物料凭证号", "SAP退库单采购订单号", "SAP退库单物料凭证行项目",
          "物料编码", "品牌", "物料名称", "规格", "型号", "退库数量", "对应入库单入库数量", "已开红票数量",
          "关联发票号", "单位", "退库单价", "退库金额", "批号", "冲销状态", "退库单号", "审核时间",
          "审批状态", "审批意见");
}
