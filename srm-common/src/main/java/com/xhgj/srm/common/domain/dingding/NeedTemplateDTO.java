package com.xhgj.srm.common.domain.dingding;

import com.alibaba.fastjson.annotation.JSONField;
import java.util.List;
import lombok.Builder;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @since 2022/9/30 13:38
 */
@Data
@Builder
public class NeedTemplateDTO {

  /**
   * 配置
   */
  @JSONField(name = "config")
  private ConfigDTO config;
  /**
   * 头
   */
  @JSONField(name = "header")
  private HeaderDTO header;
  /**
   * 内容
   */
  @JSONField(name = "contents")
  private List<ContentsDTO> contents;

  @Data
  @Builder
  public static class ConfigDTO {

    /**
     * 样式
     */
    @JSONField(name = "autoLayout")
    private Boolean autoLayout;
    @JSONField(name = "enableForward")
    private Boolean enableForward;
  }


  @Data
  @Builder
  public static class HeaderDTO {

    /**
     * 标头
     */
    @JSONField(name = "title")
    private TitleDTO title;


    @Data
    @Builder
    public static class TitleDTO {

      /**
       * 类型
       */
      @J<PERSON>NField(name = "type")
      private String type;
      /**
       * 文本
       */
      @J<PERSON>NField(name = "text")
      private String text;
    }
  }


  @Data
  @Builder
  public static class ContentsDTO {
    /**
     * 类型
     */
    @JSONField(name = "type")
    private String type;
    /**
     * 文本
     */
    @JSONField(name = "text")
    private String text;
    @JSONField(name = "id")
    private String id;
    @JSONField(name = "actions")
    private List<ActionsDTO> actions;


    @Data
    @Builder
    public static class ActionsDTO {

      @JSONField(name = "type")
      private String type;
      @JSONField(name = "label")
      private LabelDTO label;
      @JSONField(name = "actionType")
      private String actionType;
      @JSONField(name = "url")
      private UrlDTO url;
      @JSONField(name = "status")
      private String status;
      @JSONField(name = "id")
      private String id;
      @JSONField(name = "disabled")
      private Boolean disabled;
      @JSONField(name = "value")
      private String value;


      @Data
      @Builder
      public static class LabelDTO {

        @JSONField(name = "type")
        private String type;
        @JSONField(name = "text")
        private String text;
        @JSONField(name = "id")
        private String id;
      }


      @Data
      @Builder
      public static class UrlDTO {

        @JSONField(name = "all")
        private String all;
      }
    }
  }
}
