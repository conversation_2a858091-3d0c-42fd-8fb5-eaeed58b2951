package com.xhgj.srm.common.utils;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.domain.ExportExcelTitleAttr;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.StringUtils;
import java.io.*;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * Excel 导出类
 *
 * <AUTHOR>
 * @since 2019/4/29 18:03
 */
@Component
@Slf4j
public class ExportUtil {


  public void createRegion(Sheet sheet, int bIndex, int eIndex, int bCol, int eCol) {
        CellRangeAddress region = new CellRangeAddress(bIndex, eIndex, bCol, eCol);
        sheet.addMergedRegion(region);
    }

    public Sheet createSheet(Workbook workbook, String name, List<Integer> widths) {
        Sheet sheet = workbook.createSheet(name);
        if (widths != null) {
            for (int i = 0; i < widths.size(); i++) {
                sheet.setColumnWidth(i, widths.get(i) * 256);
            }
        }
        return sheet;
    }

    public void createTitle(List<String> titles, CellStyle style, Row titleRow) {
        for (int i = 0; i < titles.size(); i++) {
            String title = titles.get(i);
            createCell(titleRow, i, title, style, false, false);
        }
    }

    public void createCell(Row row, int colNum, Object value, CellStyle style) {
        createCell(row, colNum, value, style, false, false);
    }

    /**
     * 创建单元格
     *
     * @param row     行对象
     * @param colNum  列
     * @param value   值
     * @param style   样式
     * @param isNum   是否是数字
     * @param isMoney 是否是金额
     */
    public void createCell(
            Row row, int colNum, Object value, CellStyle style, boolean isNum, boolean isMoney) {
        Cell cell = row.createCell(colNum);
        if (isNum || isMoney) {
            cell.setCellType(CellType.NUMERIC);
            if (isMoney) {
                cell.setCellValue(Double.parseDouble(value.toString()));
            } else {
                cell.setCellValue(Integer.parseInt(value.toString()));
            }
        } else {
            if (value == null) {
                cell.setCellValue(" - ");
            } else {
                cell.setCellValue(value.toString());
            }
        }
        cell.setCellStyle(style);
    }

    public CellStyle getBaseStyle(Workbook book) {
        CellStyle style = book.createCellStyle();
        // 如果需要在单元格内换行加入下句，使用【\r\n】强制换行
        //    style.setWrapText(true);
        // 底线和颜色
        style.setBorderBottom(BorderStyle.THIN);
        style.setBottomBorderColor(IndexedColors.BLACK.index);
        // 设置左边线和颜色
        style.setBorderLeft(BorderStyle.THIN);
        style.setLeftBorderColor(IndexedColors.BLACK.index);
        // 设置右边线和颜色
        style.setBorderRight(BorderStyle.THIN);
        style.setRightBorderColor(IndexedColors.BLACK.index);
        // 设置上面线和颜色
        style.setBorderTop(BorderStyle.THIN);
        style.setTopBorderColor(IndexedColors.BLACK.index);
        // 水平居中
        style.setAlignment(HorizontalAlignment.CENTER);
        // 垂直居中
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        return style;
    }

    /**
     * 天蓝色单元格样式
     */
    public CellStyle getSkyBlueCellStyle(Workbook book) {
        return getColorfulCellStyle(book, IndexedColors.SKY_BLUE.index);
    }

    /**
     * 海洋绿色单元格样式
     */
    public CellStyle getSeaGreenCellStyle(Workbook book) {
        return getColorfulCellStyle(book, IndexedColors.SEA_GREEN.index);
    }

    /**
     * 浅橘色单元格样式
     */
    public CellStyle getLightOrangeCellStyle(Workbook book) {
        return getColorfulCellStyle(book, IndexedColors.LIGHT_ORANGE.index);
    }

  /**
   * 浅灰色单元格样式
   */
  public CellStyle getGrayCellStyle(Workbook book) {
    return getColorfulCellStyle(book, IndexedColors.GREY_25_PERCENT.index);
  }

    private CellStyle getColorfulCellStyle(Workbook book, short colorIndex) {
        CellStyle style = getBaseStyle(book);
        style.setFillForegroundColor(colorIndex);
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        return style;
    }

    public CellStyle getMoneyStyle(Workbook book) {
        CellStyle style = getBaseStyle(book);
        DataFormat format = book.createDataFormat();
        style.setDataFormat(format.getFormat("¥#,##0.00"));
        return style;
    }

    public CellStyle getTitleStyle(Workbook workbook) {
        if (workbook instanceof HSSFWorkbook) {
            return getHSSFTitleStyle((HSSFWorkbook) workbook);
        } else if (workbook instanceof XSSFWorkbook || workbook instanceof SXSSFWorkbook) {
            return getXSSFTitleStyle(workbook);
        } else {
            log.error("无法识别的Excel类型！");
            return null;
        }
    }

    public CellStyle getXSSFTitleStyle(Workbook workbook) {
        CellStyle styleTitle = getBaseStyle(workbook);
        Font font = workbook.createFont();
        font.setFontHeightInPoints((short) 12);
        font.setBold(true);
        styleTitle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        styleTitle.setFillForegroundColor(IndexedColors.YELLOW.index);
        styleTitle.setFont(font);
        return styleTitle;
    }

    public CellStyle getHSSFTitleStyle(HSSFWorkbook workbook) {
        CellStyle styleTitle = getBaseStyle(workbook);
        HSSFFont font = workbook.createFont();
        font.setFontHeightInPoints((short) 12);
        font.setBold(true);
        styleTitle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        styleTitle.setFillForegroundColor(IndexedColors.YELLOW.index);
        styleTitle.setFont(font);
        return styleTitle;
    }

    public boolean validateExcel(Sheet sheet, int titleRowIndex, List<String> titles) {
        if (sheet == null || titleRowIndex < 0 || CollectionUtils.isEmpty(titles)) {
            return false;
        }
        Row row = sheet.getRow(titleRowIndex);
        if (row == null) {
            return false;
        }
        try {
            for (int i = 0; i < titles.size(); i++) {
                Cell cell = row.getCell(i);
                if (!titles.get(i).equals(cell.getStringCellValue())) {
                    log.error(
                            "第【" + i + "】列标题不一致：【" + titles.get(i) + "】->【" + cell.getStringCellValue() + "】");
                    return false;
                }
            }
        } catch (Exception e) {
           log.error("Excel标题校验失败！"+ExceptionUtil.stacktraceToString(e));
            return false;
        }
        return true;
    }

    public String write(String filePath, Workbook workbook) throws Exception {
        String fullPath;
        File f = new File(filePath);
        if (!f.exists()) {
            f.mkdirs();
        }
        if (workbook instanceof XSSFWorkbook || workbook instanceof SXSSFWorkbook) {
            fullPath = filePath + ".xlsx";
        } else {
            fullPath = filePath + ".xls";
        }
        try( FileOutputStream out = new FileOutputStream(fullPath);) {
          write(out, workbook);
          return fullPath;
        }
    }

    public void write(OutputStream out, Workbook workbook) throws IOException {
        if (out == null || workbook == null) {
            return;
        }
        try {
            workbook.write(out);
            out.flush();
        } finally {
            out.close();
            workbook.close();
        }
    }

    public Workbook buildByFile(String fileName, InputStream inputStream) throws IOException {
        try {
          Workbook book = null;
          if (fileName.endsWith(".xls")) {
            book = new HSSFWorkbook(inputStream);
          } else if (fileName.endsWith(".xlsx")) {
            book = new XSSFWorkbook(inputStream);
          }
          return book;
        }finally {
            if (inputStream != null) {
                inputStream.close();
            }
        }
    }

    public String getCellStringValue(Cell cell) {
        String res = "";
        if (cell != null) {
            // XSSF 如果这个单元格类型本来就是 String 类型，重复设置会导致 getStringCellValue 取不到值
            if (cell.getCellType() != CellType.STRING) {
                cell.setCellType(CellType.STRING);
            }
            res = cell.getStringCellValue();
        }
        return res;
    }

    public String getMsg(String sign, String msg) {
        return sign + "：" + msg + "||";
    }

    public String getMsg(int index, String msg) {
        return getMsg(null, index, msg);
    }

    public String getMsg(String fileName, int index, String msg) {
        String res = "";
        if (!StrUtil.isEmptyOrUndefined(fileName)) {
            res += "Excel：||【" + fileName + "】";
        }
        res += "第" + index + "行提醒：" + msg + "||";
        return res;
    }

    public boolean isEmptyRow(Row row) {
        if (row == null || row.toString().isEmpty()) {
            return true;
        } else {
            boolean isEmpty = true;
            for (int i = row.getFirstCellNum(); i < row.getLastCellNum(); i++) {
                Cell cell = row.getCell(i);
                if (cell != null && cell.getCellType() != CellType.BLANK) {
                    isEmpty = false;
                    break;
                }
            }
            return isEmpty;
        }
    }

    public int simpleReadExcel(
            String fileName,
            InputStream inputStream,
            int sheetIndex,
            int titleRowIndex,
            List<String> titles,
            int readStartIndex,
            Function<Row, Integer> consumer)
            throws IOException {
      try (Workbook book = buildByFile(fileName, inputStream);) {
        if (book == null) {
          throw new CheckException("文件类型错误！");
        }
        Sheet sheet = book.getSheetAt(sheetIndex);
        if (!validateExcel(sheet, titleRowIndex, titles)) {
          throw new CheckException("非法表格，请使用从系统内下载的模板导入！");
        }
        Map<Integer, String> needValidateMap = new HashMap<>();
        for (int i = 0; i < titles.size(); i++) {
          String title = titles.get(i);
          if (title.contains("*")) {
            needValidateMap.put(i, title);
          }
        }
        int rowNums = sheet.getPhysicalNumberOfRows();
        int count = 0;
        for (int i = readStartIndex; i < rowNums; i++) {
          Row row = sheet.getRow(i);
          if (isEmptyRow(row)) {
            continue;
          }
          int rowIndex = i + 1;
          // 校验非空列
          StringBuilder validateColMsg = new StringBuilder();
          for (Entry<Integer, String> needValidateEntry : needValidateMap.entrySet()) {
            String cellValue = getCellStringValue(row.getCell(needValidateEntry.getKey()));
            if (StringUtils.isNullOrEmpty(cellValue)) {
              validateColMsg.append(
                  getMsg(rowIndex, "【" + needValidateEntry.getValue().replace("*", "") + "】为必填项"));
            }
          }
          if (!StringUtils.isNullOrEmpty(validateColMsg.toString())) {
            throw new CheckException("Excel 格式有误：" + validateColMsg.toString());
          }
          count += consumer.apply(row);
        }
        return count;
      }
    }

  public void setTableTitle(Sheet sheet, XSSFWorkbook book, List<ExportExcelTitleAttr> titleAttrs) {
    //按颜色分类的表头样式
    Map<Short, CellStyle> colorToCellStyleMap = new HashMap<>();
    for (ExportExcelTitleAttr titleAttr : titleAttrs) {
      String title = titleAttr.getTitle();
      short backgroundColor = titleAttr.getBackgroundColor();
      boolean needRegion = titleAttr.isNeedRegion();
      int rowIndex = titleAttr.getRowIndex();
      int colIndex = titleAttr.getColIndex();
      sheet.setColumnWidth(colIndex, titleAttr.getCellWidth() * 256);
      Row row = sheet.getRow(rowIndex);
      if (row == null) {
        row = sheet.createRow(rowIndex);
      }
      if (needRegion) {
        createRegion(sheet, titleAttr.getBIndex(), titleAttr.getEIndex(), titleAttr.getBCol(),
            titleAttr.getECol());
      }
      CellStyle cellStyle = colorToCellStyleMap.get(backgroundColor);
      if (cellStyle == null) {
        cellStyle = getTitleStyle(book);
        cellStyle.setFillForegroundColor(backgroundColor);
        colorToCellStyleMap.put(backgroundColor, cellStyle);
      }
      createCell(row, colIndex, title, cellStyle);
    }
  }

}
