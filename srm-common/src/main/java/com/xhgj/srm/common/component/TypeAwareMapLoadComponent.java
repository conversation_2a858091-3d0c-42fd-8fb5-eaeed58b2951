package com.xhgj.srm.common.component;/**
 * @since 2024/11/19 14:33
 */

import org.springframework.stereotype.Component;
import javax.annotation.PostConstruct;

/**
 *<AUTHOR>
 *@date 2024/11/19 14:33:28
 *@description 加载TypeAwareMap执行static代码块
 */
@Component
public class TypeAwareMapLoadComponent {
  @PostConstruct
  public void load() {
    try {
      Class.forName("com.xhgj.srm.common.map.TypeAwareMap");
    } catch (ClassNotFoundException e) {
      e.printStackTrace();
    }
  }
}
