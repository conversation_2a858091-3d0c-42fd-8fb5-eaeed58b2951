package com.xhgj.srm.common.utils;

import cn.hutool.core.map.MapUtil;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2020/5/29 15:14
 */
public class MapUtils {
  public static String getKey(Map<String, String> map, String value) {
    String key = "";
    if (MapUtil.isNotEmpty(map)) {
      for (Entry<String, String> entry : map.entrySet()) {
        if (Objects.equals(entry.getValue(), value)) {
          key = entry.getKey();
        }
      }
    }
    return key;
  }
}
