package com.xhgj.srm.common;

public class Constants_Batch {

  public static final String UPLOAD_FILE_DIR_TASK_IMPORT = "/srm/upload/batch_task/import/";
  /** 批量任务来源 */
  public static final String SOURCE_TYPE = "2";

  /*supplier端 */
  /** 预导入
   * @deprecated 该类型任务已经废弃，不会再产生新数据
   * @see Constants_Batch#BATCH_TASK_PRODUCT_IN
   * */
  public static final String BATCH_TASK_YDR = "1";

  /** 预导出 */
  public static final String BATCH_TASK_YDC = "2";

  /** 属性覆盖 */
  public static final String BATCH_TASK_SXFG = "3";

  /** ***** admin端 ******* */
  /** 批量修改供应商负责采购 */
  public static final String BATCH_TASK_PGGYSFZCZ = "4";

  /** 批量修改合同负责采购 */
  public static final String BATCH_TASK_PGHTFZR = "5";

  /** 导入询价 */
  public static final String BATCH_TASK_INQUIRY = "6";

  /** 合同导出 */
  public static final String BATCH_TASK_DCHT = "7";

  /** 供应商导出 */
  public static final String BATCH_TASK_DCGYS = "8";

  /** 报备单导入 */
  public static final String BATCH_TASK_FILLING_IN = "9";

  /** 报备单导入 */
  public static final String BATCH_TASK_FILLING_OUT = "10";

  /** 导入组织 */
  public static final String BATCH_TASK_GROUP_IN = "11";

  /** 导出组织 */
  public static final String BATCH_TASK_GROUP_OUT = "12";

  /** 导入部门 */
  public static final String BATCH_TASK_DEPARTMENT_IN = "13";

  /** 导出部门 */
  public static final String BATCH_TASK_DEPARTMENT_OUT = "14";

  /** 导出供应商列表 */
  public static final String BATCH_TASK_SUPPLIER_IN_GROUP_OUT = "15";

  /** 导入供应商列表 */
  public static final String BATCH_TASK_SUPPLIER_IN_GROUP_IN = "16";

  /** 导入用户 */
  public static final String BATCH_TASK_USER_IN = "17";

  /** 导出用户 */
  public static final String BATCH_TASK_USER_OUT = "18";

  /** 导入批改联系人 */
  public static final String BATCH_TASK_IMPORT_CORRECTION_CONTACT = "19";

  /** 导出供应商订单明细 */
  public static final String BATCH_TASK_EXPORT_SUPPLIER_ORDER_DETAILS = "20";

  /** 导出供应商订单 */
  public static final String BATCH_TASK_EXPORT_SUPPLIER_ORDER = "21";

  /** 前台导出供应商订单明细 */
  public static final String BATCH_TASK_EXPORT_SUPPLIER_ORDER_DETAILS_SUPPLIER = "22";

  /** 前台导出供应商订单 */
  public static final String BATCH_TASK_EXPORT_SUPPLIER_ORDER_SUPPLIER = "23";

  /** 后台导出 落地商订单 */
  public static final String BATCH_TASK_EXPORT_ORDER = "25";

  /** 导出落地商开票列表 */
  public static final String BATCH_TASK_ORDER_INVOICE = "26";

  /** 导出落地商订单物料明细 */
  public static final String BATCH_TASK_EXPORT_ORDER_PRODUCT_DETAIL = "27";

  /**
   * 导出供应商账号信息
   *
   * @deprecated 已迁移
   */
  public static final String BATCH_TASK_EXPORT_SUPPLIER_ACCOUNT_INFO = "28";

  /** 物料批量导入 */
  public static final String BATCH_TASK_PRODUCT_IN = "29";

  /** 上架项目批量导入 */
  public static final String BATCH_TASK_PROJECT_IN = "30";

  /** 物料图片批量导入 */
  public static final String BATCH_TASK_PICTURE_IN = "31";

  /** 落地商履约信息导入 */
  public static final String BATCH_TASK_LANDING_MERCHANT_PERFORMANCE_INFO_IN = "32";

  /** 对账单导出 */
  public static final String BATCH_TASK_ORDER_ACCOUNT_OUT = "33";

  /** 落地商所有订单导出 */
  public static final String BATCH_TASK_EXPORT_ORDER_NEW = "34";

  /**
   * 物料明细导出
   *
   * @deprecated 已迁移
   */
  public static final String BATCH_TASK__PRODUCT_DETAIL = "35";

  /** 物料库存导出 */
  public static final String BATCH_TASK_PRODUCT_STOCK = "36";

  /** 物料库存导入 */
  public static final String BATCH_TASK_PRODUCT_STOCK_IN = "37";

  /** 落地商合同信息导入 */
  public static final String BATCH_TASK_CONTRACT_IMPORT = "38";

  /** 落地商合同附件导入 */
  public static final String BATCH_TASK_IMPORT_CONTRACT_FILE = "39";

  /** 导出采购价格库 */
  public static final String BATCH_TASK_EXPORT_PURCHASE_INFO_RECORD = "41";

  /** 后台导出 采购申请单 */
  public static final String BATCH_TASK_EXPORT_PURCHASE_APPLY_FOR_ORDER = "42";

  /** 批量修改供应商注册地址 */
  public static final String BATCH_TASK_SUPPLIER_REGISTERED_ADDRESS = "44";

  /** 后台导入 - 落地商订单关联单号导入 */
  public static final String BATCH_TASK_IMPORT_RELATION_TICKET = "43";

  public static final String BATCH_TASK_SUPPLIER_EMPOWER = "45";

  /** 导入采购订单 */
  public static final String BATCH_TASK_EXPORT_PURCHASE_ORDER = "46";

  /**
   * 后台导出 进项票列表
   *
   * @deprecated 已迁移
   */
  public static final String BATCH_TASK_EXPORT_INPUT_INVOICE = "47";

  /** 导出采购单物料信息 */
  public static final String BATCH_TASK_EXPORT_SUPPLIER_ORDER_PRODUCT_DETAIL = "48";

  /** 落地商合导出 */
  public static final String THE_GROUND_QUOTIENT_IS_DERIVED = "49";

  /** 打印-批量打印采购申请单 */
  public static final String BATCH_PRINT_OUT_PURCHASE_APPLY_ORDER_ACCEPT_TEMP = "50";

  /**
   * 入库单导出
   *
   * @deprecated 已迁移
   */
  public static final String BATCH_TASK_EXPORT_STORAGE_ORDER = "51";

  /**
   * 退库单导出
   *
   * @deprecated 已迁移
   */
  public static final String BATCH_TASK_EXPORT_RETURN_STORAGE_ORDER = "52";

  /** 导入-入库单已开票数量 */
  public static final String BATCH_TASK_IMPORT_WAREHOUSE_INVOICE_NUM = "53";

  /** 导入-财务凭证 */
  public static final String BATCH_TASK_IMPORT_FINANCIAL_VOUCHER = "54";

  /** 付款申请财务凭证导出 */
  public static final String BATCH_TASK_EXPORT_PAYMENT_APPLICATION_FINANCIAL_VOUCHERS = "55";

  /** 落地商订单明细导出 */
  public static final String BATCH_TASK_EXPORT_ORDER_DETAIL = "56";

  /** 付款发票过账凭证导出 */
  public static final String BATCH_TASK_EXPORT_PAYMENT_APPLICATION_INVOICE_VOUCHERS = "57";

  /**
   * 付款申请单导出
   *
   * @deprecated 已迁移
   */
  public static final String BATCH_TASK_EXPORT_PAYMENT_APPLICATION = "58";

  /** 导入-退库单已开红票数量 */
  public static final String BATCH_TASK_IMPORT_RETURN_RED_INVOICE_NUM = "59";

  /** 导出-落地商订单发货明细 */
  public static final String BATCH_TASK_EXPORT_ORDER_DELIVERY_DETAIL = "60";

  /** 导出-落地商订单退货明细 */
  public static final String BATCH_TASK_EXPORT_ORDER_RETURN_DETAIL = "61";

  /**
   * 导出入驻报备单
   */
  public static final String BATCH_TASK_EXPORT_ENTRY_REGISTRATION = "62";

  /**
   * 供应商品牌导入批改 占位符--实际fc任务类型为 64
   */
  public static final String BATCH_TASK_IMPORT_SUPPLIER_BRAND = "64";

  /**
   * 供应商类目导入批改 占位符--实际fc任务类型为 65
   */
  public static final String BATCH_TASK_IMPORT_SUPPLIER_CATEGORY = "65";

  /**
   * 供应商等级导入批改 占位符--实际fc任务类型为 66
   */
  public static final String BATCH_TASK_IMPORT_SUPPLIER_GRADE = "66";

  /**
   * 库存列表导出 占位符--实际fc任务类型为 67
   */
  public static final String BATCH_TASK_EXPORT_INVENTORY = "67";
  /**
   * 退换货订单导出 占位符--实际fc任务类型为 68
   */
  public static final String BATCH_TASK_EXPORT_RETURN_EXCHANGE = "68";

  /** 打印-批量打印采购申请单（产业） */
  public static final String BATCH_PRINT_OUT_PURCHASE_APPLY_ORDER_ACCEPT_TEMP_V2 = "69";
}
