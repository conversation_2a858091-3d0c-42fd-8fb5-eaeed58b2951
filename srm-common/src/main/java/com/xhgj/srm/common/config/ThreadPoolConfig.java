package com.xhgj.srm.common.config;

import com.xhgj.srm.common.handler.CustomRejectedExecutionHandler;
import com.xhiot.boot.core.common.decorator.MdcTaskDecorator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

/**
 * create by Geng Shy on 2024.1.15
 */
@Configuration
public class ThreadPoolConfig {

  @Bean("IoIntensiveThreadPool")
  public ThreadPoolTaskExecutor buildIoIntensiveThreadPool() {
    ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
    int availableProcessors = Runtime.getRuntime().availableProcessors();
    // 核心线程数
    executor.setCorePoolSize(availableProcessors);
    // 最大线程数
    executor.setMaxPoolSize(availableProcessors * 2);
    int queueSize = availableProcessors * 10;
    // 队列容量
    executor.setQueueCapacity(queueSize);
    // 线程空闲时间（单位：秒）
    executor.setKeepAliveSeconds(60);
    // 线程前缀名
    executor.setThreadNamePrefix("IoIntensiveThreadPool-");
    // 允许核心线程超时后被回收
    executor.setAllowCoreThreadTimeOut(true);
    executor.setRejectedExecutionHandler(new CustomRejectedExecutionHandler());
    // 设置装饰器，赋值traceId
    executor.setTaskDecorator(new MdcTaskDecorator());
    // 初始化
    executor.initialize();
    return executor;
  }
}
