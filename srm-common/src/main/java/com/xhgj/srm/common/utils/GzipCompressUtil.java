package com.xhgj.srm.common.utils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;

/**
 * 使用gzip对大文本字段进行解压缩
 */
public class GzipCompressUtil {

  /**
   * @description: zip压缩解压并使用Base64进行编码工具类
   *  JDK内置支持，无需额外依赖
   **/
  public static String compress(String primStr) {
    if (primStr == null || primStr.length() == 0) {
      return primStr;
    }
    ByteArrayOutputStream out = new ByteArrayOutputStream();
    GZIPOutputStream gzip = null;
    try {
      gzip = new GZIPOutputStream(out);
      gzip.write(primStr.getBytes());
    } catch (IOException e) {
      e.printStackTrace();
    } finally {
      if (gzip != null) {
        try {
          gzip.close();
        } catch (IOException e) {
          e.printStackTrace();
        }
      }
    }
    return new sun.misc.BASE64Encoder().encode(out.toByteArray());
  }
  public static String decompress(String compressedStr) {
    if (compressedStr == null || compressedStr.length() == 0) {
      return compressedStr;
    }
    ByteArrayOutputStream out = new ByteArrayOutputStream();
    ByteArrayInputStream in = null;
    GZIPInputStream ginzip = null;
    byte[] compressed = null;
    String decompressed = null;
    try {
      compressed = new sun.misc.BASE64Decoder().decodeBuffer(compressedStr);
      in = new ByteArrayInputStream(compressed);
      ginzip = new GZIPInputStream(in);

      byte[] buffer = new byte[1024];
      int offset = -1;
      while ((offset = ginzip.read(buffer)) != -1) {
        out.write(buffer, 0, offset);
      }
      decompressed = out.toString();
    } catch (IOException e) {
      e.printStackTrace();
    } finally {
      if (ginzip != null) {
        try {
          ginzip.close();
        } catch (IOException e) {
        }
      }
      if (in != null) {
        try {
          in.close();
        } catch (IOException e) {
        }
      }
      if (out != null) {
        try {
          out.close();
        } catch (IOException e) {
        }
      }
    }
    return decompressed;
  }
}
