package com.xhgj.srm.common;

import com.google.common.collect.ImmutableMap;
import java.math.BigDecimal;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/12/29 19:45
 */
public class Constants_PurchaseInfoRecord {

  /** 默认有效期截止时间：9999-12-31 23:59:59 */
  public static final long DEFAULT_VALID_DATE_END = 253402271999000L;

  /** 税率 -> 税率编码映射表 */
  public static final Map<String, String> TAX_RATE_MAP =
      ImmutableMap.<String, String>builder()
          .put("0%", "J0")
          .put("1%", "JA")
          .put("17%", "J1")
          .put("16%", "J2")
          .put("13%", "J3")
          .put("12%", "J4")
          .put("11%", "J5")
          .put("10%", "J6")
          .put("9%", "J7")
          .put("6%", "J8")
          .put("3%", "J9")
          .put("5%", "JB")
          .build();

  /**
   * 项目类别是寄售时：默认价格
   */
  public static final BigDecimal PROJECT_TYPE_JS_DEFAULT_PRICE = new BigDecimal("0.01");
  /**
   * 项目类别是寄售时：srmId
   */
  public static final String PROJECT_TYPE_JS_DEFAULT_SRM_ID = "666";

  /**
   * 默认采购信息记录分类
   */
  public static final String DEFAULT_ESOKZ = "0";
  /**
   * 项目类别是寄售时：采购信息记录分类
   */
  public static final String PROJECT_TYPE_JS_ESOKZ = "2";
}
