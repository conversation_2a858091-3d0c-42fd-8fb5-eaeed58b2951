package com.xhgj.srm.common.utils;

import com.xhgj.srm.common.utils.runner.JedisUtil;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * 订单编号生成规则
 *
 * <AUTHOR>
 * @date 2023-12-18
 */
public class OrderNumUtil {
  private int seq = 0;
  private static final JedisUtil jedisUtil = JedisUtil.getInstance();
  private static final String ORDERNUM6SRM_REDISKEY = "orderno6srm_counter"; //

  private static class OrderNumContainer {
    private static OrderNumUtil instance = new OrderNumUtil();
  }

  public static OrderNumUtil getInstance() {
    return OrderNumContainer.instance;
  }
  // 获取今日订单顺序号
  public synchronized String getSeq() {
    LocalDate currentDate = LocalDate.now();
    String datetime = currentDate.format(DateTimeFormatter.ofPattern("yyMM"));
    if (jedisUtil.exists(ORDERNUM6SRM_REDISKEY)) {
      String tempStr = jedisUtil.get(ORDERNUM6SRM_REDISKEY);
      String[] numStr = tempStr.split(",");
      if (!datetime.equals(numStr[0])) {
        // 重新从1开始
        this.seq = 1;
      } else {
        this.seq = Integer.parseInt(numStr[1]) + 1;
      }
    } else {
      // 拿不到key 分两种情况：1、redis服务异常；2、redis初始安装
      if (jedisUtil.checkConnection()) {
        this.seq = 1;
      } else {
        // redis服务异常
        return "0";
      }
    }
    jedisUtil.set(ORDERNUM6SRM_REDISKEY, datetime + "," + this.seq);

    String result = String.valueOf(seq);
    if (result.length() < 6) {
      int mun = 6 - result.length();
      for (int i = 0; i < mun; i++) {
        result = "0" + result;
      }
    }
    result = (datetime.replace("-", "")) + result;
    return result;
  }


}
