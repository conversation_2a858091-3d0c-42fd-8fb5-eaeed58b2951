package com.xhgj.srm.common.domain.dingding;

import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2022/12/5 10:21
 */
@Builder
@Data
@Accessors(chain = true)
@NoArgsConstructor
public class BatchSendOTORequest {
  /** 机器人的robotCode */
  private String robotCode;

  /** 被推送会话人员的userId列表 */
  private java.util.List<String> userIds;

  /** 消息的msgKey */
  private String msgKey;
  /** 消息体 */
  private String msgParam;

  public BatchSendOTORequest(String robotCode, List<String> userIds, String msgKey,
      String msgParam) {
    this.robotCode = robotCode;
    this.userIds = userIds;
    this.msgKey = msgKey;
    this.msgParam = msgParam;
  }
}
