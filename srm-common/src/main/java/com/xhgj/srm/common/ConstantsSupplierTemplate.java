package com.xhgj.srm.common;

import cn.hutool.core.collection.CollUtil;
import com.xhgj.srm.common.config.SupplierRecommendedLevelConfig;
import com.xhgj.srm.common.enums.supplier.SupplierLevelEnum;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022/7/7 8:59
 */
public class ConstantsSupplierTemplate {

  /* ---------供应商模板常量----- */
  /** 企业名称 key（存放于数据的字段名称） */
  public static final String ENTERPRISE_NAME = "enterpriseName";
  /** 企业名称 */
  public static final String ENTERPRISE_NAME_STR = "企业名称";
  /** 国家 key（存放于数据的字段名称） */
  public static final String COUNTRY = "country";
  /** 国家 */
  public static final String COUNTRY_STR = "国家";
  /** 省份 key（存放于数据的字段名称） */
  public static final String PROVINCE = "province";
  /** 省份 */
  public static final String PROVINCE_STR = "省份";
  /** 城市 key（存放于数据的字段名称） */
  public static final String CITY = "city";
  /** 城市 */
  public static final String CITY_STR = "城市";
  /** 行业 key（存放于数据的字段名称） */
  public static final String INDUSTRY = "industry";
  /** 行业 */
  public static final String INDUSTRY_STR = "行业";
  /** 等级 key（存放于数据的字段名称） */
  public static final String ENTERPRISE_LEVEL = "enterpriseLevel";
  /** 等级 */
  public static final String ENTERPRISE_LEVEL_STR = "等级";
  /** 供应商性质 key（存放于数据的字段名称） */
  public static final String ENTERPRISE_NATURE = "enterpriseNature";
  /** 供应商性质 */
  public static final String ENTERPRISE_NATURE_STR = "供应商性质";
  /** 统一社会信用代码 key（存放于数据的字段名称） */
  public static final String USCC = "uscc";
  /** 统一社会信用代码 */
  public static final String USCC_STR = "统一社会信用代码";
  /** 营业执照 key（存放于数据的字段名称） */
  public static final String LICENSE_URL = "licenseUrl";
  /** 营业执照 */
  public static final String LICENSE_URL_STR = "营业执照";
  /** 邮寄地址 key（存放于数据的字段名称） */
  public static final String DETAILS = "details";
  /** 邮寄地址 */
  public static final String DETAILS_STR = "邮寄地址";
  /** 发票类型 key（存放于数据的字段名称） */
  public static final String INVOICE_TYPE = "invoiceType";
  /** 发票类型 */
  public static final String INVOICE_TYPE_STR = "发票类型";
  /** 默认税率 key（存放于数据的字段名称） */
  public static final String TAX_RATE = "taxRate";
  /** 默认税率 */
  public static final String TAX_RATE_STR = "默认税率";
  /** 结算币别 key（存放于数据的字段名称） */
  public static final String SETTLE_CURRENCY = "settleCurrency";
  /** 结算币别 */
  public static final String SETTLE_CURRENCY_STR = "结算币别";
  /** 开户银行 key（存放于数据的字段名称） */
  public static final String BANK_NAME = "bankName";
  /** 开户银行 */
  public static final String BANK_NAME_STR = "开户银行";
  /** 银行账号 key（存放于数据的字段名称） */
  public static final String BANK_NUM = "bankNum";
  /** 银行账号 */
  public static final String BANK_NUM_STR = "银行账号";
  /** 账户名称 key（存放于数据的字段名称） */
  public static final String BANK_ACCOUNT = "bankAccount";
  /** 账户名称 */
  public static final String BANK_ACCOUNT_STR = "账户名称";
  /** 开户行地址 key（存放于数据的字段名称） */
  public static final String BANK_ADDRESS = "bankAddress";
  /** 开户行地址 */
  public static final String BANK_ADDRESS_STR = "开户行地址";
  /** 联行号 key（存放于数据的字段名称） */
  public static final String BANK_CODE = "bankCode";
  /** 联行号 */
  public static final String BANK_CODE_STR = "联行号";
  /** swiftCode key（存放于数据的字段名称） */
  public static final String SWIFT_CODE = "swiftCode";
  /** swiftCode */
  public static final String SWIFT_CODE_STR = "swiftCode";
  /** 开户许可证 key（存放于数据的字段名称） */
  public static final String ACCOUNT_URL = "accountUrl";
  /** 开户许可证 */
  public static final String ACCOUNT_URL_STR = "开户许可证";
  /** 姓名 key（存放于数据的字段名称） */
  public static final String NAME = "name";
  /** 姓名 */
  public static final String NAME_STR = "姓名";
  /** 性别 key（存放于数据的字段名称） */
  public static final String SEX = "sex";
  /** 性别 */
  public static final String SEX_STR = "性别";
  /** 区域负责 key（存放于数据的字段名称） */
  public static final String AREA = "area";
  /** 区域负责 */
  public static final String AREA_STR = "区域负责";
  /** 联系方式 key（存放于数据的字段名称） */
  public static final String PHONE = "phone";
  /** 联系方式 */
  public static final String PHONE_STR = "联系方式";
  /** 邮箱 key（存放于数据的字段名称） */
  public static final String MAIL = "mail";
  /** 邮箱 */
  public static final String MAIL_STR = "邮箱";
  /** 职务 key（存放于数据的字段名称） */
  public static final String DUTY = "duty";
  /** 职务 */
  public static final String DUTY_STR = "职务";
  /**
   * 法人 key
   */
  public static final String CORPORATE = "corporate";
  /**
   * 法人
   */
  public static final String CORPORATE_STR = "法人";

  /**
   * 注册地址 key
   */
  public static final String REG_ADDRESS = "regAddress";
  /**
   * 注册地址
   */
  public static final String REG_ADDRESS_STR = "注册地址";

  public static final Map<String, String> SUPPLIER_LEVEL_LINK_BRAND = new LinkedHashMap<>(5);
  public static final Map<String, String> SUPPLIER_LEVEL_LINK_CATEGORY = new LinkedHashMap<>(5);
  public static final Map<String, String> SUPPLIER_RECOMMENDED_LEVEL = new LinkedHashMap<>(1);
  public static final Map<String, String> SUPPLIER_LEVEL_LINK_EVALUATION_TABLE =
      new LinkedHashMap<>(5);


  /** 国内供应商模板 */
  public static final Map<String, Map<String, String>> SUPPLIER_TEMPLATE_CHINA =
      new LinkedHashMap<>(16);
  /** 国内供应商模板基本信息 */
  public static final Map<String, String> SUPPLIER_TEMPLATE_CHINA_BASE_INFO =
      new LinkedHashMap<>(16);

  static {
    SUPPLIER_TEMPLATE_CHINA_BASE_INFO.put(ENTERPRISE_NAME, ENTERPRISE_NAME_STR);
    SUPPLIER_TEMPLATE_CHINA_BASE_INFO.put(PROVINCE, PROVINCE_STR);
    SUPPLIER_TEMPLATE_CHINA_BASE_INFO.put(CITY, CITY_STR);
    SUPPLIER_TEMPLATE_CHINA_BASE_INFO.put(INDUSTRY, INDUSTRY_STR);
    SUPPLIER_TEMPLATE_CHINA_BASE_INFO.put(ENTERPRISE_LEVEL, ENTERPRISE_LEVEL_STR);
    SUPPLIER_TEMPLATE_CHINA_BASE_INFO.put(ENTERPRISE_NATURE, ENTERPRISE_NATURE_STR);
    SUPPLIER_TEMPLATE_CHINA_BASE_INFO.put(USCC, USCC_STR);
    SUPPLIER_TEMPLATE_CHINA_BASE_INFO.put(LICENSE_URL, LICENSE_URL_STR);
    SUPPLIER_TEMPLATE_CHINA_BASE_INFO.put(DETAILS, DETAILS_STR);
    SUPPLIER_TEMPLATE_CHINA_BASE_INFO.put(CORPORATE, CORPORATE_STR);
    SUPPLIER_TEMPLATE_CHINA_BASE_INFO.put(REG_ADDRESS, REG_ADDRESS_STR);
  }
  /** 国内供应商模板财务信息 */
  public static final Map<String, String> SUPPLIER_TEMPLATE_CHINA_FINANCE_INFO =
      new LinkedHashMap<>(16);

  static {
    SUPPLIER_TEMPLATE_CHINA_FINANCE_INFO.put(INVOICE_TYPE, INVOICE_TYPE_STR);
    SUPPLIER_TEMPLATE_CHINA_FINANCE_INFO.put(TAX_RATE, TAX_RATE_STR);
    SUPPLIER_TEMPLATE_CHINA_FINANCE_INFO.put(SETTLE_CURRENCY, SETTLE_CURRENCY_STR);
    SUPPLIER_TEMPLATE_CHINA_FINANCE_INFO.put(BANK_NAME, BANK_NAME_STR);
    SUPPLIER_TEMPLATE_CHINA_FINANCE_INFO.put(BANK_NUM, BANK_NUM_STR);
    SUPPLIER_TEMPLATE_CHINA_FINANCE_INFO.put(BANK_ACCOUNT, BANK_ACCOUNT_STR);
    SUPPLIER_TEMPLATE_CHINA_FINANCE_INFO.put(BANK_ADDRESS, BANK_ADDRESS_STR);
    SUPPLIER_TEMPLATE_CHINA_FINANCE_INFO.put(BANK_CODE, BANK_CODE_STR);
    SUPPLIER_TEMPLATE_CHINA_FINANCE_INFO.put(SWIFT_CODE, SWIFT_CODE_STR);
    SUPPLIER_TEMPLATE_CHINA_FINANCE_INFO.put(ACCOUNT_URL, ACCOUNT_URL_STR);
  }
  /** 国内供应商模板联系人 */
  public static final Map<String, String> SUPPLIER_TEMPLATE_CHINA_CONTACT_INFO =
      new LinkedHashMap<>(16);

  static {
    SUPPLIER_TEMPLATE_CHINA_CONTACT_INFO.put(NAME, NAME_STR);
    SUPPLIER_TEMPLATE_CHINA_CONTACT_INFO.put(SEX, SEX_STR);
    SUPPLIER_TEMPLATE_CHINA_CONTACT_INFO.put(AREA, AREA_STR);
    SUPPLIER_TEMPLATE_CHINA_CONTACT_INFO.put(PHONE, PHONE_STR);
    SUPPLIER_TEMPLATE_CHINA_CONTACT_INFO.put(MAIL, MAIL_STR);
    SUPPLIER_TEMPLATE_CHINA_CONTACT_INFO.put(DUTY, DUTY_STR);
  }


  /** 国外供应商模板 */
  public static final Map<String, Map<String, String>> SUPPLIER_TEMPLATE_ABROAD =
      new LinkedHashMap<>(16);
  /** 国外供应商模板基本信息 */
  public static final Map<String, String> SUPPLIER_TEMPLATE_ABROAD_BASE_INFO =
      new LinkedHashMap<>(16);

  static {
    SUPPLIER_TEMPLATE_ABROAD_BASE_INFO.put(ENTERPRISE_NAME, ENTERPRISE_NAME_STR);
    SUPPLIER_TEMPLATE_ABROAD_BASE_INFO.put(COUNTRY, COUNTRY_STR);
    SUPPLIER_TEMPLATE_ABROAD_BASE_INFO.put(ENTERPRISE_LEVEL, ENTERPRISE_LEVEL_STR);
  }
  /** 国外供应商模板财务信息 */
  public static final Map<String, String> SUPPLIER_TEMPLATE_ABROAD_FINANCE_INFO =
      new LinkedHashMap<>(16);

  static {
    SUPPLIER_TEMPLATE_ABROAD_FINANCE_INFO.put(INVOICE_TYPE, INVOICE_TYPE_STR);
    SUPPLIER_TEMPLATE_ABROAD_FINANCE_INFO.put(TAX_RATE, TAX_RATE_STR);
    SUPPLIER_TEMPLATE_ABROAD_FINANCE_INFO.put(SETTLE_CURRENCY, SETTLE_CURRENCY_STR);
    SUPPLIER_TEMPLATE_ABROAD_FINANCE_INFO.put(BANK_NAME, BANK_NAME_STR);
    SUPPLIER_TEMPLATE_ABROAD_FINANCE_INFO.put(BANK_NUM, BANK_NUM_STR);
    SUPPLIER_TEMPLATE_ABROAD_FINANCE_INFO.put(BANK_ACCOUNT, BANK_ACCOUNT_STR);
    SUPPLIER_TEMPLATE_ABROAD_FINANCE_INFO.put(BANK_ADDRESS, BANK_ADDRESS_STR);
    SUPPLIER_TEMPLATE_ABROAD_FINANCE_INFO.put(BANK_CODE, BANK_CODE_STR);
    SUPPLIER_TEMPLATE_ABROAD_FINANCE_INFO.put(SWIFT_CODE, SWIFT_CODE_STR);
  }

  /** 国外供应商模板联系人 */
  public static final Map<String, String> SUPPLIER_TEMPLATE_ABROAD_CONTACT_INFO =
      new LinkedHashMap<>(16);

  static {
    SUPPLIER_TEMPLATE_ABROAD_CONTACT_INFO.put(NAME, NAME_STR);
    SUPPLIER_TEMPLATE_ABROAD_CONTACT_INFO.put(SEX, SEX_STR);
    SUPPLIER_TEMPLATE_ABROAD_CONTACT_INFO.put(AREA, AREA_STR);
    SUPPLIER_TEMPLATE_ABROAD_CONTACT_INFO.put(PHONE, PHONE_STR);
    SUPPLIER_TEMPLATE_ABROAD_CONTACT_INFO.put(MAIL, MAIL_STR);
    SUPPLIER_TEMPLATE_ABROAD_CONTACT_INFO.put(DUTY, DUTY_STR);
  }
  /** 个人供应商模板 */
  public static final Map<String, Map<String, String>> SUPPLIER_TEMP_LATE_PERSON =
      new LinkedHashMap<>(16);
  /** 个人供应商模板基本信息 */
  public static final Map<String, String> SUPPLIER_TEMPLATE_PERSON_BASE_INFO =
      new LinkedHashMap<>(16);

  static {
    SUPPLIER_TEMPLATE_PERSON_BASE_INFO.put(NAME, NAME_STR);
    SUPPLIER_TEMPLATE_PERSON_BASE_INFO.put(PHONE, PHONE_STR);
  }
  /** 个人供应商模板财务信息 */
  public static final Map<String, String> SUPPLIER_TEMPLATE_PERSON_FINANCE_INFO =
      new LinkedHashMap<>(16);

  static {
    SUPPLIER_TEMPLATE_PERSON_FINANCE_INFO.put(BANK_NAME, BANK_NAME_STR);
    SUPPLIER_TEMPLATE_PERSON_FINANCE_INFO.put(BANK_NUM, BANK_NUM_STR);
  }

  /** 供应商模板信息内容类型 */
  public static final Map<String, String> SUPPLIER_TEMPLATE_INFO_TYPE = new LinkedHashMap<>();
  /** 供应商模板基本信息 CODE */
  public static final String SUPPLIER_TEMPLATE_BASE_INFO_CODE = "0";
  /** 供应商模板基本信息 */
  public static final String SUPPLIER_TEMPLATE_BASE_INFO_STR = "基本信息";
  /** 供应商模板财务信息 CODE */
  public static final String SUPPLIER_TEMPLATE_FINANCE_INFO_CODE = "1";
  /** 供应商模板财务信息 */
  public static final String SUPPLIER_TEMPLATE_FINANCE_INFO_STR = "财务信息";
  /** 供应商模板联系人 CODE */
  public static final String SUPPLIER_TEMPLATE_CONTACT_INFO_CODE = "2";
  /** 供应商模板联系人 */
  public static final String SUPPLIER_TEMPLATE_CONTACT_STR = "联系人";

  /** 需关联品牌 CODE */
  public static final String SUPPLIER_TEMPLATE_NEED_BRAND = "5";
  /** 需关联品牌 */
  public static final String SUPPLIER_TEMPLATE_NEED_BRAND_STR = "需关联品牌";
  /** 需关联类目 CODE */
  public static final String SUPPLIER_TEMPLATE_NEED_CATEGORY = "6";
  /** 需关联类目 */
  public static final String SUPPLIER_TEMPLATE_NEED_CATEGORY_STR = "需关联类目";

  /** 自营等级推荐规则 CODE */
  public static final String SUPPLIER_TEMPLATE_RECOMMENDED_LEVEL = "7";

  /** 自营等级推荐规则 */
  public static final String SUPPLIER_RECOMMENDED_LEVEL_STR = "自营等级推荐规则";

  /** 需上传供应商评估表 CODE */
  public static final String SUPPLIER_TEMPLATE_NEED_UPLOAD_EVALUATION_TABLE = "8";
  /** 需上传供应商评估表 */
  public static final String SUPPLIER_TEMPLATE_NEED_UPLOAD_EVALUATION_TABLE_STR = "需上传供应商评估表";

  static {
    SUPPLIER_LEVEL_LINK_BRAND.put(
        String.format("%s-%s-%s", "brand", SupplierLevelEnum.POTENTIAL.getField(),
            SupplierLevelEnum.POTENTIAL.getCode()), SupplierLevelEnum.POTENTIAL.getAbbr());
    SUPPLIER_LEVEL_LINK_BRAND.put(
        String.format("%s-%s-%s", "brand", SupplierLevelEnum.SPORADIC.getField(),
            SupplierLevelEnum.SPORADIC.getCode()), SupplierLevelEnum.SPORADIC.getAbbr());
    SUPPLIER_LEVEL_LINK_BRAND.put(
        String.format("%s-%s-%s", "brand", SupplierLevelEnum.GENERAL.getField(),
            SupplierLevelEnum.GENERAL.getCode()), SupplierLevelEnum.GENERAL.getAbbr());
    SUPPLIER_LEVEL_LINK_BRAND.put(
        String.format("%s-%s-%s", "brand", SupplierLevelEnum.HIGH_QUALITY.getField(),
            SupplierLevelEnum.HIGH_QUALITY.getCode()), SupplierLevelEnum.HIGH_QUALITY.getAbbr());
    SUPPLIER_LEVEL_LINK_BRAND.put(
        String.format("%s-%s-%s", "brand", SupplierLevelEnum.STRATEGIC.getField(),
            SupplierLevelEnum.STRATEGIC.getCode()), SupplierLevelEnum.STRATEGIC.getAbbr());
  }

  static {
    SUPPLIER_LEVEL_LINK_CATEGORY.put(
        String.format("%s-%s-%s", "category", SupplierLevelEnum.POTENTIAL.getField(),
            SupplierLevelEnum.POTENTIAL.getCode()), SupplierLevelEnum.POTENTIAL.getAbbr());
    SUPPLIER_LEVEL_LINK_CATEGORY.put(
        String.format("%s-%s-%s", "category", SupplierLevelEnum.SPORADIC.getField(),
            SupplierLevelEnum.SPORADIC.getCode()), SupplierLevelEnum.SPORADIC.getAbbr());
    SUPPLIER_LEVEL_LINK_CATEGORY.put(
        String.format("%s-%s-%s", "category", SupplierLevelEnum.GENERAL.getField(),
            SupplierLevelEnum.GENERAL.getCode()), SupplierLevelEnum.GENERAL.getAbbr());
    SUPPLIER_LEVEL_LINK_CATEGORY.put(
        String.format("%s-%s-%s", "category", SupplierLevelEnum.HIGH_QUALITY.getField(),
            SupplierLevelEnum.HIGH_QUALITY.getCode()), SupplierLevelEnum.HIGH_QUALITY.getAbbr());
    SUPPLIER_LEVEL_LINK_CATEGORY.put(
        String.format("%s-%s-%s", "category", SupplierLevelEnum.STRATEGIC.getField(),
            SupplierLevelEnum.STRATEGIC.getCode()), SupplierLevelEnum.STRATEGIC.getAbbr());
  }
  static {
    SUPPLIER_LEVEL_LINK_EVALUATION_TABLE.put(
        String.format("%s-%s-%s", "evaluation", SupplierLevelEnum.POTENTIAL.getField(),
            SupplierLevelEnum.POTENTIAL.getCode()), SupplierLevelEnum.POTENTIAL.getAbbr());
    SUPPLIER_LEVEL_LINK_EVALUATION_TABLE.put(
        String.format("%s-%s-%s", "evaluation", SupplierLevelEnum.SPORADIC.getField(),
            SupplierLevelEnum.SPORADIC.getCode()), SupplierLevelEnum.SPORADIC.getAbbr());
    SUPPLIER_LEVEL_LINK_EVALUATION_TABLE.put(
        String.format("%s-%s-%s", "evaluation", SupplierLevelEnum.GENERAL.getField(),
            SupplierLevelEnum.GENERAL.getCode()), SupplierLevelEnum.GENERAL.getAbbr());
    SUPPLIER_LEVEL_LINK_EVALUATION_TABLE.put(
        String.format("%s-%s-%s", "evaluation", SupplierLevelEnum.HIGH_QUALITY.getField(),
            SupplierLevelEnum.HIGH_QUALITY.getCode()), SupplierLevelEnum.HIGH_QUALITY.getAbbr());
    SUPPLIER_LEVEL_LINK_EVALUATION_TABLE.put(
        String.format("%s-%s-%s", "evaluation", SupplierLevelEnum.STRATEGIC.getField(),
            SupplierLevelEnum.STRATEGIC.getCode()), SupplierLevelEnum.STRATEGIC.getAbbr());
  }

  static {
    SUPPLIER_RECOMMENDED_LEVEL.put(SupplierRecommendedLevelConfig.KEY, "自营等级推荐规则配置");
  }


  /** 国内供应商模板不可编辑的信息 */
  public static final List<String> SUPPLIER_TEMPLATE_CHINA_CANNOT_EDIT =
      CollUtil.toList(ENTERPRISE_NAME, PROVINCE, CITY, INDUSTRY,ENTERPRISE_LEVEL);
  /** 国外供应商模板不可编辑的信息 */
  public static final List<String> SUPPLIER_TEMPLATE_ABROAD_CANNOT_EDIT =
      CollUtil.toList(ENTERPRISE_NAME, COUNTRY,ENTERPRISE_LEVEL);
  /** 个人供应商模板不可编辑的信息 */
  public static final List<String> SUPPLIER_TEMPLATE_PERSON_CANNOT_EDIT =
      CollUtil.toList(NAME, PHONE);

  static {
    SUPPLIER_TEMPLATE_INFO_TYPE.put(
        SUPPLIER_TEMPLATE_BASE_INFO_CODE, SUPPLIER_TEMPLATE_BASE_INFO_STR);
    SUPPLIER_TEMPLATE_INFO_TYPE.put(
        SUPPLIER_TEMPLATE_FINANCE_INFO_CODE, SUPPLIER_TEMPLATE_FINANCE_INFO_STR);
    SUPPLIER_TEMPLATE_INFO_TYPE.put(
        SUPPLIER_TEMPLATE_CONTACT_INFO_CODE, SUPPLIER_TEMPLATE_CONTACT_STR);
    SUPPLIER_TEMPLATE_INFO_TYPE.put(
        SUPPLIER_TEMPLATE_NEED_BRAND, SUPPLIER_TEMPLATE_NEED_BRAND_STR);
    SUPPLIER_TEMPLATE_INFO_TYPE.put(
        SUPPLIER_TEMPLATE_NEED_CATEGORY, SUPPLIER_TEMPLATE_NEED_CATEGORY_STR);
    SUPPLIER_TEMPLATE_INFO_TYPE.put(
        SUPPLIER_TEMPLATE_RECOMMENDED_LEVEL, SUPPLIER_RECOMMENDED_LEVEL_STR);
    SUPPLIER_TEMPLATE_INFO_TYPE.put(
        SUPPLIER_TEMPLATE_NEED_UPLOAD_EVALUATION_TABLE, SUPPLIER_TEMPLATE_NEED_UPLOAD_EVALUATION_TABLE_STR);
  }

  static {
    SUPPLIER_TEMPLATE_CHINA.put(
        SUPPLIER_TEMPLATE_BASE_INFO_CODE, SUPPLIER_TEMPLATE_CHINA_BASE_INFO);
    SUPPLIER_TEMPLATE_CHINA.put(
        SUPPLIER_TEMPLATE_FINANCE_INFO_CODE, SUPPLIER_TEMPLATE_CHINA_FINANCE_INFO);
    SUPPLIER_TEMPLATE_CHINA.put(
        SUPPLIER_TEMPLATE_CONTACT_INFO_CODE, SUPPLIER_TEMPLATE_CHINA_CONTACT_INFO);
    SUPPLIER_TEMPLATE_CHINA.put(SUPPLIER_TEMPLATE_NEED_BRAND, SUPPLIER_LEVEL_LINK_BRAND);
    SUPPLIER_TEMPLATE_CHINA.put(SUPPLIER_TEMPLATE_NEED_CATEGORY, SUPPLIER_LEVEL_LINK_CATEGORY);
    SUPPLIER_TEMPLATE_CHINA.put(SUPPLIER_TEMPLATE_RECOMMENDED_LEVEL, SUPPLIER_RECOMMENDED_LEVEL);
    SUPPLIER_TEMPLATE_CHINA.put(SUPPLIER_TEMPLATE_NEED_UPLOAD_EVALUATION_TABLE, SUPPLIER_LEVEL_LINK_EVALUATION_TABLE);
  }

  static {
    SUPPLIER_TEMPLATE_ABROAD.put(
        SUPPLIER_TEMPLATE_BASE_INFO_CODE, SUPPLIER_TEMPLATE_ABROAD_BASE_INFO);
    SUPPLIER_TEMPLATE_ABROAD.put(
        SUPPLIER_TEMPLATE_FINANCE_INFO_CODE, SUPPLIER_TEMPLATE_ABROAD_FINANCE_INFO);
    SUPPLIER_TEMPLATE_ABROAD.put(
        SUPPLIER_TEMPLATE_CONTACT_INFO_CODE, SUPPLIER_TEMPLATE_ABROAD_CONTACT_INFO);
  }

  static {
    SUPPLIER_TEMP_LATE_PERSON.put(
        SUPPLIER_TEMPLATE_BASE_INFO_CODE, SUPPLIER_TEMPLATE_PERSON_BASE_INFO);
    SUPPLIER_TEMP_LATE_PERSON.put(
        SUPPLIER_TEMPLATE_FINANCE_INFO_CODE, SUPPLIER_TEMPLATE_PERSON_FINANCE_INFO);
  }

}
