package com.xhgj.srm.common.utils;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.xhiot.boot.core.common.exception.CheckException;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

/**
 * Created by Geng Shy on 2023/11/17
 */
@Slf4j
public class RequestUtil {
  //咸亨国际各系统统一ResultBean的data节点。
  private static String data = "data";

  /**
   * 提取咸亨国际各系统统一返回对象的data节点，针对于数组格式的数据。
   *
   * @param jsonObject 咸亨国际各系统统一返回对象
   * @param t data节点转换的类
   * @param <T> data节点转换的类
   * @return 转换之后的对象
   */
  public static <T> List<T> extractDataArray(JSONObject jsonObject, Class<T> t) {
    if (jsonObject == null) {
      throw new CheckException("远程调用请求无响应，请稍后重试！");
    }
    Assert.notNull(t);
    if (!jsonObject.containsKey(data)) {
      throwException(jsonObject);
    }
    JSONArray jsonArray = jsonObject.getJSONArray(data);
    List<T> result = null;
    try {
      result = JSON.parseArray(jsonArray.toJSONString(), t);
    } catch (Exception e) {
      log.error(ExceptionUtil.stacktraceToString(e, -1));
      throwException(jsonObject);
    }
    return result;
  }

  /**
   * 提取咸亨国际各系统统一返回对象的data节点。
   *
   * @param jsonObject 咸亨国际各系统统一返回对象
   * @param <T> data节点转换的类
   * @return 转换之后的对象
   */
  public static <T> T extractData(JSONObject jsonObject, TypeReference<T> typeReference) {
    if (jsonObject == null) {
      throw new CheckException("远程调用请求无响应，请稍后重试！");
    }
    Assert.notNull(typeReference);
    if (!jsonObject.containsKey(data)) {
      throwException(jsonObject);
    }
    String jsonString = jsonObject.getString(data);
    T result = null;
    try {
      result = JSON.parseObject(jsonString, typeReference);
    } catch (Exception e) {
      log.error(ExceptionUtil.stacktraceToString(e, -1));
      throwException(jsonObject);
    }
    return result;
  }


  private static void throwException(JSONObject jsonObject) {
    log.error("ResultBean提取data失败，json：" + jsonObject);
    throw new CheckException("ResultBean提取data失败");
  }
}
