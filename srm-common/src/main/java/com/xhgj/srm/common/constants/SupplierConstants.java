package com.xhgj.srm.common.constants;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> @date 2023/7/6
 */
public class SupplierConstants {

  /**
   * 供应商税率，百分比
   */
  public final static String TAX_RATE_9 = "9";
  public final static String TAX_RATE_17 = "17";
  public final static String TAX_RATE_13 = "13";
  public final static String TAX_RATE_0 = "0";
  public final static String TAX_RATE_11 = "11";
  public final static String TAX_RATE_6 = "6";
  public final static String TAX_RATE_3 = "3";
  public final static String TAX_RATE_16 = "16";
  public final static String TAX_RATE_1 = "1";
  public final static String TAX_RATE_12 = "12";
  public final static String TAX_RATE_10 = "10";
  /**
   * 供应商税率映射
   */
  public final static Map<String, String> TAX_RATE_MAP = new HashMap<String, String>(){{
    put("0004", TAX_RATE_9);
    put("SL01_SYS", TAX_RATE_17);
    put("SL02_SYS", TAX_RATE_13);
    put("SL04_SYS", TAX_RATE_0);
    put("SL05_SYS", TAX_RATE_11);
    put("SL06_SYS", TAX_RATE_6);
    put("SL07_SYS", TAX_RATE_3);
    put("SL08_SYS", TAX_RATE_16);
    put("SL09_SYS", TAX_RATE_1);
    put("SL45_SYS", TAX_RATE_12);
    put("SL62_SYS", TAX_RATE_10);
  }};
}
