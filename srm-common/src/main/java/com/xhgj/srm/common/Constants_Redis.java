package com.xhgj.srm.common;

/** <AUTHOR> @ClassName Constants_Redis */
public class Constants_Redis {
  /** 缓存用户权限下用户id结果集 */
  public static final String CACHE_GET_USER_ID_LIST_KEY = "get:user:id:list:key";
  /** 缓存用户权限下用户name结果集 */
  public static final String CACHE_GET_USER_NAME_LIST_KEY = "get:user:name:list:key";
  /** 缓存登录用户次数 */
  public static final String CACHE_GET_USER_LOGIN_NAME_KEY = "srm_fail_count";
  /**
   * 记录srm定时器<br>
   * 定时器更新 组织内供应商 合作性质<br>
   * 上次更新时间<br>
   * 组织维度
   */
  public static final String CACHE_LAST_UPDATE_PARTNERSHIP_TYPE = "srm:last_update_time:partnership_type:{}";

  /**
   * 记录srm定时器<br>
   * 定时器更新 组织内供应商 金额统计与价格库条数<br>
   * 上次更新时间<br>
   * 组织维度
   */
  public static final String CACHE_LAST_UPDATE_AMOUNT_AND_COUNT= "srm:last_update_time:amount_count:{}";

  /**
   * 记录srm定时器<br>
   * 定时器更新 组织内供应商 推荐自营等级<br>
   * 上次更新时间<br>
   * 组织维度
   */
  public static final String CACHE_LAST_UPDATE_RECOMMENDED_LEVEL = "srm:last_update_time:recommended_level:{}";


  /**
   * 库存列表SAP调srm同步时间
   * 最近同步时间<br>
   */
  public static final String CACHE_LAST_UPDATE_INVENTORY_BY_SAP = "srm:last_update_time:inventory_sap";

}
