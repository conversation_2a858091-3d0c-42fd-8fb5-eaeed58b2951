package com.xhgj.srm.common.domain;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021/1/5 10:26
 */
@Data
public class ExcelTitleAttr {
  /** 标题单元格宽 */
  private int cellWidth;
  /** 该列是否需要导入 */
  private boolean needImport = true;

  /** 背景颜色 */
  private short backgroundColor;

  public ExcelTitleAttr(int cellWidth) {
    this.cellWidth = cellWidth;
  }

  public ExcelTitleAttr(int cellWidth, boolean needImport) {
    this.cellWidth = cellWidth;
    this.needImport = needImport;
  }

  public ExcelTitleAttr(int cellWidth, short backgroundColor) {
    this.cellWidth = cellWidth;
    this.backgroundColor = backgroundColor;
  }
}
