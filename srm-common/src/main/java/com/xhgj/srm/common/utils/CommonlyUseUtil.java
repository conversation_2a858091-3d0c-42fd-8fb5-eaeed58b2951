package com.xhgj.srm.common.utils;

import cn.hutool.core.util.StrUtil;
import com.xhiot.boot.core.common.util.DateUtils;
import com.xhiot.boot.core.common.util.StringUtils;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map.Entry;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class CommonlyUseUtil {

  public static float EXPORT_EXCEL_SCHEDULE_PERCENTAGE = 0.02f;

  /**
   * @param str
   * @return
   * @Title: isContainChinese
   * @Description:判断字符是否为中文
   * <AUTHOR>
   * @date 2019年3月19日下午1:40:18
   */
  public static boolean isContainChinese(String str) {
    Pattern p = Pattern.compile("([\u4E00-\u9FA5]|[\uFE30-\uFFA0])+");
    Matcher m = p.matcher(str);
    if (m.find()) {
      return true;
    }
    return false;
  }

  /**
   * @param str
   * @return
   * @Title: isNumeric
   * @Description:判断价格是否为小数和正整数
   * <AUTHOR>
   * @date 2019年4月9日下午7:40:04
   */
  public static Boolean isNumeric(String str) {
    char[] strArr = str.toCharArray();
    int count = 0;
    int dobuleNum = 0;
    for (int i = 0; i < strArr.length; i++) {
      if (strArr[i] == '.') {
        dobuleNum = dobuleNum + 1;
        continue;
      }
      ;
      if (Character.isDigit(strArr[i]) == false) {
        count++;
      }
    }
    if (count > 0 || dobuleNum > 1) {
      return false;
    } else {
      return true;
    }
  }

  /**
   * @param str
   * @return
   * @Title: BigDecimalValue
   * @Description:去掉多余的零值
   * <AUTHOR>
   * @date 2019年5月16日下午4:04:00
   */
  public static String BigDecimalValue(String str) {
    str = new BigDecimal(str).toPlainString();
    if (null != str && str.indexOf(".") > 0) {
      str = str.replaceAll("0+?$", "").replaceAll("[.]$", "");
    }
    return str.equals("0") ? "0" : str;  //如果去除多余零之后结果为0，那么返回0.00保持和没修改之前一样
  }

  /**
   * @param mobile
   * @return
   */
  public final static boolean isMobileNO(String mobile) {
    Pattern pp = Pattern
        .compile("^(0|86|17951)?(13[0-9]|15[012356789]|17[0-9]|18[0-9]|14[57])[0-9]{8}$");
    Matcher m = pp.matcher(mobile);
    return m.matches();
  }

  /***
   * 比较两个list是否相等
   * @param list1
   * @param list2
   * <AUTHOR>
   * @date 2021/8/17 18:36
   */
  public static boolean equalList(List list1, List list2) {
    if (list1 != null && list2 != null) {
			if (list1.size() != list2.size()) {
				return false;
			}
      for (Object object : list1) {
				if (!list2.contains(object)) {
					return false;
				}
      }
    }
    return true;
  }

  /***
   *  保留两位小数，少于两位加零
   * @param obj
   * <AUTHOR>
   * @date 2022/3/16 16:10
   */
  public static String formatToNumber(BigDecimal obj) {
    DecimalFormat df = new DecimalFormat("#.00");
    if (obj.compareTo(BigDecimal.ZERO) == 0) {
      return "0.00";
    } else if (obj.compareTo(BigDecimal.ZERO) > 0 && obj.compareTo(new BigDecimal(1)) < 0) {
      return "0" + df.format(obj).toString();
    } else if (obj.compareTo(BigDecimal.ZERO) < 0 && obj.compareTo(new BigDecimal(-1)) > 0) {
      df = new DecimalFormat("0.00");
      return df.format(obj);
    } else {
      return df.format(obj).toString();
    }
  }

  /***
   * 存在/的项目编号替换成-
   * @param projectNumber 入参：ce/01
   * <AUTHOR>
   * @date 2022/5/9 16:02
   * @return 返参：ce_01
   */
  public static String containsFileSeparatorProjectReReplace(String projectNumber) {
    if (!StringUtils.isNullOrEmpty(projectNumber) && projectNumber.contains("/")) {
      projectNumber = StrUtil.replace(projectNumber, "/", "_");
    }
    return projectNumber;
  }


  /***
   * 获取数据的乘以百分比得到的数值
   * @param dataLength 数据总长度
   * @param percentage  百分比；值为double数类型 例如需要获取5% 传入的值为0.05
   * <AUTHOR>
   * @date 2022/5/10 21:11
   * @return 总长度的百分比返回值
   */
  public static int getPercentageValue(int dataLength, double percentage) {
    double d1 = dataLength * percentage;
    return (int) Math.ceil(d1);
  }

  /***
   *  获取导出任务指定百分比数值
   * @param dataLength 数据总长度
   * <AUTHOR>
   * @date 2022/5/10 21:18
   * @return 总长度的5%的返回值值
   */
  public static int getBatchExportPercentageValue(int dataLength) {
    return getPercentageValue(dataLength, EXPORT_EXCEL_SCHEDULE_PERCENTAGE);
  }

  /***
   * 将日期格式转化为当天日期最大时分秒
   * @param date 格式：2022-05-11
   * <AUTHOR>
   * @date 2022/5/11 19:40
   * @return 格式 ：2022-05-11 23:59:59
   */
  public static String parseDateToMaxDateTime(String date) {
    if (!StringUtils.isNullOrEmpty(date)) {
      return date + " 23:59:59";
    }
    return date;
  }


  /**
   * 判断当前时间是否在时间段内 begin 开始日期时间字符串  String begin="2022-09-12 09:00:00"; end  结束日期时间字符串    String
   * end="2022-09-12 12:00:00";
   */
  public static boolean compareDateTime(String begin, String end) {
    boolean result = false;
    long sysTime = System.currentTimeMillis();
    long startTime = DateUtils.parseNormalDateTimeToTimeStamp(begin);
    long closeTime = DateUtils.parseNormalDateTimeToTimeStamp(end);
    if (sysTime <= closeTime && sysTime >= startTime) {
      return true;
    }
    return result;
  }


  /**
   * 判断当前时间是否在时间段内 begin 开始时间字符串  String begin="09:00:00"; end  结束时间字符串    String end="12:00:00";
   */
  public static boolean compareTime(String begin, String end) {
    boolean result = false;
    //将时间字符串转化成时间
    SimpleDateFormat df = new SimpleDateFormat("HH:mm:ss");
    try {
      //转换成时间格式
      Date beginTime = df.parse(begin);
      Date endTime = df.parse(end);
      //取出当前时间的时分秒编码再解码
      Date date = df.parse(df.format(new Date()));
      //通过日历形式开始比较
      Calendar b = Calendar.getInstance();
      b.setTime(beginTime);
      Calendar e = Calendar.getInstance();
      e.setTime(endTime);
      Calendar d = Calendar.getInstance();
      d.setTime(date);
      //当前时间晚于开始时间，早于结束时间则表明在指定的时间段内
      if (d.after(b) && d.before(e)) {
        result = true;
      }
    } catch (ParseException e1) {
      e1.printStackTrace();
    }
    return result;
  }


  /***
   *  是否为不合法的物流单号
   * @param expressNo  物流单号 必填
   * <AUTHOR>
   * @date 2022/6/28 11:12
   */
  public static boolean notLegalLogisticsNo(String expressNo) {
    if (StringUtils.isNullOrEmpty(expressNo)) {
      return true;
    }
    if (isContainChinese(expressNo) || expressNo.contains("/")) {
      return true;
    }
    return false;
  }

  /***
   * 获取订单特定处理的年月日时分秒 返回格式：07-01 09:07:37 2022
   * @param time 时间戳
   * <AUTHOR>
   * @date 2022/7/1 9:08
   */
  public static String getOrderTimeStr(long time) {
    String timeStr = "";
    if (time > 0) {
      timeStr = DateUtils.formatTimeStampToNormalDateTime(time);
      if (timeStr.length() > 10) {
        String monthDay = timeStr.substring(5, 19);
        String year = timeStr.substring(0, 4);
        timeStr = monthDay + " " + year;
      }
    }
    return timeStr;
  }


  /***
   *  获取list中重复出现数据的集合
   * @param list 例如：入参：{1,2,3,4,4,3}  返参：{3,4}
   * <AUTHOR>
   * @date 2022/7/11 13:08
   * @return
   */
  public static List<String> getListRepetitionData(List<String> list) {
    return list.isEmpty() ? new ArrayList<>()
        : list.stream().collect(Collectors.groupingBy(str -> str, Collectors.counting()))
            .entrySet().stream().filter(entry -> entry.getValue() > 1).map(Entry::getKey)
            .collect(Collectors.toList());
  }


  public static String buildBrandName(String brandNameCn, String brandNameEn) {
    String brandNameStr;
    brandNameCn = StrUtil.emptyIfNull(brandNameCn);
    brandNameEn = StrUtil.emptyIfNull(brandNameEn);
    if (!StringUtils.isNullOrEmpty(brandNameCn) && !StringUtils.isNullOrEmpty(brandNameEn)) {
      brandNameStr = brandNameCn + "/" + brandNameEn;
    } else if (!StringUtils.isNullOrEmpty(brandNameCn)) {
      brandNameStr = brandNameCn;
    } else if (!StringUtils.isNullOrEmpty(brandNameEn)) {
      brandNameStr = brandNameEn;
    } else {
      brandNameStr = null;
    }
    return StrUtil.emptyIfNull(brandNameStr);
  }

}

