package com.xhgj.srm.common.utils;

import com.xhgj.srm.common.utils.runner.JedisUtil;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * 付款单退款单号生成规则：1+年月日+三位流水号
 */
public class PaymentRefundNumUtil {
  private int seq = 0;
  private static final String PAYMENT_REFUND_NO_10SRM_COUNTER = "payment_refund_no_10srm_counter";

  private static final JedisUtil jedisUtil = JedisUtil.getInstance();

  private static final String PREFIX = "1";

  private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyMMdd");

  private static class RefundNumContainer {
    private static PaymentRefundNumUtil instance = new PaymentRefundNumUtil();
  }

  public static PaymentRefundNumUtil getInstance() {
    return RefundNumContainer.instance;
  }

  public synchronized String getSeq() {
    LocalDate currentDate = LocalDate.now();
    String datetime = currentDate.format(DATE_FORMATTER);
    if (jedisUtil.exists(PAYMENT_REFUND_NO_10SRM_COUNTER)) {
      String tempStr = jedisUtil.get(PAYMENT_REFUND_NO_10SRM_COUNTER);
      String[] numStr = tempStr.split(",");
      if (!datetime.equals(numStr[0])) {
        // 重新从1开始
        this.seq = 1;
      } else {
        this.seq = Integer.parseInt(numStr[1]) + 1;
      }
    } else {
      if (jedisUtil.checkConnection()) {
        this.seq = 1;
      } else {
        // redis服务异常
        return "0";
      }
    }
    jedisUtil.set(PAYMENT_REFUND_NO_10SRM_COUNTER, datetime + "," + this.seq);
    String formattedSequence = String.format("%03d", this.seq);
    return PREFIX + datetime + formattedSequence;
  }
}
