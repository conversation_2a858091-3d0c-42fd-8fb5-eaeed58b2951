package com.xhgj.srm.common.domain.dingding;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/9/1 10:02
 */
@Data
public class SendCarTalkRobotParam {


 @ApiModelProperty("钉钉机器人主题（不支持 markdown）")
 @NotBlank(message = "主题必传")
 private String title;

 @ApiModelProperty("内容（支持 markdown格式）")
 @NotBlank(message = "发送内容不能为空")
 private String text;

 @ApiModelProperty("发送人的手机号")
 @NotEmpty(message = "发送人的手机号")
 @NotNull(message = "发送人手机号必传")
 private List<String> mobiles;

 @ApiModelProperty("链接和链接名称，最大支持6个")
 private List<ActionTitleAndUrl> titleAndUrls;

}
