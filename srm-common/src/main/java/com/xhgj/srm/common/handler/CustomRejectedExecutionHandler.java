package com.xhgj.srm.common.handler;

import com.xhiot.boot.core.common.exception.CheckException;
import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * created by <PERSON><PERSON> Shy on 2024/1/16
 */
public class CustomRejectedExecutionHandler implements RejectedExecutionHandler {

  @Override
  public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
    throw new CheckException("服务器繁忙，请稍候再试");
  }


}
