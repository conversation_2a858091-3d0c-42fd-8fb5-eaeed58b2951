package com.xhgj.srm.common.constants;

import cn.hutool.core.collection.CollUtil;
import com.xhgj.srm.common.enums.PaymentApplyTypeEnums;
import java.util.List;

public class ConstantsFinance {
  private ConstantsFinance() {}
  public static final String PAYMENT_IN_ADVANCE = "预付";
  public static final String ACCOUNTS_PAYABLE = "应付";

  /**
   * 验凭证行项目不匹配的类型
   */
  public static final List<String> CHECK_VOUCHER_LINE_ITEMS = CollUtil.toList(
      PaymentApplyTypeEnums.ADVANCE.getKey(),
      PaymentApplyTypeEnums.DRAW.getKey()
  );
}
