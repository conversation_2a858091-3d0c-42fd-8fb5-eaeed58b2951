package com.xhgj.srm.common.constants;

public class Constants_Sap {

  /**
   * sap默认属性值，当属性值为“@”时，代表此字段的值不会被更新。
   */
  public static final String DEFAULT_ATTRIBUTE_VALUES = "@";

  /**
   * 表示确认、肯定的标识
   */
  public static final String CONFIRM_IDENTIFICATION = "X";

  /**
   * sap入参项目
   */
  public static final String ITEM = "ITEM";

  /**
   * sap入参项目1
   */
  public static final String ITEM_ONE = "ITEM1";
  /**
   * sap入参项目2
   */
  public static final String ITEM_TWO = "ITEM2";

  /** SAP 成功 type */
  public static final String SUCCESS_TYPE = "S";
  /** SAP 失败 type */
  public static final String ERROR_TYPE = "E";

  /**
   * 未查询到数据
   */
  public static final String EMPTY_QUERY_RESULT = "未查询";
  /**
   * 1-新增退换货订单
   */
  public static final String RETURN_ORDER_TYPE_ADD = "1";
  /**
   * 2-参考原单退货
   */
  public static final String RETURN_ORDER_TYPE_ORIGINAL = "2";
  /**
   * Y-是
   */
  public static final String YES = "Y";
  /**
   * N-否
   */
  public static final String NO = "N";

  /**
   * 030回传类型-1入库单
   */
  public static final String MM_030_RETURN_TYPE_1 = "1";

  /**
   * 030回传类型-2出库单
   */
  public static final String MM_030_RETURN_TYPE_2 = "2";

}
