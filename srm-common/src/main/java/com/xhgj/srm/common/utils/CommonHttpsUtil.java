package com.xhgj.srm.common.utils;


import cn.hutool.core.exceptions.ExceptionUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.DefaultHttpClient;
import org.springframework.stereotype.Component;
import sun.net.www.protocol.http.HttpURLConnection;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.ConnectException;
import java.net.URL;

@Slf4j
@Component
public class CommonHttpsUtil {


    private static final String HEADER_FORM = "application/x-www-form-urlencoded; charset=UTF-8";
    private static final String HEADER_JSON = "application/json; charset=UTF-8";

    /**
     * 发送http请求
     *
     * @param requestUrl    请求地址
     * @param requestMethod 请求方式（GET、POST）
     * @param outputStr     提交的数据
     * @return JSONObject(通过JSONObject.get ( key)的方式获取json对象的属性值)
     */
    public String httpRequest(String requestUrl, String requestMethod, String outputStr) {
        return httpRequest(requestUrl, requestMethod, outputStr, HEADER_FORM);
    }

    /**
     * 发送http请求
     *
     * @param requestUrl    请求地址
     * @param requestMethod 请求方式（GET、POST）
     * @param outputStr     提交的数据
     * @return JSONObject(通过JSONObject.get ( key)的方式获取json对象的属性值)
     */
    public String httpRequestJson(String requestUrl, String requestMethod, String outputStr) {
        return httpRequest(requestUrl, requestMethod, outputStr, HEADER_JSON);
    }


    /**
     * 发送http请求
     *
     * @param requestUrl    请求地址
     * @param requestMethod 请求方式（GET、POST）
     * @param outputStr     提交的数据
     * @return JSONObject(通过JSONObject.get ( key)的方式获取json对象的属性值)
     */
    public String httpRequest(String requestUrl, String requestMethod, String outputStr, String header) {
        URL url;
        try {
            url = new URL(requestUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setDoOutput(true); // 设置该连接是可以输出的
            connection.setRequestMethod(requestMethod); // 设置请求方式
            connection.setRequestProperty("Content-Type", header);
            // 当outputStr不为null时向输出流写数据
            if (null != outputStr) {
                OutputStream outputStream = connection.getOutputStream();
                // 注意编码格式
                outputStream.write(outputStr.getBytes("UTF-8"));
                outputStream.close();
            }
            try(BufferedReader br = new BufferedReader(new InputStreamReader(connection.getInputStream(), "utf-8"));) {
              String line = null;
              StringBuilder result = new StringBuilder();
              while ((line = br.readLine()) != null) { // 读取数据
                result.append(line + "\n");
              }
              return result.toString();
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }



    /**
     * 发送https请求
     *
     * @param requestUrl    请求地址
     * @param requestMethod 请求方式（GET、POST）
     * @param outputStr     提交的数据
     * @return JSONObject(通过JSONObject.get ( key)的方式获取json对象的属性值)
     */
    public JSONObject httpsRequest(String requestUrl, String requestMethod, String outputStr) {
        JSONObject jsonObject = null;
        try {

            // 创建SSLContext对象，并使用我们指定的信任管理器初始化
            TrustManager[] tm = {new MyX509TrustManager()};
            SSLContext sslContext = SSLContext.getInstance("SSL", "SunJSSE");
            sslContext.init(null, tm, new java.security.SecureRandom());
            // 从上述SSLContext对象中得到SSLSocketFactory对象
            SSLSocketFactory ssf = sslContext.getSocketFactory();
            URL url = new URL(requestUrl);
            HttpsURLConnection conn = (HttpsURLConnection) url.openConnection();
            conn.setSSLSocketFactory(ssf);
            conn.setDoOutput(true);
            conn.setDoInput(true);
            conn.setUseCaches(false);
            // 设置请求方式（GET/POST）
            conn.setRequestMethod(requestMethod);
            // 当outputStr不为null时向输出流写数据
            if (null != outputStr) {
                OutputStream outputStream = conn.getOutputStream();
                // 注意编码格式
                outputStream.write(outputStr.getBytes("UTF-8"));
                outputStream.close();
            }
            // 从输入流读取返回内容
          try(InputStream inputStream = conn.getInputStream();
              InputStreamReader inputStreamReader = new InputStreamReader(inputStream, "utf-8");
              BufferedReader bufferedReader = new BufferedReader(inputStreamReader);) {
            String str;
            StringBuilder buffer = new StringBuilder();
            while ((str = bufferedReader.readLine()) != null) {
              buffer.append(str);
            }
            // 释放资源
            conn.disconnect();
            jsonObject = JSONObject.parseObject(buffer.toString());
          }
        } catch (ConnectException ce) {
            log.error(ce.toString());
        } catch (Exception e) {
            log.error("https请求异常：{" + ExceptionUtil.stacktraceToString(e) + "}");
        }
        return jsonObject;
    }


    public String httpHeaderRequest(String requestUrl, String requestMethod, String outputStr, String token) {
        URL url;
        try {
            url = new URL(requestUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setDoOutput(true); // 设置该连接是可以输出的
            connection.setRequestMethod(requestMethod); // 设置请求方式
            connection.setRequestProperty("Content-Type", "application/json; charset=UTF-8");
            connection.setRequestProperty("Authorization", token);
            // 当outputStr不为null时向输出流写数据
            if (null != outputStr) {
                OutputStream outputStream = connection.getOutputStream();
                // 注意编码格式
                outputStream.write(outputStr.getBytes("UTF-8"));
                outputStream.close();
            }
          try (BufferedReader br = new BufferedReader(
              new InputStreamReader(connection.getInputStream(), "utf-8"));) {
            String line = null;
            StringBuilder result = new StringBuilder();
            while ((line = br.readLine()) != null) { // 读取数据
              result.append(line + "\n");
            }
            return result.toString();
          }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

}
