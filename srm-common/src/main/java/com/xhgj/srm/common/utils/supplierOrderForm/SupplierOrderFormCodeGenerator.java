package com.xhgj.srm.common.utils.supplierOrderForm;/**
 * @since 2025/5/14 9:25
 */

import cn.hutool.core.collection.CollUtil;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormType;
import com.xhgj.srm.common.utils.runner.JedisUtil;
import com.xhiot.boot.core.common.exception.CheckException;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 *<AUTHOR>
 *@date 2025/5/14 09:25:33
 *@description 采购订单form单号生成器
 */
@Slf4j
public class SupplierOrderFormCodeGenerator {
  // 单例实例
  public static final SupplierOrderFormCodeGenerator INSTANCE = new SupplierOrderFormCodeGenerator();
  private static final ThreadLocal<Map<SupplierOrderFormType, List<String>>> ORDER_NEW_BATCH_MAP =
      ThreadLocal.withInitial(HashMap::new);
  private static final JedisUtil jedisUtil = JedisUtil.getInstance();
  private static final int RANGE_START = 1;
  private static final int RANGE_END = 9999;
  private static final String REDIS_ORDER_USED_KEY_PREFIX  = "srm_used_supplier_form_codes:";
  private static final String LOCK_PREFIX   = "srm:lock:srm_supplier_form_code:";
  private static final int LOCK_WAIT_TIME = 30; // 等待锁的最长时间（秒）
  private static final int LOCK_LEASE_TIME = 120; // 锁自动释放时间（秒）
  private static final String REDIS_ORDER_COUNTER_KEY_PREFIX = "srm_supplier_form_count:";
  private LocalDate now;
  private SupplierOrderFormCodeGenerator() {

  }

  public static void clear() {
    ORDER_NEW_BATCH_MAP.remove();
  }

  // 获取当前日期（yyyy-MM-dd）
  private String getCurrentDate() {
    now = LocalDate.now();
    return now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
  }

  // 获取当月 Redis 键名
  private String getRedisKeyForUsedOrders(SupplierOrderFormType type) {
    return REDIS_ORDER_USED_KEY_PREFIX + type.getKey() + ":" + getCurrentDate();
  }

  // 获取单号计数器的Redis键名
  private String getRedisKeyForOrderCounter(SupplierOrderFormType type) {
    return REDIS_ORDER_COUNTER_KEY_PREFIX + type.getKey() + ":" + getCurrentDate();
  }

  /**
   * 由于多系统，需要通过redis加锁
   * @return
   */
  @SneakyThrows
  public String generate(RedissonClient redissonClient, SupplierOrderFormType type) {
    RLock lock = redissonClient.getLock(LOCK_PREFIX + type.getKey());
    try {
      boolean locked = lock.tryLock(LOCK_WAIT_TIME, LOCK_LEASE_TIME, TimeUnit.SECONDS);
      if (!locked) {
        throw new RuntimeException("获取锁失败，lockId: " + type.getKey());
      }
      return generate(type);
    } catch (Exception e) {
      throw e;
    } finally {
      unlockAllLocks(Collections.singletonList(lock));
    }
  }

  private void unlockAllLocks(List<RLock> locks) {
    if (CollUtil.isNotEmpty(locks)) {
      locks.forEach(lock -> {
        try {
          if (lock.isHeldByCurrentThread()) {
            lock.unlock();
          }
        } catch (Exception e) {
          // 记录日志，避免解锁异常影响主流程
          log.error("解锁失败", e);
        }
      });
    }
  }

  private String generate(SupplierOrderFormType type) {
    if (type == null) {
      throw new CheckException("单据类型不能为空");
    }
    // 根据单据类型生成不同的前缀
    String prefix = "";
    switch (type) {
      case DELIVER:
        prefix = "WER";
        break;
      case WAREHOUSING:
        prefix = "GR";
        break;
      case RETURN:
        prefix = "TK";
        break;
      case DETAILED:
        throw new CheckException("订单明细不支持生成单号");
      case CANCEL:
        // 暂不支持
        throw new CheckException("取消单不支持生成单号");
    }
    String orderNumber = generateOrderNumber(type);
    if (orderNumber == null) {
      throw new CheckException("生成单号失败");
    }
    String datetime = now.format(DateTimeFormatter.ofPattern("yyMMdd"));
    return prefix + datetime + orderNumber;
  }

  // 生成一个新的订单号
  @SneakyThrows
  private String generateOrderNumber(SupplierOrderFormType type) {
    String redisUsedKey = getRedisKeyForUsedOrders(type);
    String redisCounterKey = getRedisKeyForOrderCounter(type);
    // 获取已使用的单号集合
    Set<String> usedOrderNumbers = jedisUtil.smembers(redisUsedKey);
    usedOrderNumbers = CollUtil.emptyIfNull(usedOrderNumbers);
    String count = jedisUtil.get(redisCounterKey);
    int counter = 0;
    try {
      counter = (count != null) ? Integer.parseInt(count) : 0;
    } catch (NumberFormatException e) {
      log.warn("计数器值非法，重置为0，key={}", redisCounterKey, e);
    }
    if (counter <= RANGE_END) {
      // 如果计数器不存在或小于等于9999，初始化计数器
      // 1. 先在常规范围(1-9999)内查找未使用的号码
      for (int i = RANGE_START; i <= RANGE_END; i++) {
        String orderNumber = String.format("%04d", i);
        if (!usedOrderNumbers.contains(orderNumber)) {
          // 如果该单号没有被使用，加入到已使用集合中
          jedisUtil.sadd(redisUsedKey, orderNumber);
          ORDER_NEW_BATCH_MAP.get()
              .computeIfAbsent(type, k -> new ArrayList<>())
              .add(orderNumber);
          return orderNumber;
        }
      }
    }
    // 2. 如果常规范围内的号码都用完了，使用Redis的INCR原子操作生成新单号
    // 这确保了并发安全性，每次调用都会得到一个唯一的递增值
    long nextValue = jedisUtil.incr(redisCounterKey);
    // 如果是首次超出范围使用，将计数器设置为10000(9999+1)
    if (nextValue <= RANGE_END) {
      nextValue = RANGE_END + 1;
      jedisUtil.set(redisCounterKey, String.valueOf(nextValue));
    }
    String orderNumber = String.valueOf(nextValue);
    ORDER_NEW_BATCH_MAP.get()
        .computeIfAbsent(type, k -> new ArrayList<>())
        .add(orderNumber);
    return orderNumber;
  }

  // 将单号回退到 Redis 列表中
  public void rollbackOrderNumber() {
    Map<SupplierOrderFormType, List<String>> supplierOrderFormTypeListMap = ORDER_NEW_BATCH_MAP.get();
    for (SupplierOrderFormType type : supplierOrderFormTypeListMap.keySet()) {
      String redisKey = getRedisKeyForUsedOrders(type);
      List<String> orderNumbers = supplierOrderFormTypeListMap.get(type);
      for (String orderNumber : orderNumbers) {
        jedisUtil.srem(redisKey, orderNumber);
      }
    }
    // 清空当前线程的单号列表
    ORDER_NEW_BATCH_MAP.remove();
  }

  // 清空昨日的
  public void clearYesterdayOrderNumbers(LocalDate cronTime) {
    // 获取昨天的日期
    LocalDate yesterday = cronTime.minusDays(1);
    String yesterdayDateStr = yesterday.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

    // 构建 Redis 键的前缀
    for (SupplierOrderFormType type : SupplierOrderFormType.values()) {
      String usedKey   = REDIS_ORDER_USED_KEY_PREFIX  + type.getKey() + ":" + yesterdayDateStr;
      String countKey  = REDIS_ORDER_COUNTER_KEY_PREFIX + type.getKey() + ":" + yesterdayDateStr;
      jedisUtil.del(usedKey);
      jedisUtil.del(countKey);
    }
  }
}
