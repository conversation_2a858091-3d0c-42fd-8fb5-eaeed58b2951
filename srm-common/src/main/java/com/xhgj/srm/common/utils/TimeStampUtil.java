package com.xhgj.srm.common.utils;

import cn.hutool.core.util.StrUtil;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAccessor;

public class TimeStampUtil {

  private TimeStampUtil() {
  }

  /**
   * @param timestampInMillis 时间戳
   * @return 格式化后的时间 eg：20240108
   */
  public static String convertTimestampToFormat(Long timestampInMillis) {
    if (timestampInMillis == null) {
      return StrUtil.EMPTY;
    }
    // 将毫秒时间戳转换为Instant对象
    Instant instant = Instant.ofEpochMilli(timestampInMillis);
    // 使用DateTimeFormatter创建所需的格式
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
    // 将Instant转换为ZonedDateTime（为了能用formatter格式化）
    ZonedDateTime zonedDateTime = instant.atZone(ZoneId.systemDefault());
    // 格式化为字符串
    return zonedDateTime.format(formatter);
  }

  /**
   * @param date  日期
   * @param format 格式
   * @return 毫秒级时间戳
   */
  public static long convertYmdToMillis(String date, String format) {
    // 创建日期格式化器
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);

    // 解析日期字符串
    TemporalAccessor parsedDate = formatter.parse(date);

    // 将解析结果转换为LocalDate对象
    LocalDate localDate = LocalDate.from(parsedDate);

    // 将LocalDate转换为默认时区的Instant对象
    Instant instant = localDate.atStartOfDay(ZoneId.systemDefault()).toInstant();

    // 获取该日期对应的毫秒级时间戳
    return instant.toEpochMilli();
  }

  /**
   * 给定一个时间戳和要添加的天数，返回新的时间戳。
   *
   * @param timestamp 输入的时间戳（单位：毫秒）
   * @param days      要添加的天数
   * @return 新的时间戳（单位：毫秒）
   */
  public static long addDaysToTimestamp(long timestamp, int days) {
    Instant instant = Instant.ofEpochMilli(timestamp);
    instant = instant.plus(days, ChronoUnit.DAYS);
    return instant.toEpochMilli();
  }

}
