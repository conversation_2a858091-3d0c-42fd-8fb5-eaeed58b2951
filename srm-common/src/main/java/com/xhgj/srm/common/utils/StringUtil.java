package com.xhgj.srm.common.utils;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import java.math.BigDecimal;

/**
 * Created by <PERSON><PERSON> on 2023/10/26
 */
public class StringUtil {

  private StringUtil() {
  }

  /**
   * @param input 要去除的字符串
   * @return String 去除所有非中文字符的string对象
   */
  public static String removeNonChineseCharacters(String input) {
    if (StrUtil.isBlank(input)) {
      return "";
    }
    StringBuilder output = new StringBuilder();
    for (char c : input.toCharArray()) {
      if (c >= 0x4E00 && c <= 0x9FFF) { // 判断是否为中文字符
        output.append(c);
      }
    }
    return output.toString();
  }

  /**
   * @param str 要处理的字符串
   * @return  返回该字符串第一个是中文的字符下表，如果没有的话返回-1。
   */
  public static int getFirstChineseCharacter(String str) {
    if (StrUtil.isBlank(str)) {
      return -1;
    }
    for (int i = 0; i < str.length(); i++) {
      if (isChinese(str.charAt(i))) {
        return i;
      }
    }
    return -1;
  }

  public static boolean isChinese(char c) {
    return c >= '\u4e00' && c <= '\u9fff'; // Unicode范围对应中文字符
  }


  /**
   * 根据是否需要红票转换金额的表示。
   *
   * @param amount    需要转换的金额
   * @param needRedTicket 是否需要红票的标志
   * @return 转换后的金额字符串
   */
  public static String convertAmountForRedTicket(BigDecimal amount, String needRedTicket) {
    if (amount == null) {
      return StrUtil.EMPTY;
    }
    boolean isRedTicketApplicable = StrUtil.equals(needRedTicket, Constants.NEED_RED_TICKET) && NumberUtil.isLess(amount, BigDecimal.ZERO);
    return isRedTicketApplicable ?  amount.abs().toPlainString() : amount.toPlainString();
  }
}
