package com.xhgj.srm.common.constants;

/**
 * Created by <PERSON><PERSON> on 2023/11/22
 */
public class Constants_LockName {
  private Constants_LockName(){}

  /**
   * 物料临时编码
   */
  public final static String TEMP_PRODUCT_CODE = "tempProductCode";
  /**
   * 提交物料
   */
  public final static String SUBMIT_PRODUCT = "submitProduct";
  /**
   * 供应商订单发货
   */
  public final static String SUPPLIER_ORDER_DELIVERY = "supplierOrderDelivery";

  /**
   * 落地商订单发货
   */
  public final static String ORDER_DELIVERY = "orderDelivery";

  /**
   * 提交开票申请
   */
  public final static String SUBMIT_ORDER_INVOICE = "submitOrderInvoice";

  /**
   * 落地商开票保存
   */
  public final static String SUPPLIER_INVOICE_SAVE = "supplierInvoiceSave";

  /**
   * 供应商开票保存
   */
  public final static String SUPPLIER_OPEN_INVOICE_SAVE = "supplierOpenInvoiceSave";

  /**
   * 提交付款申请
   */
  public final static String SUBMIT_PAYMENT_APPLY = "submitPaymentApply";

  /**
   * 合并开票申请
   */
  public final static String INVOICE_APPLY_MERGE_LOCK = "invoiceApplyMergeLock";

  /**
   * 单个开票申请
   */
  public final static String INVOICE_APPLY_SINGLE_LOCK = "invoiceApplySingleLock";

  /**
   * ERP采购订单下推采购入库单
   */
  public final static String ADD_BILL_LOCK = "addBillLock";

  /**
   * 全局锁
   */
  public final static String GLOBAL_LOCK = "globalLock";

  /**
   * 落地商退货锁
   */
  public final static String ORDER_RETURN_LOCK = "orderReturnLock";

  /**
   * 采购订单发货单撤销锁
   */
  public final static String PURCHASE_ORDER_INVOICE_REVOKE = "purchaseOrderInvoiceRevoke";

  /**
   * 采购订单发货并且入库锁
   */
  public final static String PURCHASE_ORDER_DELIVERY_AND_CONFIRM_RECEIPT =
      "purchaseOrderDeliveryAndConfirmReceipt";

  /**
   * 采购订单入库锁
   */
  public final static String PURCHASE_ORDER_CONFIRM_RECEIPT =
      "purchaseOrderConfirmReceipt";

  /**
   * 入库/退货单 冲销
   */
  public final static String PURCHASE_ORDER_RECEIPT_OR_RETURN_REVERSAL =
      "purchaseOrderReceiptOrReturnReversal:";

  /**
   * 等待锁时间
   */
  public final static long WAIT_LOCK_TIME = 10L;

  /**
   * 持有锁
   */
  public final static long LEASE_LOCK_MAX_TIME = 60L;

  public final static String PURCHASE_ORDER_RETURN =
      "purchaseOrderReturn";
  /**
   * 组合锁：入库/退货单冲销 + 新增退库单 + 新增入库单
   */
  public final static String LOCK_GROUP_PURCHASE_ADD_RETURN_AND_REVERSAL =
      "lockGroupPurchaseReturnAndReversal";

  /**
   * srm 报备单生成filingNo加锁
   */
  public static final String FILING_NO_LOCK = "srm:filingNoLock";

  /**
   * srm 针对采购订单明细更新加锁 -- 颗粒度为采购订单id
   */
  public static final String PURCHASE_ORDER_UPDATE_LOCK = "srm:purchase_order_update:{}:shared";
}
