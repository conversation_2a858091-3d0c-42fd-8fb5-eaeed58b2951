package com.xhgj.srm.common.utils.asmDisOrder;/**
 * @since 2025/2/18 9:52
 */
import cn.hutool.core.collection.CollUtil;
import com.xhgj.srm.common.enums.asmDisOrder.AsmDisOrderType;
import com.xhgj.srm.common.utils.runner.JedisUtil;
import com.xhiot.boot.core.common.exception.CheckException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Set;

/**
 *<AUTHOR>
 *@date 2025/2/18 09:52:30
 *@description
 */
public class AsmDisOrderCleanerAndGenerator {
  // 单例实例
  public static final AsmDisOrderCleanerAndGenerator INSTANCE =
      new AsmDisOrderCleanerAndGenerator();
  private static final ThreadLocal<String> ORDER_NEW = ThreadLocal.withInitial(() -> null);
  private static final JedisUtil jedisUtil = JedisUtil.getInstance();
  private static final int RANGE_START = 1;
  private static final int RANGE_END = 9999;
  private static final String REDIS_ORDER_USED_KEY_PREFIX  = "srm_used_asm_dis_order_numbers:";
  private LocalDate now;

  public static void clear() {
    ORDER_NEW.remove();
  }

  // 获取当前日期（yyyy-MM）
  private String getCurrentDate() {
    now = LocalDate.now();
    return now.format(DateTimeFormatter.ofPattern("yyyy-MM"));
  }

  // 获取当月 Redis 键名
  private String getRedisKeyForUsedOrders(Byte type) {
    return REDIS_ORDER_USED_KEY_PREFIX + type + ":" + getCurrentDate();
  }

  public synchronized String generate(Byte type) {
    if (AsmDisOrderType.ASSEMBLY_ORDER.getCode() != type
        && AsmDisOrderType.DISASSEMBLY_ORDER.getCode() != type) {
      throw new CheckException("组装拆卸单类型错误");
    }
    String orderNumber = generateOrderNumber(type);
    if (orderNumber == null) {
      if (type == AsmDisOrderType.ASSEMBLY_ORDER.getCode()) {
        throw new CheckException("本月组装单流水号已用完");
      } else {
        throw new CheckException("本月拆卸单流水号已用完");
      }
    }
    String datetime = now.format(DateTimeFormatter.ofPattern("yyMM"));
    if (AsmDisOrderType.ASSEMBLY_ORDER.getCode() == type) {
      return "98" + datetime + orderNumber;
    } else {
      return "99" + datetime + orderNumber;
    }
  }

  // 生成一个新的订单号
  private String generateOrderNumber(Byte type) {
    String redisKey = getRedisKeyForUsedOrders(type);
    // 尝试生成一个未使用的单号
    // 获取已使用的单号集合
    Set<String> usedOrderNumbers = jedisUtil.smembers(redisKey);
    usedOrderNumbers = CollUtil.emptyIfNull(usedOrderNumbers);
    // 尝试生成一个未使用的单号
    for (int i = RANGE_START; i <= RANGE_END; i++) {
      String orderNumber = String.format("%04d", i);
      if (!usedOrderNumbers.contains(orderNumber)) {
        // 如果该单号没有被使用，加入到已使用集合中
        jedisUtil.sadd(redisKey, orderNumber);
        ORDER_NEW.set(orderNumber);
        return orderNumber;
      }
    }
    return null;
  }

  // 将单号回退到 Redis 列表中
  public void rollbackOrderNumber(Byte type) {
    if (ORDER_NEW.get() != null) {
      String redisKey = getRedisKeyForUsedOrders(type);
      // 将失败的单号从已使用集合中删除
      jedisUtil.srem(redisKey, ORDER_NEW.get());
    }
  }

  // 清空昨日的
  public void clearLastMonthOrderNumbers(LocalDate cronTime) {
    // 获取昨天的日期
    LocalDate lastMonth = cronTime.minusMonths(1);
    String lastMonthDateStr = lastMonth.format(DateTimeFormatter.ofPattern("yyyy-MM"));

    // 构建 Redis 键的前缀，删除上个月的数据
    String key1 =
        REDIS_ORDER_USED_KEY_PREFIX + AsmDisOrderType.ASSEMBLY_ORDER.getCode() + lastMonthDateStr;
    String key2 =
        REDIS_ORDER_USED_KEY_PREFIX + AsmDisOrderType.DISASSEMBLY_ORDER.getCode() + lastMonthDateStr;


    // 删除该日期下的所有记录
    jedisUtil.del(key1);
    jedisUtil.del(key2);
  }
}
