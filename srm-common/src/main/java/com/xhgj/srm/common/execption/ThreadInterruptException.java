package com.xhgj.srm.common.execption;/**
 * @since 2025/3/28 9:06
 */

/**
 * <AUTHOR>
 * 线程阻断异常
 * 用于指示当前执行流程应该被终止，不再执行后续方法
 */
public class ThreadInterruptException extends RuntimeException {

  /**
   * 创建一个无详细信息的线程阻断异常
   */
  public ThreadInterruptException() {
    super();
  }

  /**
   * 创建一个带详细信息的线程阻断异常
   *
   * @param message 异常的详细信息
   */
  public ThreadInterruptException(String message) {
    super(message);
  }

  /**
   * 创建一个带详细信息和原因的线程阻断异常
   *
   * @param message 异常的详细信息
   * @param cause 导致此异常的原因
   */
  public ThreadInterruptException(String message, Throwable cause) {
    super(message, cause);
  }

  /**
   * 创建一个带原因的线程阻断异常
   *
   * @param cause 导致此异常的原因
   */
  public ThreadInterruptException(Throwable cause) {
    super(cause);
  }

}

