package com.xhgj.srm.common.utils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 金额转大写
 */
public class MoneyUtil {

  /**金额格式化*/
  //public static DecimalFormat df = new DecimalFormat("#.####");
  /**大写数字*/
  private static String[] chineseNum = new String[] {"零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖"};
  /**数位*/
  private static String[] position = new String[] {"", "", "拾", "佰", "仟"};
  /**整数单位*/
  private static String[] intNumUnit = new String[] {"", "元", "万", "亿"};
  /**小数单位*/
  private static String[] decimalNumUnit = new String[] {"角", "分", "毫", "厘"};

  /**
   * 金额转大写
   * @param money
   * @return
   */
  public static String toChinese(BigDecimal money) {
    if (money == null || money.compareTo(BigDecimal.ZERO) == 0) {
      return "";
    }
    String moneyStr = money.toString();
    //整数和小数分割
    String[] arr = splitMoney(moneyStr);
    //整数部分
    String intNum = arr[0];
    //小数部分
    String decimalNum = arr.length>1 ? arr[1] : "";
    return intNumToChinese(intNum) + decimalNumToChinese(decimalNum);
  }

  /**
   * 整数部分大写
   * @param intNum
   * @return
   */
  private static String intNumToChinese(String intNum) {
    //如果是0，直接返回空
    if(Integer.parseInt(intNum)==0) {
      return "";
    }
    int len = intNum.length();
    int groups = len%4==0 ? len/4 : (len/4)+1;
    Map<Integer, String> map = intNumberDivide(intNum, groups);
    StringBuffer chineseNum = new StringBuffer();
    for(Integer i=groups; i>0; i--) {
      chineseNum.append(converte(i, map.get(i)));
    }
    return chineseNum.toString();
  }


  /**
   * 小数部分转大写
   * @param decimalNum
   * @return
   */
  private static String decimalNumToChinese(String decimalNum) {
    //如果是00，直接返回空
    if("".equals(decimalNum) || Long.parseLong(decimalNum)==0) {
      return "整";
    }
    int len = decimalNumUnit.length<decimalNum.length() ? decimalNumUnit.length : decimalNum.length();
    StringBuffer strb = new StringBuffer();
    for(int i=0; i<len; i++) {
      int n = Integer.parseInt(String.valueOf(decimalNum.charAt(i)));
      if(n!=0) {
        strb.append(chineseNum[n]).append(decimalNumUnit[i]);
      }
    }
    return strb.toString();
  }

  /**
   * 整数部分每四位分一组
   * @param intNum
   * @return
   */
  public static Map<Integer, String> intNumberDivide(String intNum, int groups) {
    Map<Integer, String> map = new HashMap<Integer, String>();
    int len = intNum.length();
    for(int i=1; i<=groups; i++) {
      int startIndex = (len-(4*i))<0 ? 0 : len-(4*i);
      map.put(i, intNum.substring(startIndex, len-(4*(i-1))));
    }
    return map;
  }

  /**
   * 转换
   * @param groupNum
   * @param numStr
   * @return
   */
  private static String converte(int groupNum, String numStr) {
    //如果是0000，直接返回空
    if(Integer.parseInt(numStr)==0) {
      return groupNum==1 ? intNumUnit[1] : "";
    }
    StringBuffer strb = new StringBuffer();
    int len = numStr.length();
    for(int i=0; i<len; i++) {
      int n = Integer.parseInt(String.valueOf(numStr.charAt(i)));
      if(n==0) {
        //最后一位是0，不转换
        if(i==len-1) {
          break;
        }
        //判断后一位数字
        int next = Integer.parseInt(String.valueOf(numStr.charAt(i+1)));
        //后一位数字不为0，此处拼 零
        if(next!=0) {
          strb.append(chineseNum[n]);
        }
      } else {
        strb.append(chineseNum[n]).append(position[len-i]);
      }
    }
    strb.append(intNumUnit[groupNum]);
    return strb.toString();
  }

  /**
   * 整数和小数分割
   * @param money
   * @return
   */
  private static String[] splitMoney(String money){
    String[] arr = money.split("\\.");
    //去除非数字字符
    arr[0] = arr[0].replaceAll("\\D", "");
    return arr;
  }

}
