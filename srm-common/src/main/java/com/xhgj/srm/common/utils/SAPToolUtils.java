package com.xhgj.srm.common.utils;

import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.NumberUtil;
import com.xhiot.boot.core.common.exception.CheckException;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2024-03-22 14:43
 */
public class SAPToolUtils {


  public static Pair<BigDecimal,Integer> convertSapPrice(BigDecimal price,int retainScale
  ){
    if (price==null) {
      return new Pair<>(BigDecimal.ZERO,1);
    }
    BigDecimal reduceAccuracyPrice = price.stripTrailingZeros();
    int scale = reduceAccuracyPrice.scale();
    if (scale>10) {
      throw new CheckException("小数位精度太大无法处理");
    }
    if (scale<=retainScale) {
      return new Pair<>(reduceAccuracyPrice,1);
    }
    int pow = scale - retainScale;
    int multiplier = NumberUtil.pow(10, pow).intValue();
    return new Pair<>(NumberUtil.mul(reduceAccuracyPrice,multiplier).setScale(retainScale),multiplier);
  }
}
