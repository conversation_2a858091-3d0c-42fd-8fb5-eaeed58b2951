package com.xhgj.srm.domain;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * Created by Geng Shy on 2023/12/13
 */
@Data
public class PurchaseOrderReturnVO {
  @ApiModelProperty("退库单 id")
  private String id;
  @ApiModelProperty("退库时间")
  private Long time;
  @ApiModelProperty("物流公司")
  private String logisticsCompany;
  @ApiModelProperty("物流编码")
  private String logisticsCode;
  @ApiModelProperty("快递单号")
  private String trackNum;
  @ApiModelProperty("物料凭证")
  private String productVoucher;
  @ApiModelProperty("物料凭证年度")
  private String productVoucherYear;
  @ApiModelProperty("冲销单物料凭证")
  private String sapReversalNo;
  @ApiModelProperty("退库原因")
  private String returnReason;
  @ApiModelProperty("状态")
  private String state;
  @ApiModelProperty("退库明细")
  private List<PurchaseOrderProductDetailedReturnVO> returnProductDTOList;
  @ApiModelProperty("wms仓库执行状态 0 未执行 1已执行")
  private String executionStatus;
  @ApiModelProperty("退货仓库")
  private String returnWarehouse;
  @ApiModelProperty("收件人")
  private String consignee;
  @ApiModelProperty("收件人地址")
  private String receiveAddress;
  @ApiModelProperty("是否需要开红票 0 不需要 1 需要")
  private String needRedTicket;
  @ApiModelProperty("SAP退库单采购订单号")
  private String sapReturnNumber;
  @ApiModelProperty("采购订单号")
  private String orderNo;
}
