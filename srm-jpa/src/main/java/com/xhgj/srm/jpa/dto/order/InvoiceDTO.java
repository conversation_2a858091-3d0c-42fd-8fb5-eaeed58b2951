package com.xhgj.srm.jpa.dto.order;

import com.xhgj.srm.jpa.dto.order.BaseOrderInvoiceDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class InvoiceDTO extends BaseOrderInvoiceDTO {

  @ApiModelProperty("开票 id")
  private String orderInvoiceId;

  @ApiModelProperty("发票类型")
  private String type;
  @ApiModelProperty("发票抬头")
  private String title;
  @ApiModelProperty("开票申请单号")
  private String invoiceApplicationNumber;
}
