package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.jpa.entity.PermissionUser;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;
import java.util.List;

/** <AUTHOR> @ClassName UserPermissionDao */
public interface PermissionUserDao extends BootBaseDao<PermissionUser> {

  /**
   * 删除当前权限类型id的数据 @Author: liuyq @Date: 2022/7/8 10:11
   *
   * @param permissionTypeId 权限类型id 必传
   * @return void
   */
  void deletePermissionUser(String permissionTypeId);

  /**
   * 根据权限类型id获取数用户id集合 @Author: liuyq @Date: 2022/7/8 15:23
   *
   * @param permissionTypeId 权限类型id 必传
   * @return java.util.List<java.lang.String>
   */
  List<String> getPermissionUserList(String permissionTypeId);
}
