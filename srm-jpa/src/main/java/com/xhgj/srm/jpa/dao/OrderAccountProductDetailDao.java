package com.xhgj.srm.jpa.dao;


import com.xhgj.srm.jpa.entity.OrderAccountProductDetail;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;
import org.springframework.data.domain.Page;

import java.util.List;

public interface OrderAccountProductDetailDao extends BootBaseDao<OrderAccountProductDetail> {

    /**
     * 删除订单对账详情
     *
     * @param detailId
     */
    void deleteOrderAccountProductByOrderAccount(String detailId);

    /**
     * 根据对账单分页获取其下商品明细
     *
     * @param accountId
     * @return
     */
    Page<OrderAccountProductDetail> getOrderAccountProductDetailPageByAccount(String accountId, int pageNo, int pageSize);

    /**
     * 根据回款订单分页获取其下商品明细
     *
     * @param accountId
     * @return
     */
    List<OrderAccountProductDetail> getOrderAccountProductDetailListByOrderNo(String accountId, String orderNo);


}
