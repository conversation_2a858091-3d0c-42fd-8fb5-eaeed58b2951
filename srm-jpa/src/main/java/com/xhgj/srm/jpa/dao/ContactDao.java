package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.jpa.entity.Contact;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;
import org.springframework.data.domain.Page;

import java.util.List;

public interface ContactDao extends BootBaseDao<Contact> {

  Contact getCurContactBySid(String sid, String mobile);

  List<Contact> getContactListBySid(String sid);

  List<Contact> getContactListByFbid(String sid);

  Contact getCurContactByFbid(String sid, String mobile);

  List<Contact> getContactListBySidAndUserid(String sid, String userid);

  Contact getCurContactBySidAndUser(String sid, String mobile, String name, String userid);

  Contact getCurContactBySidAndName(String sid, String name);

  void delContactBySidAndUid(String sid, String uid);

  /**
   * 根据供应商和用户分页获取联系人列表
   * @Author: liuyq
   * @Date: 2021/6/7 9:57
   * @param sid
   * @param supplierUserId
   * @param pageNo
   * @param pageSize
   * @return org.springframework.data.domain.Page<com.xhgj.srm.jpa.entity.Contact>
   **/
  Page<Contact> getContactListBySidAndUserId(String sid, String supplierUserId, String pageNo, String pageSize);

  List<String> getContactIdListBySid(String sid);

  /**
   * 根据组织内供应商 id 获得联系人列表
   * @param id 组织内供应商
   */
  List<Contact> getContactListBySupplierInGroupId(String id);

  /**
   * 更新多组织联系人
   * @param supplierInGroupId
   * @param supplierId
   */
  void updateContactSupplierInGroup(String supplierInGroupId, String supplierId);
}
