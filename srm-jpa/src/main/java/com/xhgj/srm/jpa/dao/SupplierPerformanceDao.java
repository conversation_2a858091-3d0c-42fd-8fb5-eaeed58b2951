package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.jpa.entity.SupplierPerformance;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;
import java.util.List;
import java.util.Map;

import org.springframework.data.domain.Page;

/**
 * <AUTHOR>
 * @since 2023-04-14 10:07
 */
public interface SupplierPerformanceDao extends BootBaseDao<SupplierPerformance> {

  /**
   * 通过供应商 id 获取供应商的履约信息
   *
   * @param supplierIdList 供应商 id 集合
   */
  List<SupplierPerformance> getListBySupplierId(List<String> supplierIdList);

  /**
   * 通过供应商 id 获取供应商的履约信息（生效状态）
   *
   * @param supplierIdList 供应商 id 集合
   */
  List<SupplierPerformance> getListBySupplierIdEffect(List<String> supplierIdList);

  /**
   * 通过供应商 id 获取供应商的履约信息 - 单条
   *
   * @param supplierId 供应商 id
   */
  List<SupplierPerformance> getListBySupplierId(String supplierId);

  /**
   * 根据供应商编码更新履约状态
   *
   * @param status 状态
   * @param supplierId 供应商id
   */
  void updateStatusBySupplierId(String status, String supplierId);

  /**
   * 根据落地商合同id查询
   *
   * @param contractId
   * @return
   */
  @Deprecated
  SupplierPerformance getByContractId(String contractId);

  /**
   * 根据供应商id和平台编码查询
   *
   * @param supplierId
   * @param platformCode
   * @return
   */
  SupplierPerformance getBySupplierIdAndPlatformCode(String supplierId, String platformCode);

  /**
   * 查询供应商履约信息对接采购集合
   *
   * @param supplierId
   * @param platformCode
   * @return
   */
  List<SupplierPerformance> getListBySupplierIdAndPlatformCode(
      String supplierId, String platformCode);

  /**
   * 根据平台code获取供应商
   *
   * @param list
   * @return
   */
  List<SupplierPerformance> getByCodeList(List<String> list);

  /**
   * @param platformCode 平台编码
   * @return 分页履约信息
   */
  Page<SupplierPerformance> findPageByPlatformCode(String platformCode, int pageNo, int pageSize);

  void updateByErpCode(String oldCode, String newCode);

  /**
   * 根据平台编码查询履约信息
   *
   * @param platform
   * @return
   */
    SupplierPerformance findByPlatform(String platform);

  /**
   * 根据合同删除所有的供应商履约信息
   * @param landingContractId 合同 id
   */
  void deleteByLandingContractId(String landingContractId);

  /**
   * 根据平台编码获取可接单供应商履约信息
   *
   * @param enterpriseName 供应商名称 模糊
   * @param platformCode 平台编码
   * @return [{供应商id、供应商名称、履约生效状态、关联合同id、我方签约主体编码、我方签约主体ERP编码、我方签约主体名称}]
   */
  List<Object[]> getListByPlatformCodeWithSupplierOpenOrder(
      String enterpriseName, String platformCode);

  /**
   * 根据平台编码获取可接单供应商履约信息（自定义平台使用）
   *
   * @param platformCode 平台编码
   * @return [{供应商id、供应商名称、履约生效状态、关联合同id、我方签约主体编码、我方签约主体ERP编码、我方签约主体名称}]
   */
  List<Object[]> getListByZDYPlatformCode(String platformCode);


  /**
   * 根据qurymap查询供应商履约信息 for batch
   * @param queryMap
   * @return
   */
  List<SupplierPerformance> getListByMapForBatch(Map<String, Object> queryMap);

  /**
   * 根据platformCodes查询供应商履约信息
   * @param platformCodes
   * @param state
   * @return
   */
  List countAllByPlatformCodeInAndState(List<String> platformCodes, String state);

  /**
   * 根据合作时间判断。若合同到期则关闭
   * 若是手动开启或报备单审批通过，则状态变为生效，若7天后还有未有合同附件归档，则自动更改状态为关闭
   */
  void refreshState();

  List<SupplierPerformance> findAllByContactsIsNull();

  List<SupplierPerformance> findAllByBusinessLeaderIsNull();
}
