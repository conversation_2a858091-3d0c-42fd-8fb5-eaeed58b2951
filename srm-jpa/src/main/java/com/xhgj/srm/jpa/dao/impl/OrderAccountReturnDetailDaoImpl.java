package com.xhgj.srm.jpa.dao.impl;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.Constants_order;
import com.xhgj.srm.jpa.dao.OrderAccountReturnDetailDao;
import com.xhgj.srm.jpa.entity.OrderAccountReturnDetail;
import com.xhiot.boot.core.common.util.DateUtils;
import com.xhiot.boot.core.common.util.ObjectUtils;
import com.xhiot.boot.core.common.util.StringUtils;
import com.xhiot.boot.framework.jpa.dao.AbstractBaseDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

@Slf4j
@Service
public class OrderAccountReturnDetailDaoImpl extends AbstractExtDao<OrderAccountReturnDetail> implements OrderAccountReturnDetailDao {

    @Override
    public List<OrderAccountReturnDetail> getOrderAccountReturnDetailByAccount(String accountId) {
        String hql = "from OrderAccountReturnDetail o where o.account.id = ? and o.returnState <> ? ";
        Object[] params = new Object[]{accountId, Constants_order.ORDER_REFUND_STATE_MAP_CANCEL};
        return getHqlList(hql, params);
    }

    @Override
    public Page<OrderAccountReturnDetail> getOrderAccountReturnDetailPageByAccount(String accountId, int pageNo, int pageSize) {
        String hql = "from OrderAccountReturnDetail o where o.account.id = ? and o.state <> ? ";
        Object[] params = new Object[]{accountId, Constants.COMMONSTATE_DELETE};
        return findPage(hql, params, pageNo, pageSize);
    }


    @Override
    public BigDecimal getOrderAccountReturnPriceByAccount(String accountId) {
        String hql = "select sum(od.price) from OrderAccountReturnDetail od where od.returnState = ? and od.account.id = ?  ";
        try {
            return getFirstHqlObj(hql, Constants_order.ORDER_REFUND_STATE_MAP_DONE,accountId)!=null?new BigDecimal(getFirstHqlObj(hql, Constants_order.ORDER_REFUND_STATE_MAP_DONE,accountId).toString()):BigDecimal.ZERO;
        } catch (Exception e) {
            log.error(ExceptionUtil.stacktraceToString(e));
            return BigDecimal.ZERO;
        }
    }

    @Override
    public OrderAccountReturnDetail getOrderAccountReturnByOmsId(String returnId) {
        String hql = "from OrderAccountReturnDetail o where o.omsReturnId = ? and o.returnState <> ? ";
        Object[] params = new Object[]{returnId, Constants_order.ORDER_REFUND_STATE_MAP_CANCEL};
        return getFirstHqlEntity(hql, params);
    }

    @Override
    public long getOrderAccountReturnCountByAccount(String accountId) {
        long count =0;
        String hql = " select count(od.id) from OrderAccountReturnDetail od where od.state != ? and od.account.id = ?  ";
        Object[] params = new Object[] {Constants.STATE_DELETE,accountId};
        try {
            count = count(hql,params);
        } catch (Exception e) {
            log.error(ExceptionUtil.stacktraceToString(e));
        }
        return count;
    }
}
