package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.jpa.entity.OrderSupplierInvoice;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;
import java.util.List;
import java.util.Optional;

public interface OrderSupplierInvoiceDao extends BootBaseDao<OrderSupplierInvoice> {

  /**
   * 根据对账单获取其下OrderAccountInvoice
   *
   * @param accountId
   * @return
   */
  List<OrderSupplierInvoice> getOrderAccountInvoiceByAccount(String accountId);

  /**
   * 根据发票号查找有效发票（数据状态正常、未被冲销）
   *
   * @param invoiceNum 发票号，必传
   */
  Optional<OrderSupplierInvoice> getValidInvoiceByNum(String invoiceNum);

  /**
   * 根据发票号列表查找有效发票（数据状态正常、未被冲销）
   *
   * @param invoiceNumList 发票号列表，必传
   */
  Optional<OrderSupplierInvoice> getValidInvoiceByInvoiceNumList(List<String> invoiceNumList);
}
