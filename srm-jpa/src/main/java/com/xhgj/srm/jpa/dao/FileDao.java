
package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.jpa.entity.File;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;
import java.util.List;
import org.springframework.data.domain.Page;

public interface FileDao extends BootBaseDao<File> {

  List<File> getFileListByRId(String relationId);

  void delFilesByRelationId(String relationId);

  List<File> getFileListBySId(String relationid, String type);

  List<File> getFileListBySIdNoTransactional(String relationid, String type);

  List<File> getFileListByRelationIdAndTypesAndState(String relationId, List<String> types,
      String state);

  List<File> getFileListByRIdAndAgree(String relationid);

  List<File> getFileListByRIdAndRtype(String relationid);

  void delFilesByRelationIdAndType(String relationId);

    void delFilesByIdAndType(String id, String type1, String type2, String contractid);

  /**
   * 根据关系表 id 和关系类型删除文件
   * @param relationId 关系表 id
   * @param relationTypeList 关系类型
   * @return
   */
  int deleteByRelationIdAndRelationTypeIn(String relationId, List<String> relationTypeList);


    /**
     *
     * @param relationId
     * @param relationType
     * @return
     */
  int deleteByRelationIdAndRelationType(String relationId, String relationType);

   /**
    * 根据关联id和类型、搜索条件分页获取文件列表
    * @Author: liuyq
    * @Date: 2021/6/23 17:43
    * @param supplierId
    * @param fileName
    * @param brandName
    * @param supplierName
    * @param type
    * @param pageNo
    * @param pageSize
    * @return org.springframework.data.domain.Page<com.xhgj.srm.jpa.entity.File>
    **/
    Page<String> getFileListByRelationIdAndType(String supplierId, String fileName, String brandName, String supplierName, String type,  String pageNo, String pageSize);


    /**
     * 根据类型和关联id、品牌id获取文件
     * @Author: liuyq
     * @Date: 2021/6/30 9:41
     * @param supplierId
     * @param type
     * @param brandId
     * @return com.xhgj.srm.jpa.entity.File
     **/
    List<File> getFileByRelationIdAndType(String supplierId, String type, String brandId);

    /**
     * 获取供应商资质证照
     * @Author: liuyq
     * @Date: 2021/6/3 15:03
     * @param id
     * @return com.xhgj.srm.jpa.entity.File
     **/
    List<File> getFileBySupplierId(String id,String type);


    /**
     * 根据品牌id获取品牌授权
     * @Author: liuyq
     * @Date: 2021/9/8 16:13
     * @param brandId
     * @param type
     * @return java.util.List<com.xhgj.srm.jpa.entity.File>
     **/
    List<String> getFileByBrandId(String brandId,String type);


    /**
     * 获取财务信息附件
     * @Author: liuyq
     * @Date: 2021/8/5 17:34
     * @param id
     * @param type
     * @return com.xhgj.srm.jpa.entity.File
     **/
    File getSingleFileBySupplierId(String id,String type);


    /**
     * 根据类型获取未读的附件列表
     * @Author: liuyq
     * @Date: 2021/7/2 15:09
     * @param id
     * @param type
     * @return java.util.List<com.xhgj.srm.jpa.entity.File>
     **/
    List<File> getFileListBySupplierId(String id, String type);


   void delFilesByIdAndRelation(String id, String relationId);

    /**
     * 删除对应附件
     * @param id
     */
    void delCurFileById(String id);

    /**
     * 判断是否存在验收单
     * @param id
     */
    boolean isHaveAcceptFileById(String id);

  /**
   * 根据合同的 id 获得合同文件的数量
   * @param contractSignedSupplierIdList 合同 id 的集合
   */
  long getContractFileCountByContractId(List<String> contractSignedSupplierIdList);

  /**
   * 根据所传的组织下供应商的 id 集合获取这些供应商有协议的数量
   *
   * @param myResponsibleSupplierIdList 组织下供应商的 id 集合 必传
   */
  long getNumberOfSuppliersWithAgreementBySupplierInGroupIds(List<String> myResponsibleSupplierIdList);

  /**
   * 根据关联 id 和类型获得文件列表
   * @param relationId 文件关联 id 必传
   * @param types 文件类型必传
   */
  List<File> getByRelationIdAndTypes(String relationId, List<String> types);

  /**
   * 更新多组织供应商附件
   * @param supplierInGroupId
   * @param supplierId
   * @param type
   */
  void updateFileSupplierInGroup(String supplierInGroupId, String supplierId,String type);

  /**
   * 通过条件判断是否存在该文件
   * @param brandId 品牌 id
   * @param type 类型 必传
   * @param relationType 关联类型 必传
   * @param relationId 关联 id 必传
   */
  boolean existByBrandIdAndTypeAndRelationTypeAndRelationId(String brandId, String type, String relationType, String relationId);

  /**
   * 根据关联id获取第一个
   * @param relationid
   * @return
   */
  File getFirstFileByRId(String relationid);

  /**
   * 根据关联id和类型获取第一个
   * @param relationId
   * @param relationType
   * @return
   */
  File getFirstFileByRIdAndRType(String relationId, String relationType);

  /**
   * 根据id和类型获取文件集合
   * @param id
   * @param type
   * @return
   */
  List<File> getFileList(String id, String type);
}