package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.jpa.entity.OrderReturnDetail;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;
import java.math.BigDecimal;
import java.util.List;

public interface OrderReturnDetailDao extends BootBaseDao<OrderReturnDetail> {

    /**
     * 获取退货单详情
     * @param orderId
     * @return
     */
    List<OrderReturnDetail> getOrderReturnDetailByOrderId(String orderId);

    /**
     * 通过退货单id获取退货单详情
     * @param returnId
     * @return
     */
    List<OrderReturnDetail> getOrderReturnDetailByOrderReturnId(String returnId);

  List<OrderReturnDetail> getOrderReturnDetailByOrderReturnId(String returnId, String type);

  /**
     * 获取单个商品的取消数量
     * @return
     */
  @Deprecated
  BigDecimal getOrderReturnDetailSumByCodeAndOrderId(String orderId,String code);

    /**
     * 获取订单的取消数量
     * @return
     */
    @Deprecated
    BigDecimal getOrderReturnDetailSumByOrderId(String orderId);
}
