package com.xhgj.srm.jpa.dao.impl;

import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.Constants_order;
import com.xhgj.srm.jpa.dao.OrderPaymentToOrderDao;
import com.xhgj.srm.jpa.entity.OrderPaymentToOrder;
import com.xhiot.boot.framework.jpa.dao.AbstractBaseDao;
import java.math.BigDecimal;
import org.springframework.stereotype.Repository;

@Repository
public class OrderPaymentToOrderDaoImpl extends AbstractExtDao<OrderPaymentToOrder> implements
    OrderPaymentToOrderDao {

  public BigDecimal getPaymentFinalPrice(String paymentId) {
    String sql =
        "select sum(o.c_price) - sum(o.c_refund_price) from t_order_payment_to_order op left join"
            + " t_order o on CONVERT(op.relation_id USING utf8) = o.id where op.c_type = ? and op"
            + ".c_order_payment_id "
            + " = ? and op.c_state = ? ";
    Object[] params = new Object[] {Constants_order.ORDER_TO_PAYMENT_TYPE_ORDER_ID, paymentId,
        Constants.STATE_OK};
    Object uniqueSqlObj = getUniqueSqlObj(sql, params);
    if (uniqueSqlObj instanceof BigDecimal) {
      return (BigDecimal) uniqueSqlObj;
    }
    return BigDecimal.ZERO;
  }
}
