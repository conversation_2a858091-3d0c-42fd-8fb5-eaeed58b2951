package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.jpa.entity.Product;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;
import java.util.List;
import java.util.Map;
import org.springframework.data.domain.Page;

/**
 * @ClassName ProductDao Create by Liuyq on 2021/6/1 9:35
 */
public interface ProductDao extends BootBaseDao<Product> {

  /**
   * 根据物料状态获取物料个数 @Author: liuyq @Date: 2021/6/1 9:42
   *
   * @param state
   * @return long
   */
  long getProductCountByState(String state, String supplierId, String saleState);

  /**
   * 根据物料状态获取物料个数 @Author: liuyq @Date: 2021/6/1 9:42
   *
   * @param state
   * @return long
   */
  long getProductCountBySupplierId(String state);

  /**
   * 获取物料列表 - 分页
   *
   * @param queryMap
   */
  Page<Product> getCheckProductPageRef(Map<String, Object> queryMap);

  /** 获取商品列表 */
  List<Product> getProductListBySupplier(String supplierId, String batchNumber, String state);

  List<Product> getUnPassProductListBySupplier(String supplierId);

  /**
   * 获取删除物料 @Author: liuyq @Date: 2021/6/1 9:42
   *
   * @return long
   */
  Product getProductBySupplierAndTempCode(String tempCode, String supplierId);

  Product getProductByTempCode(String tempCode);

  Product getProductByTempCodeAndState(String tempCode, String state);

  Product getValidProduct(
      String name,
      String model,
      String brandEn,
      String brandCn,
      String unit,
      String desc,
      String id);

  /**
   * 判断是否存在关联品牌的物料 @Author: liuyq @Date: 2021/6/29 17:28
   *
   * @param mdmBrandId
   * @return com.xhgj.srm.jpa.entity.Product
   */
  boolean getProductByMdmBrandId(String mdmBrandId, String supplierId);

  /**
   * 获取物料列表 - 分页
   *
   * @return
   */
  Page<String> getUnreviewedPage(
      String state,
      String supplierId,
      String productType,
      String code,
      String name,
      String brand,
      String model,
      String unit,
      String referenceSupplyPrice,
      String relevancyPlatform,
      String mainPicture,
      String detailsPicture,
      String extendedAttribute,
      int pageNo,
      int pageSize);
}
