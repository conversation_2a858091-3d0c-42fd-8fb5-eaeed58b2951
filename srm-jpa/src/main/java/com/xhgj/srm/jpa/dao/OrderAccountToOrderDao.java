package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.jpa.entity.OrderAccountToOrder;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;
import java.util.List;
import java.util.Map;

public interface OrderAccountToOrderDao extends BootBaseDao<OrderAccountToOrder> {

  /**
   * 对账单id
   *
   * @param accountId
   * @return
   */
  long getOrderCountByOrderAccount(String accountId);

  /**
   * 对账单id
   *
   * @param accountIds
   * @return
   */
  Map<String, Long> getOrderCountByOrderAccounts(List<String> accountIds);

  List<String> getOrderIdByOrderAccount(String accountId);

}
