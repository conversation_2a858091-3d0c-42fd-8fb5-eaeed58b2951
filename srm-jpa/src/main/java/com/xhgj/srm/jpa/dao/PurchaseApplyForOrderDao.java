package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.jpa.dto.PurchaseApplyForOrderPageQueryDTO;
import com.xhgj.srm.jpa.dto.purchase.apply.PurchaseApplyStatistics;
import com.xhgj.srm.jpa.dto.purchase.order.PurchaseOrderTableHeaderParams;
import com.xhgj.srm.jpa.entity.PurchaseApplyForOrder;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;
import org.springframework.data.domain.Page;
import java.util.List;

/**
 * Created by Geng Shy on 2023/12/11
 */
public interface PurchaseApplyForOrderDao extends BootBaseDao<PurchaseApplyForOrder> {

  Page<PurchaseApplyForOrder> findPage(PurchaseApplyForOrderPageQueryDTO pageQueryDTO);

  PurchaseApplyStatistics findStatistics(PurchaseApplyForOrderPageQueryDTO pageQueryDTO);

  List<PurchaseApplyForOrder> findList(PurchaseApplyForOrderPageQueryDTO pageQueryDTO);

  List<String> getPurchaseMansByName(List<String> nameList,String applyType);

  List<String> getApplyListByTableHeader(PurchaseOrderTableHeaderParams params);

  /**
   * 根据采购申请单号和行ids查询采购申请单
   * @param purchaseOrderCode 采购申请单号
   * @param rowIds 行ids
   */
  List<PurchaseApplyForOrder> findAllByPurchaseOrderCodeAndRowIds(String purchaseOrderCode,
      List<String> rowIds);
}
