package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.entity.ProductField;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;
import java.util.List;
import org.springframework.transaction.annotation.Transactional;

/**
 * @description 物料上架项目字段表 Dao
 * <AUTHOR>
 * @date 2023-05-06 18:31:11
 */
public interface ProductFieldDao extends BootBaseDao<ProductField> {

  /**
   * 根据物料id和项目编码获取物料关联项目字段信息
   * @Author: weiquan
   * @Date: 2023/05/10
   * @param productId 物料id
   * @param platform 项目编码
   * @return List<ProductField>
   **/
  List<ProductField> getProductFieldByParams(String productId,String platform);

  /**
   * 根据物料id删除项目字段
   * @param productId
   */
  void deleteByProductId(String productId);

}

