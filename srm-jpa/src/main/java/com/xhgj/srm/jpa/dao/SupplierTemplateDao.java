package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.jpa.entity.SupplierTemplate;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;
import org.springframework.data.domain.Page;

/**
 * <AUTHOR>
 * @since 2022/7/5 15:56
 */

public interface SupplierTemplateDao extends BootBaseDao<SupplierTemplate> {

  /**
   * 查询供应商模板
   * @param companyName 企业名称
   * @param pageNo 页码
   * @param pageSize 一页展示最大条数
   */
  Page<SupplierTemplate> findPage(String companyName,int pageNo,int pageSize);

  /**
   * 根据模板 id 查询供应商模板
   * @param id 供应商模板 id 必传
   */
  SupplierTemplate getById(String id);

  /**
   * 根据组织 id 获得模板
   * @param groupId 组织 id 必传
   */
  SupplierTemplate getSupplierTemplateByGroupId(String groupId);
}
