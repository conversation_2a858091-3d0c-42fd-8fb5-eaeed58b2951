package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.jpa.entity.EntryRegistrationOrder;
import com.xhgj.srm.jpa.entity.User;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;
import org.springframework.data.domain.Page;
import java.util.List;
import java.util.Map;

/**入住报备*/
public interface EntryRegistrationDao extends BootBaseDao<EntryRegistrationOrder> {

  /**
   * 入住报备分页列表
   * @param registrationNumber
   * @param registrationStatus
   * @param partnerName
   * @param salesmanName
   * @param pageNo
   * @param pageSize
   * @return
   */
  Page<Object[]> entryRegistrationPageList(String registrationNumber, String registrationStatus, String partnerName, String salesmanName, Integer pageNo, Integer pageSize);

  /**
   * 逻辑删除入驻报备单
   *
   * @param ids 报备单id
   */
  void logicDeleteInBatch(List<String> ids, User user);

  /**
   * 入住报备分页列表
   * @param registrationNumber 报备单号
   * @param registrationStatus 报备状态
   * @param partnerName 合作商名称
   * @param salesmanName 业务员名称
   * @param pageNo 页码
   * @param pageSize 每页大小
   * @return
   */
  Page<EntryRegistrationOrder> getPage(String registrationNumber, String registrationStatus, String partnerName, String salesmanName, int pageNo, int pageSize);

  /**
   * 入住报备分页列表
   * @param queryMap
   * @return
   */
  Page<EntryRegistrationOrder> getPageRef(Map<String,Object> queryMap);
}
