package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.jpa.entity.SupplierInGroup;
import java.util.List;
import java.util.Map;
import org.springframework.data.domain.Page;

/**
 * <AUTHOR>
 * @since 2022/7/10 16:38
 */
public interface SupplierInGroupDao {

  /**
   * 根据供应商 id 获得供应商
   * @param queryMap
   * @return
   */
  Page<SupplierInGroup> getSupplierDomesticByPageRef(Map<String, Object> queryMap);

  /**
   * 分页获取国内供应商
   *
   * @param userId 用户 id 必传
   * @param groupId 组织 id 必传
   * @param mdmCode MDM 编码
   * @param enterpriseName 企业名称
   * @param enterpriseLevel 等级
   * @param startTime 时间范围 开始
   * @param endTime 时间范围 结束
   * @param purchaserName 负责人
   * @param brands 品牌
   * @param contacts 联系人
   * @param mobile 联系方式
   * @param integrity 完善度
   * @param pageNo 页码
   * @param pageSize 展示条数
   */
  @Deprecated
  Page<SupplierInGroup> getSupplierDomesticByPage(
      String userId,
      String groupId,
      String mdmCode,
      String enterpriseName,
      String enterpriseLevel,
      Long startTime,
      Long endTime,
      String purchaserName,
      String brands,
      String categories,
      String contacts,
      String mobile,
      String integrity,
      Boolean uploadLicenseUrl ,
      int pageNo,
      int pageSize);

  @Deprecated
  List<String> getSupplierDomesticIdList(
      String userId,
      String groupId,
      String mdmCode,
      String enterpriseName,
      String enterpriseLevel,
      Long startTime,
      Long endTime,
      String purchaserName,
      String brands,
      String categories,
      String contacts,
      String mobile,
      String integrity,
      Boolean uploadLicenseUrl);

  /**
   * 分页获取国外供应商
   *
   * @param groupId 组织 id 必传
   * @param mdmCode MDM 编码
   * @param enterpriseName 企业名称
   * @param country 国家
   * @param purchaserName 负责人
   * @param enterpriseLevel 企业等级
   * @param startTime 时间范围【开始】
   * @param endTime 时间范围【结束】
   * @param pageNo 页码
   * @param pageSize 每页展示条数
   */
  Page<SupplierInGroup> getSupplierAbroadPage(
      String groupId,
      String mdmCode,
      String enterpriseName,
      String country,
      String purchaserName,
      String enterpriseLevel,
      Long startTime,
      Long endTime,
      int pageNo,
      int pageSize);

  List<String> getSupplierAbroad(
      String groupId,
      String mdmCode,
      String enterpriseName,
      String country,
      String purchaserName,
      String enterpriseLevel,
      Long startTime,
      Long endTime);

  /**
   * 分页获取个人供应商
   *
   * @param groupId 组织 id 必传
   * @param mdmCode mdm 编码
   * @param name 姓名
   * @param mobile 联系方式
   * @param purchaserName 负责人
   * @param startTime 时间范围 开始
   * @param endTime 时间范围 结束
   * @param pageNo 页码
   * @param pageSize 每页展示条数
   */
  Page<SupplierInGroup> getSupplierPersonByPage(
      String groupId,
      String mdmCode,
      String name,
      String mobile,
      String purchaserName,
      Long startTime,
      Long endTime,
      int pageNo,
      int pageSize);

  /**
   * 获取个人供应商
   * @param groupId
   * @param mdmCode
   * @param name
   * @param mobile
   * @param purchaserName
   * @param startTime
   * @param endTime
   * @return
   */
  List<String> getSupplierPerson(
      String groupId,
      String mdmCode,
      String name,
      String mobile,
      String purchaserName,
      Long startTime,
      Long endTime);

  /**
   * 根据采购人 id 和组织编码获得组织内供应商
   *
   * @param purchaserId 采购人 id 必传
   * @param groupId 组织 id 必传
   */
  List<SupplierInGroup> getSupplierInGroupByPurchaserIdAndGroupId(
      String purchaserId, String groupId);

  /**
   * 根据采购人 id 和组织 id，供应商等级获得供应商数量
   *
   * @param purchaserId 采购人 id 必传
   * @param groupId 组织 id 必传
   * @param level 供应商等级 必传
   */
  long getVariousSuppliersCountByLevel(String purchaserId, String groupId, String level);

  /**
   * 根据组织 id 获得供应商的数量
   *
   * @param groupId 组织 id
   */
  long getCountByGroupId(String groupId);

  /**
   * 根据用户 id 获得内部待审核的数量
   *
   * @param userId 用户 id
   */
  long getWaitAuditCount(String userId);

  /**
   * 通过用户 id 查询有关联的供应商
   *
   * @param userId
   */
  List<SupplierInGroup> getEnterpriseByUser(String userId);

  /**
   * 根据用户 id 和组织编码和供应商类型获得组织下国内供应商列表
   *
   * @param userId 用户 id
   * @param groupId 使用组织 id
   * @param supplierInGroupIdList 供应商 id 集合
   */
  List<SupplierInGroup> getSupplierInGroupChinaByUserId(
      String userId, String groupId, List<String> supplierInGroupIdList);

  /**
   * 根据用户 id 和组织编码和供应商类型获得组织下国际供应商列表
   *
   * @param userId 用户 id
   * @param groupId 使用组织 id
   * @param supplierInGroupIdList 供应商 id 集合
   */
  List<SupplierInGroup> getSupplierInGroupAbroadByUserId(
      String userId, String groupId, List<String> supplierInGroupIdList);

  /**
   * 根据用户 id 和组织编码和供应商类型获得组织下个人供应商列表
   *
   * @param userId 用户 id
   * @param groupId 使用组织 id
   * @param supplierInGroupIdList 供应商 id 集合
   */
  List<SupplierInGroup> getSupplierInGroupPersonByUserId(
      String userId, String groupId, List<String> supplierInGroupIdList);

  /**
   * 根据企业名称和组织编码查询组织下供应商
   *
   * @param enterpriseName 企业名称，必传
   * @param groupCode 组织编码，必传
   * @param supType 供应商类型
   */
  SupplierInGroup getSupplierInGroupByEnterpriseNameAndGroupCode(
      String enterpriseName, String groupCode, String supType);
  /**
   * 根据主数据编码和组织编码查询组织下供应商
   *
   * @param mdmCode MDM 编码，必传
   * @param groupCode 组织编码，必传
   */
  SupplierInGroup getSupplierInGroupByMDMAndGroupCode(
      String mdmCode, String groupCode);

  /**
   * 根据企业名称和使用组织获取供应商
   *
   * @param entername 企业名称
   * @param useGroup 使用组织
   */
  SupplierInGroup getByEnterpriseNameAndUseGroup(String entername, String useGroup);

  /**
   * 根据 erpCode 获得供应商
   *
   * @param code erpCode
   */
  SupplierInGroup getByErpCode(String code);

  /**
   * 通过姓名和联系方法和组织 id 获得个人供应商
   *
   * @param personName 姓名
   * @param mobile 联系方式
   * @param groupId 组织 id
   */
  SupplierInGroup getSupplierInGroupByEnterpriseNameAndMobileAndGroupId(
      String personName, String mobile, String groupId);

  /**
   * 根据组织编码获得组织下供应商
   *
   * @param erpCode erpCode
   */
  List<SupplierInGroup> getEnterpriseByOrg(String erpCode);

  /**
   * @uthor: liuyq @Date: 2022/8/2 19:42 判断部门组织下是否有关联的供应商
   * @param userId 用户id，必传
   * @param groupId 组织id，必传
   * @return boolean
   */
  boolean isExistSupplierByGroupAndUserId(String userId, String groupId);

  /**
   * 根据供应商 id 批量修改数据状态
   *
   * @param supplierId 供应商 id，必传
   * @param state 数据状态，必传
   */
  void updateStateBySupplierId(String supplierId, String state);

  /**
   * 根据供应商id和使用组织获取组织下供应商
   *
   * @param supplierId 供应商id
   * @param erpCode 组织编码
   */
  SupplierInGroup getByGroupAndSupplier(String supplierId, String erpCode);

  /**
   * 根据供应商 id 获取所有组织内供应商
   *
   * @param supplierId 供应商 id
   */
  List<SupplierInGroup> getAllBySupplier(String supplierId);

  /**
   * @param supplierId
   * @return
   */
  List<String> getAllInGroupERPCodeBySupplier(String supplierId);

  /**
   * 根据供应商 id 获取所有组织内供应商 id
   *
   * @param supplierId 供应商 id，必传
   */
  List<String> getAllInGroupIdBySupplier(String supplierId);

  /**
   * 根据mdm编码和使用组织获取供应商
   *
   * @param mdmCode mdm编码
   * @param useGroup 使用组织
   */
  SupplierInGroup getByMdmCodeAndUseGroup(String mdmCode, String useGroup);

  /**
   * 获取所有组织下供应商
   * @return
   */
  List<SupplierInGroup> getAllSupplierInGroup();

  /**
   * 根据供应商 id 和排序获取第一个组织下供应商
   * @param supplierId 供应商 id 必传
   * @param createTimeAsc 是否时间升序
   */
  SupplierInGroup getFirstBySupplierIdOrderCreateTime(String supplierId, Boolean createTimeAsc);

  long countSupplierInGroupByEnterpriseNameAndGroupCode(List<String> enterpriseName, String groupCode,
      String supType);

  /**
   * 根据供应商 id更新采购人
   */
  void updatePurChaserBySupplierIds(String purchaserId, List<String> ids);

  /**
   * 获取组织内供应商默认税率
   * @param supplierId 供应商 id，必传
   * @param groupId 组织 id，必传
   */
  String getTaxRateBySupplierAndGroupId(String supplierId,String groupId);
}
