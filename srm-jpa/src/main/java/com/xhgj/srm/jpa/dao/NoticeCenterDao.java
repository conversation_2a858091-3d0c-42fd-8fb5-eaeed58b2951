package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.jpa.entity.NoticeCenter;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;
import java.util.List;
import org.springframework.data.domain.Page;

/** <AUTHOR> @ClassName NoticeCenterDao */
public interface NoticeCenterDao extends BootBaseDao<NoticeCenter> {

  /**
   * 分页获取消息中心 @Author: liuyq @Date: 2022/12/29 14:08
   *
   * @param supplierId 供应商id
   * @param isRead 读取状态
   * @param type 类型
   * @param content 内容
   * @param pageNo 页数
   * @param pageSize 条数
   * @return org.springframework.data.domain.Page<com.xhgj.srm.jpa.entity.NoticeCenter>
   */
  Page<NoticeCenter> getPage(
      String supplierId, Boolean isRead, String type, String content, int pageNo, int pageSize);

  /**
   * 获取消息列表 @Auhor: liuyq @Date: 2022/12/30 10:17
   *
   * @param size 条数
   * @param supplierId 供应商id，必传
   * @return java.util.List<com.xhgj.srm.jpa.entity.NoticeCenter>
   */
  List<NoticeCenter> getNoticeCenterList( String supplierId,int size);
}
