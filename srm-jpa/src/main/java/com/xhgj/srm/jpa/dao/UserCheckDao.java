package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.jpa.entity.UserCheck;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;

import java.util.List;

public interface UserCheckDao extends BootBaseDao<UserCheck> {

    UserCheck getUserCheckByUser(String userId,String type,String level);

    List<UserCheck> getUserCheckListByUser(String userId);
    void updateCodeByCode(String oldCode,String newCode);

}
