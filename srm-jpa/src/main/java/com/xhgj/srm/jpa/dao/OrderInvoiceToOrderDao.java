package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.jpa.entity.OrderInvoiceToOrder;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/7
 */
public interface OrderInvoiceToOrderDao extends BootBaseDao<OrderInvoiceToOrder> {

  /**
   * 根据订单号获取
   * @param id
   * @return
   */
  List<OrderInvoiceToOrder> getByOrderId(String id);

  /**
   * 根据订单号获取第一个
   * @param id
   * @return
   */
  OrderInvoiceToOrder getFirstByOrderId(String id);

  String getFirstOrderInvoiceIdByOrderId(String id);

  /**
   * 根据发票id获取
   * @param id
   * @return
   */
  List<OrderInvoiceToOrder> getByInvoiceId(String id);

  /**
   * 根据开票状态和数据状态获取总计
   * @param invoiceState
   * @param state
   * @return
   */
  Long getCountByInvoiceType(String invoiceState, String state);

}
