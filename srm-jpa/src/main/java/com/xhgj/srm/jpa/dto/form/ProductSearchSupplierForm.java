package com.xhgj.srm.jpa.dto.form;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Map;

/**
 * <AUTHOR>
 * supplier物料查询表单
 */
@ApiModel(description = "supplier物料查询表单")
@Data
public class ProductSearchSupplierForm extends ProductSearchBaseForm {

  /**
   * 咸亨上架开始时间
   */
  @ApiModelProperty("咸亨上架开始时间")
  private String firstPassStart;

  /**
   * 咸亨上架结束时间
   */
  @ApiModelProperty("咸亨上架结束时间")
  private String firstPassEnd;

  /**
   * 复写toQueryMap方法
   * @return
   */
  @Override
  public Map<String, Object> toQueryMap() {
    Map<String, Object> queryMap = super.toQueryMap();
    // firstPassStart
    if (StrUtil.isNotBlank(firstPassStart)) {
      // 转换为时间戳
      LocalDateTime start = LocalDateTimeUtil.parse(firstPassStart, "yyyy-MM-dd");
      // 设置为一天的开始
      start = start.withHour(0).withMinute(0).withSecond(0).withNano(0);
      queryMap.put("firstPassStart", start.toInstant(ZoneOffset.of("+8")).toEpochMilli());
    }
    // firstPassEnd
    if (StrUtil.isNotBlank(firstPassEnd)) {
      // 转换为时间戳
      LocalDateTime end = LocalDateTimeUtil.parse(firstPassEnd, "yyyy-MM-dd");
      // 设置为一天的结束
      end = end.withHour(23).withMinute(59).withSecond(59).withNano(999);
      queryMap.put("firstPassEnd", end.toInstant(ZoneOffset.of("+8")).toEpochMilli());
    }
    // shipperCode
    queryMap.put("shipperCode", null);
    return queryMap;
  }
}
