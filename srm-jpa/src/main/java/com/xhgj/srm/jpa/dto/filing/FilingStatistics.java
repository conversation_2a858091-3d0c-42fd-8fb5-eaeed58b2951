package com.xhgj.srm.jpa.dto.filing;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 */
@Data
public class FilingStatistics {
  /**
   * 报备金额
   */
  @ApiModelProperty("报备金额")
  private BigDecimal totalPrice;

  public BigDecimal getTotalPrice() {
    if (totalPrice == null) {
      return BigDecimal.ZERO;
    }
    return totalPrice.setScale(2, RoundingMode.HALF_UP);
  }
}
