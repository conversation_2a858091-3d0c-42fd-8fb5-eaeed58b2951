package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.jpa.entity.ErpRequestParams;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;
import java.util.List;

/**
 * <AUTHOR> @date 2023/7/20
 */
public interface ErpRequestParamsDao extends BootBaseDao<ErpRequestParams> {

  /**
   * 根据创建时间查询
   * @param startTime 开始时间
   * @param endTime 结束时间
   * @return
   */
  List<ErpRequestParams> queryByCreateTime(Long startTime, Long endTime);
}
