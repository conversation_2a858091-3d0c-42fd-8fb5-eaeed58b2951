package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.jpa.entity.OrderAccountDetail;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;
import org.springframework.data.domain.Page;

import java.util.List;

public interface OrderAccountDetailDao  extends BootBaseDao<OrderAccountDetail> {


    /**
     * 根据订单获取对账单详情
     * @param orderNo
     * @param platform
     * @return
     */
    OrderAccountDetail getOrderAccountDetailByOrder(String orderNo, String platform);


    /**
     * 根据对账单获取其下orderAccoutDetail
     * @param accountId
     * @return
     */
    List<OrderAccountDetail> getOrderAccountDetailByAccount(String accountId);

    /**
     * 根据对账单分页获取其下orderAccoutDetail
     * @param accountId
     * @return
     */
    Page<OrderAccountDetail> getOrderAccountDetailPageByAccount(String accountId,int pageNo,int pageSize);

    /**
     * 判断订单是否有对应的对账单
     * @param orderNo
     * @param platform
     * @return
     */
    boolean isHaveOrderAccountDetailByOrderNo(String orderNo, String platform);
}
