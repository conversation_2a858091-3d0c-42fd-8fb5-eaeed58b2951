package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.jpa.entity.DingCardInfo;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;
import java.util.List;

/**
 * <AUTHOR> Shangyi
 */
public interface DingCardInfoDao extends BootBaseDao<DingCardInfo> {

  DingCardInfo getByRelevanceId(String relevanceId);

  /**
   * 根据关联id和类型查询
   * @param relevanceId
   * @param type
   * @return
   */
  List<DingCardInfo> getByLikeRelevanceIdAndType(String relevanceId, String type);

  /**
   * 根据关联id和类型和更新状态查询
   */
  List<DingCardInfo> getByLikeRelevanceIdAndTypeAndUpdateState(String relevanceId, String type,
      String updateState);
}
