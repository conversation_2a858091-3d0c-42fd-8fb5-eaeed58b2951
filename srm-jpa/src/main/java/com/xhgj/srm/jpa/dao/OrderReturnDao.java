package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.jpa.entity.OrderReturn;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;
import java.util.List;
import org.springframework.data.domain.Page;

public interface OrderReturnDao  extends BootBaseDao<OrderReturn> {

    /**
     * 分页获取退货单信息
     * @param supplierId
     * @param orderNo
     * @param orderReturnNo
     * @param platform
     * @param returnPrice
     * @param customer
     * @param returnCount
     * @param returnType
     * @param returnState
     * @param returnApplyStartDate
     * @param returnApplyEndDate
     * @param schemeId
     * @param pageNo
     * @param pageSize
     * @return
     */
    Page<OrderReturn> getOrderReturnPageBySupplier(String supplierId,String orderNo, String orderReturnNo, String platform, String returnPrice, String customer,
                                                   String consignee,String mobile,String address,String returnCount, String returnType, String returnState,
                                                   String returnApplyStartDate, String returnApplyEndDate, String schemeId, Integer pageNo, Integer pageSize);

    /**
     * 获取订单状态及其个数
     * @param supplierId
     * @param type
     * @param returnState
     * @return
     */
    long getOrderReturnCountBySupplierIdAndType(String supplierId,String type, String returnState,String startDate,String endDate);

    long getOrderReturnCountByOrderIdAndType(String orderId, String type);


    /**
     * 获取退货单
     * @param orderId
     * @return
     */
    OrderReturn getLastOrderReturnByOrderId(String orderId,String type);

  /**
   * 获取退货单
   * @param orderId 订单 id 必传
   * @param type 类型 必传
   * @return
   */
  List<OrderReturn> getByOrderIdAndType(String orderId,String type);
}
