package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.jpa.dto.ExportFiledBaseDTO;
import com.xhgj.srm.jpa.entity.ExportFiledBase;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-05-24 10:19
 */
public interface ExportFiledDao extends BootBaseDao<ExportFiledBase> {



  List<ExportFiledBaseDTO> getByTypeAndTemplateId(String type,String templateId,String supperId);
}
