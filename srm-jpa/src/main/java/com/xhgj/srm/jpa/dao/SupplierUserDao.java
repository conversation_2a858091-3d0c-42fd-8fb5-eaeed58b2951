package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.jpa.entity.SupplierUser;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;
import org.springframework.data.domain.Page;

import java.util.List;

public interface SupplierUserDao  extends BootBaseDao<SupplierUser> {

    long getSupplierUserCountBySupplier(String id);

    List<SupplierUser> getSupplierUserListBySid(String supplierId);

  List<SupplierUser> getSupplierUserListBySidAsc(String supplierId);


    /**
     * 分页获取供应商账号列表
     * @Author: liuyq
     * @Date: 2021/6/9 10:38
     * @param supplierId
     * @param name
     * @param mobile
     * @param mail
     * @param realName
     * @param pageNo
     * @param pageSize
     * @return org.springframework.data.domain.Page<com.xhgj.srm.jpa.entity.SupplierUser>
     **/
    Page<SupplierUser> getSupplierUserPage(String supplierId, String name, String mobile, String mail, String realName, String pageNo, String pageSize);


    /**
     * 根据姓名获取供应商账号
     * @Author: liuyq
     * @Date: 2021/6/9 10:38
     * @param name
     * @return com.xhgj.srm.jpa.entity.SupplierUser
     **/
    SupplierUser getSupplierUserByName(String name);

    /**
     * 判断供应商账户是否重复
     * @param name
     * @param id
     * @return
     */
    long getSupplierUserCountByName(String name, String id);
    /**
    * 根据真实姓名获取供应商账号
    **/
    SupplierUser getSupplierUserByRealName(String realName);

  /**
   * 获取对应组织内供应商账号的真实姓名
   * @param groupId
   * @return
   */
  List<String> findUserRealNameByGroup(String groupId);
}
