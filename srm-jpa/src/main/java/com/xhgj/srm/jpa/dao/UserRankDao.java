package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.jpa.entity.UserRank;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;

public interface UserRankDao extends BootBaseDao<UserRank> {

    /**
     * 根据用户组织获取用户当前组织排名
     * @param userId
     * @param groupId
     * @return
     */
    Integer getRankByUserAndGroup(String userId,String groupId);

    /**
     * 根据用户组织获取用户当前组织排名对象
     * @param userId
     * @param groupId
     * @return
     */
    UserRank getUserRankByUserAndGroup(String userId,String groupId);


}
