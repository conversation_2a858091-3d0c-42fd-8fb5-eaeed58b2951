package com.xhgj.srm.jpa.dao.impl;

import com.xhgj.srm.jpa.dao.ProductSellableAreaDao;
import com.xhgj.srm.jpa.entity.ProductSellableArea;
import com.xhiot.boot.core.common.util.ObjectUtils;
import com.xhiot.boot.core.common.util.StringUtils;
import com.xhiot.boot.framework.jpa.dao.AbstractBaseDao;
import org.springframework.stereotype.Service;
import java.util.List;

/**
  *@ClassName ProductSellableAreaDaoImpl
  *<AUTHOR>
  *@Date 2023/10/20 8:37
*/
@Service
public class ProductSellableAreaDaoImpl extends AbstractExtDao<ProductSellableArea> implements
    ProductSellableAreaDao {

  @Override
  public List<ProductSellableArea> getAreaListByCodeOrArea(String code, String area) {
    String hql = "from ProductSellableArea psa where psa.productCode = ? ";
    Object[] params = new Object[]{code};
    if(!StringUtils.isNullOrEmpty(area)){
      hql += " and psa.sellArea = ? ";
      params = ObjectUtils.objectAdd(params, area);
    }
    hql += " order by psa.createTime desc";
    return getHqlList(hql, params);
  }
}
