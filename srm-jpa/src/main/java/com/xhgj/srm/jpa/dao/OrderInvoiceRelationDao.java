package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.common.enums.LogicalOperatorsEnums;
import com.xhgj.srm.jpa.dto.OrderInvoiceRelationListQueryDTO;
import com.xhgj.srm.jpa.dto.inputInvoice.InputInvoiceStatistics;
import com.xhgj.srm.jpa.entity.InputInvoiceOrder;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import org.springframework.data.domain.Page;

/**
  *@ClassName OrderInvoiceRelationDao
  *<AUTHOR>
  *@Date 2023/8/14 10:53
*/
public interface OrderInvoiceRelationDao extends BootBaseDao<InputInvoiceOrder> {

  Page<InputInvoiceOrder> getListByParams(String orderNo, String invoiceNo, String supplierName,
      String platform, String invoiceState, String operator, String auditor, Long startExamineTime,
      Long endExamineTime,String orderSource,String orderCodes, String createMan,
      String invoiceVoucherNumber, String accountingYear, String financialVouchers,
      List<String> userNameList,String curOperator,String selectGroup,
      Long firstOpenInvoiceDateStart, Long firstOpenInvoiceDateEnd, Long createTimeStart,
      Long createTimeEnd, String invoiceTypes,String needRedTicket,
      String purchasingOrganizationCode,String purchaseGroupName,
      LogicalOperatorsEnums totalAmountOfTaxDeductionOperators,
      BigDecimal totalAmountOfTaxDeduction, LogicalOperatorsEnums orderAmountOperators,
      BigDecimal orderAmount, String purchaseMan,String source,String PurchaseDept,int pageNo,
      int pageSize);

  /**
   * 分页获取订单发票列表
   * @param queryMap
   * @return
   */
  Page<InputInvoiceOrder> getListByParamsRef(Map<String, Object> queryMap);

  /**
   * 获取订单发票统计
   * @param queryMap
   * @return
   */
  InputInvoiceStatistics getStatistics(Map<String, Object> queryMap);

  /**
   * 分页查询
   * @param orderNo 订单号
   * @param invoiceNo 发票号
   * @param platform 下单平台
   * @param invoiceState 审核状态
   * @param supplierId 供应商id
   * @param includeOffset 是否包含已冲销带的
   * @param pageNo 页数
   * @param pageSize 页尺寸
   * @return 分页结果
   */
  Page<InputInvoiceOrder> findPageByParams(String orderSource, String supplierOrderCode,
      String orderNo, String invoiceNo,
      String platform, String invoiceState, String supplierId,Boolean excludeOffset ,int pageNo,
      int pageSize);

  /**
   * 根据采购订单明细id查询进项票列表
   *
   * @param supplierOrderDetailId 订单明细id
   * @deprecated 有严重的BUG，不应该使用
   */
  @Deprecated
  List<InputInvoiceOrder> getOrderInvoiceRelationList(String supplierOrderDetailId);

  /**
   * 根据采购订单明细id查询进项票列表
   * @param supplierOrderDetailId
   * @return
   */
  List<InputInvoiceOrder> getOrderInvoiceRelationListRef(String supplierOrderDetailId);

  /**
   * 根据发票号查询订单发票关联信息
   * @param invoiceNums
   * @return
   */
  List<InputInvoiceOrder> getOrderInvoiceRelationListByInvoiceNums(List<String> invoiceNums);

  /**
   * 根据采购订单号 获取进项票
   * @param orderCode
   * @return
   */
  List<InputInvoiceOrder> getOrderInvoiceRelationByOrderCode(String orderCode);

  /**
   * 根据采购订单号 获取进项票
   * @param orderCodes
   * @return
   */
  List<InputInvoiceOrder> getOrderInvoiceRelationByOrderCode(List<String> orderCodes);

  /**
   * 根据orderIds查询进项票
   * @param orderIds
   * @return
   */
  List<InputInvoiceOrder> getOrderInvoiceIdByOrderIds(List<String> orderIds);
}
