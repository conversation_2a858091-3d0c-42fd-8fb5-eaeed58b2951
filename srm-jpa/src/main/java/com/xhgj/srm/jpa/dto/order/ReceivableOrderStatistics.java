package com.xhgj.srm.jpa.dto.order;

import lombok.Data;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * 客户回款订单统计
 */
@Data
public class ReceivableOrderStatistics {

  /**
   * 下单金额
   */
  private BigDecimal price;

  /**
   * 获取下单金额,转换2位小数
   */
  public BigDecimal getPrice() {
    if (price == null) {
      return BigDecimal.ZERO;
    }
    return price.setScale(2, RoundingMode.HALF_UP);
  }
}
