package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.jpa.dto.financial.voucher.FinancialVoucherDaoPageParam;
import com.xhgj.srm.jpa.dto.financial.voucher.FinancialVoucherStatistics;
import com.xhgj.srm.jpa.entity.FinancialVoucher;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;
import org.springframework.data.domain.Page;
import java.util.List;

public interface FinancialVoucherDao extends BootBaseDao<FinancialVoucher> {
  Page<FinancialVoucher> getPageRef(FinancialVoucherDaoPageParam param);

  /**
   * 获取财务凭证统计
   * @param daoPageParam
   * @return
   */
  FinancialVoucherStatistics getStatistics2(FinancialVoucherDaoPageParam daoPageParam);

  /**
   * 获取需要同步sap状态的财务凭证
   * @return
   */
  List<FinancialVoucher> getSyncPayStateList();

  /**
   * 获取发票过账凭证列表
   */
  List<FinancialVoucher> getAllPrepaidAmountIsNull();

  /**
   * 获取预付款凭证列表
   */
  List<FinancialVoucher> getAllByPurchaseOrderNo(String code, String voucherType);

  /**
   * 获取关联的凭证(过滤出financialVoucherNo、accountingYear、groupCode、supplierId)
   */
  List<FinancialVoucher> findLinkVouchers(List<FinancialVoucher> vouchers,
      String state, String voucherType, String applyType, List<String> applyStates);
}
