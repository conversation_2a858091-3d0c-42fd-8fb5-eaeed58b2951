package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.jpa.entity.BidProject;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;
import org.springframework.data.domain.Page;
import java.util.List;

/**
 * BidProjectDao
 */
public interface BidProjectDao extends BootBaseDao<BidProject> {

  Page<BidProject> getBidProjectPage(String platformId, Integer pageNo, Integer pageSize);

  List<BidProject> searchProjectNameByProjectCategory(String projectCategory);
}
