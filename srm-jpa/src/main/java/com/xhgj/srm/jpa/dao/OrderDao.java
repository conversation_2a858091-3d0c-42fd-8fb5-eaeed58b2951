package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.common.enums.supplierorder.OrderPageSortType;
import com.xhgj.srm.jpa.dto.landingContract.SupplierId2PlatformCode;
import com.xhgj.srm.jpa.dto.order.InvoiceDTO;
import com.xhgj.srm.jpa.dto.order.InvoiceStatistics;
import com.xhgj.srm.jpa.dto.order.OrderAmountStatistics;
import com.xhgj.srm.jpa.dto.order.PlatformPriceQueryMap;
import com.xhgj.srm.jpa.dto.order.ReceivableOrderStatistics;
import com.xhgj.srm.jpa.entity.Order;
import com.xhgj.srm.jpa.entity.SupplierPerformance;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import com.xhiot.boot.mvc.base.PageResult;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.query.Param;

public interface OrderDao extends BootBaseDao<Order> {

  /**
   * 获取订单状态及其个数
   *
   * @param supplierId
   * @return
   */
  long getOrderCountBySupplierIdAndType(String supplierId, String orderState);

  /**
   * 获取订单状态及其个数
   *
   * @param supplierId
   * @return
   */
  long getOrderCountBySupplierIdAndSignType(String supplierId, String signVoucherState,
      List<String> orderPageState);

  /**
   * 判断是否存在相同订单
   *
   * @param orderNo
   * @param platform
   * @return
   */
  boolean isHaveSameOrder(String orderNo, String platform);

  /**
   * 获取订单分页信息
   *
   * @param supplierId
   * @param orderNo
   * @param customer
   * @param consignee
   * @param mobile
   * @param address
   * @param orderStates
   * @param invoicingState
   * @param startDate
   * @param endDate
   * @param pageNo
   * @param pageSize
   * @param sortType 针对小程序验收单排序使用
   * @return
   */
  Page<Order> getOrderPageBySupplier(
      String supplierId,
      String orderNo,
      String customer,
      String orderState,
      String consignee,
      String mobile,
      String address,
      List<String> orderStates,
      String invoicingState,
      String startDate,
      String endDate,
      String platform,
      String signVoucher,
      List<String> customerReturnList,
      Integer pageNo,
      Integer pageSize,
      String accountStatus,
      String accountOpenInvoiceStatus,
      String excludeOrderState,
      String paymentStatus,
      Boolean salesReturnState,
      String startWriteOffTime,
      String endWriteOffTime,
      String paymentType,
      OrderPageSortType sortType
      );

  /**
   * 获取对应订单
   *
   * @param orderNo
   * @param type
   * @return
   */
  Order getOrderByOrderNoAndType(String orderNo, String type);


  /**
   * 获取订单列表
   *
   * @param supplierId
   * @param orderNo
   * @param customer
   * @param consignee
   * @param mobile
   * @param platform
   * @param startTime
   * @param endTime
   * @return
   */
  List<Order> getDoneOrderListBySupplier(String supplierId, String orderNo, String customer,
      String consignee, String mobile, String platform, String startTime, String endTime);

  /**
   * 获取履约中的订单
   *
   * @return
   */
  List<Order> getDeliveryOrderList();

  /**
   * 获取履约中的订单
   *
   * @return
   */
  List<Order> getAllOrderList();

  /**
   * 获取对应订单 根据订单号和下单平台
   *
   * @param orderNo 订单号
   * @param type 平台编码
   * @return 订单
   */
  Order getOrderByOrderNoAndTypeIncWith(String orderNo, String type, String supplierOrderId);


  /**
   * 获取旧对应订单（包含撤回）
   *
   * @param orderNo
   * @param type
   * @return
   */
  Order getOldOrderByOrderNoAndTypeIncWith(String orderNo, String type);

  /**
   * 分页获取采购对应供应商信息
   *
   * @param userIds                  所属用户群组
   * @param orderNo                  订单号
   * @param customer                 客户单位
   * @param consignee                收货人
   * @param mobile                   联系方式
   * @param orderState               订单状态
   * @param startDate                开始时间
   * @param endDate                  结束时间
   * @param platform                 下单平台
   * @param supplierName             供应商名称
   * @param price                    下单金额
   * @param customerReturnProgress   回款进度
   * @param hasUploadSignVoucher     是否已上传签收凭证
   * @param pageNo                   当前页
   * @param pageSize                 每页展示数量
   * @return
   * @deprecated 请使用 getOrderPageRef
   */
  @Deprecated
  Page<Order> getOrderPageByPurchase(String userIds, String orderNo, String customer,
      String consignee, String mobile, String accepted, String orderState, String startDate,
      String endDate, String platform, String supplierName, String price, String invoicingState,
      String hasUploadSignVoucher, String customerReturnProgress,
      String paymentStatus, Integer pageNo, Integer pageSize,
      List<String> customerReturnProgressList, String accountStatus,
      String accountOpenInvoiceStatus, Boolean salesReturnState, String saleOrderNo,
      String erpOrderNo, Long startFirstShipTime, Long endFirstShipTime,
      String paymentProportionOperators, BigDecimal paymentProportion, Long startArrivalTime,
      Long endArrivalTime,Long startWriteOffTime,Long endWriteOffTime,String paymentTypeName,
      String paymentCondition, Boolean backToBack,Integer accountingPeriod,
      Long startPaymentConditionTime,Long endPaymentConditionTime,Long startPredictPaymentTime,
      Long endPredictPaymentTime,Boolean receivableState,Boolean payableDate, String rate,
      Boolean isUpdateRate, Boolean isCustomerAccept,Long startCustomerInvoiceTime,Long endCustomerInvoiceTime);

  Page<Order> getOrderPageRef(Map<String, Object> queryMap);


  ReceivableOrderStatistics getOrderStatistics(Map<String, Object> queryMap);

  /**
   * 获取对应srm派单订单 （已撤回的订单）
   *
   * @param orderNo
   * @param type
   * @return
   */
  Order getWithdrawOrderBySupplierOrderId(String orderNo, String type, String supplierOrderId);


  /**
   * 获取对应派单订单
   *
   * @param orderNo
   * @param type
   * @return
   */
  List<Order> getOrderListByOrderNoAndType(String orderNo, String type);

  /**
   * 获取对应派单订单
   *
   * @param supplierOrderId 派单id
   * @return
   */
  Order getOrderBySupplierOrderId(String supplierOrderId);


  /**
   * 根据供应商 id 分组获取履约金额（排除取消、退货、撤回状态的订单）
   * @param platform 平台编码，必传
   * @return [[supplierId,SUM(c_sale_price)]]
   */
  @Deprecated
  List<Object[]> getSupplierToTotalPriceListByPlatform(String platform);



  /**
   * 获取供应商订单有效期内的履约金额
   */
  BigDecimal getTotalPriceBySupplierAndTime(String supplierId, String platformCode, Long effectiveStart, Long effectiveEnd);

  /**
   * 根据供应商ids和相关合同时间 获取相关平台的订单金额
   */
  Map<String, BigDecimal> getSupplierToTotalPriceListByPlatformAndTime(PlatformPriceQueryMap queryMap);


  /**
   * 根据状态获取供应商对应订单
   *
   * @param supplierId
   * @param orderPageStates
   * @return
   */
  List<String> getOrderListByOrderState(String supplierId, List<String> orderPageStates);

  /**
   * 根据条件获取采购对应供应商信息
   *
   * @param orderNo      订单号
   * @param customer     客户单位
   * @param consignee    收货人
   * @param mobile       联系方式
   * @param orderState   订单状态
   * @param startDate    开始时间
   * @param endDate      结束时间
   * @param platform     下单平台
   * @param supplierName 供应商名称
   * @param price        下单金额
   * @deprecated 请使用 getOrderPageRef
   */
  @Deprecated
  List<Order> getOrderByQueryParams(
      String userIds,
      String orderNo,
      String customer,
      String consignee,
      String mobile,
      String accepted,
      String orderState,
      String startDate,
      String endDate,
      String platform,
      String supplierName,
      String price,
      String invoicingState,
      Boolean hasUploadSignVoucher,
      String customerReturnProgress,
      Long allShipTimeStart,
      String paymentStatus,
      List<String> customerReturnProgressList,
      String accountStatus,
      String accountOpenInvoiceStatus,
      Boolean salesReturnState,
      String saleOrderNo,
      String erpOrderNo,
      String startCreateTime,
      String endCreateTime,
      String paymentProportionOperators, BigDecimal paymentProportion, Long startArrivalTime,
      Long endArrivalTime,Long startWriteOffTime,Long endWriteOffTime,String paymentType,String paymentCondition, Boolean backToBack,Integer accountingPeriod,
      Long startPaymentConditionTime,Long endPaymentConditionTime,Long startPredictPaymentTime,
      Long endPredictPaymentTime,Boolean receivableState,Boolean payableDate, String rate,
      Boolean isUpdateRate, Boolean isCustomerAccept,Long startCustomerInvoiceTime,Long endCustomerInvoiceTime);



  /**
   * 获取订单发票详情 已完成和待完成的
   */

  Page<Object[]> getOrderInvoiceAndStatePage(String orderNo, String orderState, String platform,
      String price,
      String customer, String createTime, String beginTime, String endTime,
      List<String> invoicingState, String type, String title,
      String excludeInvoicingState, String supplierId, String enterPriseName,
      String dispatchStartTime,String dispatchEndTime,
      Integer pageNo, Integer pageSize);

  /**
   * 根据供应商 id 获取开票中的数量
   *
   * @param supplierId 供应商 id
   * @return
   */
  long countOrderInvoiceBySupplierId(String invoicingState, String supplierId);

  /**
   * @param orderNo        订单编号
   * @param type           订单类型
   * @param enterpriseName 公司名称
   * @return
   */
  List<Order> getOrderListByOrderNoAndTypeAndEnterpriseName(String orderNo, String type,
      String enterpriseName);

  /**
   * 根据条件查询订单
   *
   * @param orderNo 外部订单号
   * @param customer 客户单位
   * @param platform 平台
   * @param orderStatus 订单状态
   * @param accountStatus 对账状态
   * @param supplierId 供应商 id
   */
  Page<Order> getOrderPage(
      String orderNo,
      String customer,
      String platform,
      String orderStatus,
      String accountStatus,
      String supplierId,
      String paymentStatus,
      Long startOrderTime,
      Long endOrderTime,
      Boolean prohibitionPaymentState,
      Pageable pageable);

  /**
   * 根据条件查询订单
   *
   * @param orderNo 外部订单号
   * @param customer 客户单位
   * @param platform 平台
   * @param orderStatus 订单状态
   * @param accountStatus 对账状态
   * @param supplierId 供应商 id
   */
  Page<Order> getMobileOrderPage(
      String orderNo,
      String customer,
      String platform,
      String orderStatus,
      String accountStatus,
      String supplierId,
      String paymentStatus,
      Long startOrderTime,
      Long endOrderTime,
      String orderNoOrCustomer,
      Boolean prohibitionPaymentState,
      Pageable pageable);

  /**
   * 通过订单 id 集合获取订单 集合
   *
   * @param orderIdList 订单 id 集合
   * @return
   */
  List<Order> getByOrderIdList(List<String> orderIdList);

  /**
   * 通过订单号集合 获取订单集合
   *
   * @param orderNoList
   * @return
   */
  List<Order> getByOrderNoList(List<String> orderNoList);

  /**
   * 根据客户订单号获取
   *
   * @param orderNo
   * @param supplierId
   * @return
   */
  List<Order> getOrderByOrderNo(String orderNo, String supplierId);

  /**
   * 开票申请列表 - 分页
   * @param orderNo 客户订单号
   * @param invoiceApplicationNum 开票申请单号
   * @param orderState 订单状态
   * @param platform  下单平台
   * @param price 下单金额
   * @param customer 客户名称
   * @param createTime 创建时间
   * @param beginTime 开始时间
   * @param endTime 结束时间
   * @param invoicingState 开票状态
   * @param type 发票类型
   * @param title 发票抬头
   * @param excludeInvoicingState 开票状态
   * @param supplierId 供应商id
   * @param pageNo
   * @param pageSize
   * @return
   */

  Page<Object[]> getOrderInvoicePageNew(String orderNo,String invoiceApplicationNum, String orderState, String platform,
      String price,
      String customer, String createTime, String beginTime, String endTime,
      List<String> invoicingState, String type, String title,
      String excludeInvoicingState,String invoiceNum, String supplierId,
      Integer pageNo, Integer pageSize);

  /**
   * 开票申请列表 - 分页
   * @param queryMap
   * @return
   */
  PageResult<InvoiceDTO> getOrderInvoicePageNew(Map<String,Object> queryMap);

  /**
   * 开票申请 统计
   * @param queryMap
   * @return
   */
  List<InvoiceDTO> getOrderInvoicePageNewStatistics(Map<String, Object> queryMap);

  /**
   * 开票申请 统计
   * @param queryMap
   * @return
   */
  InvoiceStatistics getOrderInvoicePageNewStatistics2(Map<String, Object> queryMap);

  /**
   * 根据供应商id和平台编码查询
   * @param supplierId
   * @param platformCode
   * @return
   */
  List<Order> getOrderBySupplierIdAndPlatformCode(String supplierId, String platformCode);

  /**
   *
   * @param orderNo
   * @param invoiceApplicationNum
   * @param orderState
   * @param platform
   * @param price
   * @param customer
   * @param createTime
   * @param beginTime
   * @param endTime
   * @param invoicingState
   * @param type
   * @param title
   * @return
   */
  List<String> getOrderIds(String orderNo, String invoiceApplicationNum, String orderState, String platform,
      String price,
      String customer, String createTime, String beginTime, String endTime,
      List<String> invoicingState, String type, String title);

  /**
   * 根据供应商id和平台编码查询 - 分页
   */
  Page<Order> getOrderBySupplierIdAndPlatformCodePage(List<SupplierPerformance> supplierPerformances, int pageNo, int pageSize);

  /**
   * 获取落地商合同关联订单金额之和
   * @param supplierId
   * @param platformCode
   * @return
   */
  @Deprecated
  Object getContractOrderAmount(String supplierId, String platformCode);

  /**
   * 获取落地商合同关联订单金额之和
   * @param supplierPerformances
   * @return
   */
  Object getContractOrderAmountBatch(List<SupplierPerformance> supplierPerformances);

  List<String> getContractOrderIds(String supplierId, String platformCode);

  /**
   * 获取落地商合同关联订单id
   * @param supplierPerformances
   * @return
   */
  List<String> getContractOrderIdsBatch(List<SupplierPerformance> supplierPerformances);

  /**
   * 根据对账单id获取订单下单平台
   * @对账单号 accountNo
   * @return
   */
  List<String> getPlatformByAccountId(String accountId);


  /**
   * 根据条件获取订单信息 @Author: liuyq @Date: 2023/6/1 15:32
   *
   * @param excludeReturnProgressList 排除的回款状态
   * @param excludeTypeList 排除的下单平台
   * @return java.util.List<com.xhgj.srm.jpa.entity.Order>
   */
  List<Order> getAllOrderByReturnProgressExcludeType(
    List<String> excludeReturnProgressList, List<String> excludeTypeList);

  /**
   * 根据对账单id查询
   * @param account
   * @return
   */
  List<Order> getOrderByAccountNo(String account);

  /**
   * 查询订单总计
   * @param supplierId
   * @param accountStatus
   * @return
   */
  long getOrderTotal(String supplierId, String accountStatus, String paymentStatus, Boolean prohibitionPaymentState);

  /**
   * 根据落地商id和付款状态查询订单
   * @param supplierId 供应商id
   * @param paymentStatus 付款状态
   * @return 订单集合
   */
  List<Order> getOrderBySupplierAndPaymentStatus(String supplierId, String paymentStatus);
  /**
   * 获取未开票并且erp采购单号 不为null的订单列表
   * @return
   */
  List<Order>  getOrderListByInvoicingState();

  /**
   * 根据对账单id查询关联的订单集合
   */
  List<Order> getOrderByAccountId(String accountId);

  /**
   * 根据落地商id和付款状态查询订单
   * @param supplierId 供应商id
   * @param customerReturnProgress 客户回款进度
   * @param customerReturnProgressStartTime 客户回款开始时间，>=开始时间
   * @param customerReturnProgressEndTime 客户回款结束时间， <=结束时间
   * @return 订单集合
   */
  List<Order> getOrderBySupplierAndCustomerReturnProgress(String supplierId,
      String customerReturnProgress, Long customerReturnProgressStartTime,
      Long customerReturnProgressEndTime);

  /**
   * 条件查询订单，用户可开票订单列表
   *
   * @param orderNo
   * @param customer
   * @param platform
   * @param supplierId
   * @param pageable
   * @return
   */
  Page<Order> getCanInvoiceOrderPage(
      String orderNo,
      String customer,
      String platform,
      String supplierId,
      String consignee,
      String supplierInvoiceState,
      List<String> customerPayback,
      String signVoucher,
      Long orderTimeStart,Long orderTimeEnd,Long dispatchTimeStart,Long dispatchTimeEnd,
      Pageable pageable);

  /**
   * 条件查询订单，用户可开票订单列表
   *
   * @param orderNo
   * @param customer
   * @param platform
   * @param supplierId
   * @param pageable
   * @return
   */
  Page<Order> getMobileCanInvoiceOrderPage(
      String orderNo,
      String customer,
      String platform,
      String supplierId,
      String consignee,
      String supplierInvoiceState,
      List<String> customerPayback,
      String signVoucher,
      Pageable pageable,
      String customerInfo,
      List<String> additionalQueryOrderIdList,
      List<String> orderIdList);

  /**
   * 获取该时间之前订单数据
   *
   * @param time
   * @return
   */
  List<Order> getListByCreateTimeBefore(Long time);

  /**
   * 获取该时间之后订单数据
   *
   * @param time
   * @return
   */
  List<Order> getListByCreateTimeAfter(Long time);

  /**
   * 可开票订单总计
   * @param supplierId
   * @param supplierInvoiceState
   * @return
   */
  long getCanInvoiceOrderTotal(String supplierId, String supplierInvoiceState);

  /**
   * 获取客户回款详情
   * @param supplierId
   * @param platformCode
   * @param customer
   * @return
   */
  List<Order> getCustomerPaybackList(String supplierId, String platformCode, String customer);

  /**
   * 获取该落地商该平台下该客户下的所有订单
   * @return
   */
  List<Order> getOrderByPlatformCodeAndCustomerAndSupplierId(String supplierId, String platformCode, String customer);

  /**
   * 查询对应供应商在对应的时间端内的订单，包含开始时间，不包含结束时间。
   * @param supplierId 供应商id
   * @param startTime 开始时间
   * @param endTime 结束时间
   * @param platformCode 下单平台编号
   */
  List<Order> getBySupplierIdAndPlatformCodeAndOrderTime(String supplierId, String platformCode,
      Long startTime, Long endTime);

  List<Order> getByOrderNoAndSupplierName(String orderNo, String supplierName);
  /**
   * 获取所有付款条件为空的履约订单
   */
  List<String> getAllOrderListByPaymentCondition();

  /**
   * 根据订单号分页获取订单 <AUTHOR> @Date: 2024年6月7日 10:14:16
   *
   * @param orderNo 订单号
   * @param pageNo 页数
   * @param pageSize 条数
   * @return org.springframework.data.domain.Page<java.lang.Object[]>
   */
  Page<Object[]> getByOrderNo(
      String supplierId,
      String orderNo,
      String excludeOrderState,
      List<String> orderPageStates,
      List<String> supplierOrderPageStates,
      Integer pageNo,
      Integer pageSize);

  /**
   * 获取发货订单分页信息 <AUTHOR> @Date: 2024年6月17日 09:00:50
   *
   * @param supplierId 供应商id
   * @param orderNo 订单号
   * @param orderState 订单状态
   * @param consignee 签收人
   * @param mobile 号码
   * @param address 地址
   * @param orderStates 订单状态集合
   * @param startDate 派单时间
   * @param endDate 派单时间
   * @param platform 下单平台
   * @param pageNo 页数
   * @param pageSize 条数
   * @return org.springframework.data.domain.Page<com.xhgj.srm.jpa.entity.Order>
   */
  Page<Order> getDeliveryOrderPage(
      String supplierId,
      String orderNo,
      String orderState,
      String consignee,
      String mobile,
      String address,
      List<String> orderStates,
      String invoicingState,
      String startDate,
      String endDate,
      String platform,
      Integer pageNo,
      Integer pageSize);

  /**
   * 获取订单状态及其个数
   *
   * @param supplierId 供应商id
   * @return
   */
  long getOrderCountBySupplierIdAndType(String supplierId, List<String> orderStates);

  /**
   * 根据落地商订单id列表查询订单
   * @param ids 订单id列表
   * @return
   */
  List<Order> getOrderByIds(List<String> ids);

  /**
   * 根据供应商与平台编码map查询
   * @param supplierId2PlatformCode
   * @return supplierId与PlatformCode拼接code 与 amount map关系
   */
  Map<String, BigDecimal> getContractOrderAmount(List<SupplierId2PlatformCode> supplierId2PlatformCode);

  /**
   * 根据订单号更新数据
   * @param order
   * @param id
   * @param updateTime
   * @return
   */
  int optimisticLockUpdateOrder(Order order,  String id, Long updateTime);

  /**
   * 乐观锁更新数据
   */
  boolean optimisticLockUpdateOrder(Order order, Consumer<Order> updateLogic);

  OrderAmountStatistics getOrderAmountStatistics(Map<String, Object> queryMap);

  List<String> getAllOrderListHandleOrderStatus();

  List<String> getAllOrderListHandle();

  /**
   * 查询合作金额
   * @param supplierId
   * @param groupCode
   * @return
   */
  BigDecimal getTotalAmount(String supplierId, String groupCode);

  /**
   * 查询合作金额
   * @param supplierIds
   * @param groupCode
   * @return
   */
  Map<String,BigDecimal> getTotalAmountBatch(List<String> supplierIds, String groupCode);
}


