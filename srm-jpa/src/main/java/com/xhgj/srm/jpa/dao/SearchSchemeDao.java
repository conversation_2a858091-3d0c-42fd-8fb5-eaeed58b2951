package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.jpa.entity.SearchScheme;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;

import java.util.List;

public interface SearchSchemeDao extends BootBaseDao<SearchScheme> {

        List<SearchScheme> getSearchSchemeListByUser(String userId, String type);

        List<SearchScheme> getSearchSchemeListByName(String userId, String type,String name,String id);

        SearchScheme getDefaultSearchScheme(String userId, String type);

}
