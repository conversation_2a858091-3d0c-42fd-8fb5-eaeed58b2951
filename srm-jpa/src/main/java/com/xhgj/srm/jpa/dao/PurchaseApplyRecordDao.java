package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.jpa.entity.PurchaseApplyForOrder;
import com.xhgj.srm.jpa.entity.PurchaseApplyRecord;
import com.xhgj.srm.jpa.entity.SupplierChangeRecord;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;
import org.springframework.data.domain.Page;

/**
 *PurchaseApplyRecordDao
 */
public interface PurchaseApplyRecordDao extends BootBaseDao<PurchaseApplyRecord> {

  Page<PurchaseApplyRecord> getPurchaseApplyRecordDetailList(String purchaseApplyId, Integer pageNo, Integer pageSize);
}
