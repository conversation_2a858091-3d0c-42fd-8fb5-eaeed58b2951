package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.jpa.dto.account.AccountStatistics;
import com.xhgj.srm.jpa.entity.OrderAccount;
import com.xhgj.srm.jpa.param.OrderAccountParam;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;
import java.util.List;
import java.util.Map;
import org.springframework.data.domain.Page;

public interface OrderAccountDao extends BootBaseDao<OrderAccount> {

    /**
     * 分页获取订单对账单
     * @param supplierId
     * @param accountNo
     * @param accountPrice
     * @param accountStartTime
     * @param accountEndTime
     * @param assessStartTime
     * @param assessEndTime
     * @param accountState
     * @param accountOpenInvoiceStatus
     * @param supplierName
     * @param orderNo
     * @param pageNo
     * @param pageSize
     * @return
     */
    Page<String> getOrderAccountPageBySupplier(String supplierId, String accountNo, String accountPrice,
        String accountStartTime, String accountEndTime, String assessStartTime, String assessEndTime,
        List<String> accountState, List<String> accountOpenInvoiceStatus,
        String supplierName, String orderNo, Integer pageNo, Integer pageSize
    );

    /**
     * 获取当天对账单数量
     *
     * @param startDate 对账单创建时间--起始
     * @param endDate   对账单创建时间--终止
     * @return
     */
    long getOrderAccountCountByCreateTimeBetween(String startDate, String endDate);

    /**
     * 获取正常的对账单
     *
     * @return
     */
    List<String> getAllOrderAccountList();

    /**
     * 分页获取采购对账单
     *
     * @param userIds 所属用户群组ids
     * @param supplierName 供应商名称
     * @param accountNo 对账单号
     * @param accountStartTime 对账时间(开始)
     * @param accountEndTime 对账时间(截止)
     * @param returnAmount 回款金额
     * @param accountPrice 对账金额
     * @param accountState 对账单状态
     * @param invoicingState 发票附件
     * @param pageNo 当前页
     * @param pageSize 每页展示数量
     * @return
     */
    Page<String> getOrderAccountPageByPurchase(String userIds, String supplierName, String accountNo, String returnAmount, String accountPrice, String accountStartTime,
                                               String accountEndTime, String accountState, String invoicingState,
                                               Integer pageNo, Integer pageSize);

    /**
     * 分页获取对账单
      * @param accountNo 对账单号
     * @param createSupplier 提交人
     * @param accountStatus 对账单状态
     * @param accountOpenInvoiceStatus 对账单开票状态
     * @param startCommitTime 提交时间范围 开始
     * @param endCommitTime 提交时间范围 结束
     * @param startAssessTime 审核时间范围 开始
     * @param endAssessTime 审核时间范围 结束
     * @param pageNo 页码
     * @param pageSize 分页尺寸
     * @return
     */
     Page<OrderAccount> findAccountPage(
         String accountNo, String createSupplier,
         String accountStatus, String accountOpenInvoiceStatus,
         Long startCommitTime, Long endCommitTime,
         Long startAssessTime, Long endAssessTime,int pageNo,int pageSize);

  /**
   * 分页获取对账单
   * @param accountNo 对账单号
   * @param createSupplier 提交人
   * @param accountStatus 对账单状态
   * @param accountOpenInvoiceStatus 对账单开票状态
   * @param startCommitTime 提交时间范围 开始
   * @param endCommitTime 提交时间范围 结束
   * @param startAssessTime 审核时间范围 开始
   * @param endAssessTime 审核时间范围 结束
   * @param pageNo 页码
   * @param pageSize 分页尺寸
   * @return
   */
  Page<String> findAccountPage(
      String accountNo, String createSupplier,
      String accountStatus, String accountOpenInvoiceStatus,List<String> platforms,
      Long startCommitTime, Long endCommitTime,
      Long startAssessTime, Long endAssessTime,int pageNo,int pageSize);

  /**
   * 分页获取对账单
   */
  Page<OrderAccount> findAccountPageRef(Map<String,Object> queryMap);

  /**
   * 获取对账单统计信息
   */
  List<OrderAccount> getAccountStatistics(Map<String, Object> queryMap);

  /**
   * 获取对账单统计信息
   */
  AccountStatistics getAccountStatistics2(Map<String, Object> queryMap);

  /**
   * 导出对账单查询
   * @return
   */
  List<String> findAccount(OrderAccountParam param);

  /**
   * 查询对账单总计
   * @param supplierId
   * @param orderAccountStatus
   * @param accountOpenInvoiceStatus
   */
	long getOrderAccountTotal(String supplierId, String orderAccountStatus,
      String accountOpenInvoiceStatus);

  List<OrderAccount> getListByOpenInvoiceStatus(String orderAccountOpenNotInvoiced);

  OrderAccount getAccountByAccountNo(String accountNo);
}
