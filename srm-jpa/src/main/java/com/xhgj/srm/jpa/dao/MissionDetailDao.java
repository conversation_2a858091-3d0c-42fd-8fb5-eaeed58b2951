package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.jpa.entity.MissionDetail;
import org.springframework.data.domain.Page;

import java.util.List;

public interface MissionDetailDao {

  /**
   * 分页获取任务详情
   * @param id 任务id
   * @param curpage 页数
   * @param pagesize 显示条数
   * @return
   */
    Page<MissionDetail> getMissionDetailPage(String id, Integer curpage, Integer pagesize);

    /**
     * 获取成功任务详情列表
     * @param id 任务id
     * @return
     */
    List<MissionDetail> getSuccessMissionDetailList(String id);

    /**
     * 获取成功任务的详情数量
     * @param id 任务id
     * @return
     */
    long getSuccessMissionDetailCount(String id);

    /**
     * 获取失败任务的详情列表
     * @param id 任务id
     * @return
     */
    List<MissionDetail> getFailMissionDetailList(String id);

    /**
     * 通过任务 id 获得任务成功数量
     * @param missionId 任务 id 必传
     * @return
     */
    long countSuccessMissionDetail(String missionId);

    /**
     * 通过传入的任务 id 获取任务 id 的数量
     * @param missIdList 任务 id 集合 必传
     */
    List<Object[]> getSuccessMissionDetailLists(List<String> missIdList);
}
