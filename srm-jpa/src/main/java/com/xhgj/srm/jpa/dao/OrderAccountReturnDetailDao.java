package com.xhgj.srm.jpa.dao;


import com.xhgj.srm.jpa.entity.OrderAccountReturnDetail;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;
import org.springframework.data.domain.Page;


import java.math.BigDecimal;
import java.util.List;

public interface OrderAccountReturnDetailDao extends BootBaseDao<OrderAccountReturnDetail> {
    /**
     * 获取回款单列表
     * @param accountId
     * @return
     */
    List<OrderAccountReturnDetail>  getOrderAccountReturnDetailByAccount(String accountId);


    /**
     * 分页获取回款单
     * @param accountId
     * @return
     */
    Page<OrderAccountReturnDetail>  getOrderAccountReturnDetailPageByAccount(String accountId,int pageNo,int pageSize);


    /**
     * 获取已回款金额
     * @param accountId
     * @return
     */
    BigDecimal getOrderAccountReturnPriceByAccount(String accountId);

    /**
     * 根据oms回款单id获取回款单
     */
    OrderAccountReturnDetail getOrderAccountReturnByOmsId(String returnId);

    /**
     * 获取回款单数量
     * @param accountId
     * @return
     */
    long getOrderAccountReturnCountByAccount(String accountId);


}
