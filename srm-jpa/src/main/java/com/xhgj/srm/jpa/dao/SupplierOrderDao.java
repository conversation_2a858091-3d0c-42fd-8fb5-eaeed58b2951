package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.common.enums.LogicalOperatorsEnums;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormStatus;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormType;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderState;
import com.xhgj.srm.jpa.dto.OrderMateDTO;
import com.xhgj.srm.jpa.dto.purchase.order.PurchaseOrderStatistics;
import com.xhgj.srm.jpa.entity.SupplierOrder;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * <AUTHOR>
 * @since 2022/11/29 14:47
 */
public interface SupplierOrderDao extends BootBaseDao<SupplierOrder> {

  /**
   * 分页查询供应商订单
   *
   * @param code 订单号
   * @param orderState 订单状态
   * @param startCreateTime 创建时间 开始
   * @param endCreateTime 创建时间 结束
   * @param supplierName 供应商名称
   * @param directShipment 是否厂家直发
   * @param confirmState 是否有待确认
   * @param cancelState 是否有取消
   * @param returnState 是否有退货
   * @param shipWaitStock 是否有发货待入库
   * @param purchaseGroupName 采购组织名称
   * @param receiveMan 收件人
   * @param toPageable 分页参数 必传
   */
  Page<SupplierOrder> findSupplierOrderPage(
      String purchaseId,
      List<String> userNameList,
      String code,
      SupplierOrderState orderState,
      Long startCreateTime,
      Long endCreateTime,
      String supplierName,
      Boolean directShipment,
      Boolean confirmState,
      Boolean cancelState,
      Boolean returnState,
      Boolean refuseState,
      Boolean shipWaitStock,
      String purchaseGroupName,
      String receiveMan,
      String supplierOpenInvoiceState,
      SupplierOrderFormType supplierOrderFormType,
      SupplierOrderFormStatus supplierOrderFormStatus,
      Pageable toPageable);

  /**
   * 查询供应商订单
   * @param purchaseId 采购人 id
   * @param code 订单号
   * @param orderState 订单状态
   * @param startCreateTime 创建时间 开始
   * @param endCreateTime 创建时间 结束
   * @param supplierName 供应商名称
   * @param directShipment 是否厂家直发
   * @param confirmState 是否有待确认
   * @param cancelState 是否有取消
   * @param returnState 是否有退货
   * @param shipWaitStock 是否有发货待入库
   * @param purchaseGroupName 采购组织名称
   * @param receiveMan 收件人
   */
  List<String> findIdList(
      String purchaseId,
      String code,
      SupplierOrderState orderState,
      Long startCreateTime,
      Long endCreateTime,
      String supplierName,
      Boolean directShipment,
      Boolean confirmState,
      Boolean cancelState,
      Boolean returnState,
      Boolean refuseState,
      Boolean shipWaitStock,
      String purchaseGroupName,
      String receiveMan,
      SupplierOrderFormType supplierOrderFormType,
      SupplierOrderFormStatus supplierOrderFormStatus,
      String supplierId);
  /**
   * (前台)分页获取供应商订单 @Author: liuyq @Date: 2022/11/29 13:39
   *
   * @param supplierId 供应商id
   * @param code 采购订单号
   * @param orderState 订单状态
   * @param directShipment 是否厂直发
   * @param confirmState 是否确认
   * @param cancelState 是否取消
   * @param returnState 是否退货
   * @param groupName 采购组织
   * @param receiveMan 收件人
   * @param startTime 开始时间
   * @param endTime 结束时间
   * @return com.xhiot.boot.mvc.base.PageResult<com.xhgj.srm.jpa.entity.SupplierOrder>
   */
  Page<SupplierOrder> getSupplierOrderPage(
      String supplierId,
      String code,
      String orderState,
      Boolean directShipment,
      Boolean confirmState,
      Boolean cancelState,
      Boolean returnState,
      Boolean refuseState,
      String groupName,
      String receiveMan,
      String supplierOpenInvoiceState,
      Long startTime,
      Long endTime,
      int pageNo,
      int pageSize);

  /**
   * a Author: liuyq @Date: 2022/12/8 8:58
   *
   * @param supplierId 供应商id，必填
   * @param orderState 订单状态，必填
   * @param orderConfirmState 是否有待确认
   * @param orderCancelState 是否有取消
   * @param orderReturnState 是否有退货
   * @return long
   */
  long getOrderCountByOrderState(
      String supplierId,
      String orderState,
      Boolean orderConfirmState,
      Boolean orderCancelState,
      Boolean orderReturnState);


  /**
   * 分页获取供应商订单 @Author: liuyq @Date: 2022/11/29 13:39
   * 用于导出，过滤掉自采
   *
   * @param supplierId 供应商id
   * @param code 采购订单号
   * @param orderState 订单状态
   * @param directShipment 是否厂直发
   * @param confirmState 是否确认
   * @param cancelState 是否取消
   * @param returnState 是否退货
   * @param groupName 采购组织
   * @param receiveMan 收件人
   * @param startTime 开始时间
   * @param endTime 结束时间
   * @return com.xhiot.boot.mvc.base.PageResult<com.xhgj.srm.jpa.entity.SupplierOrder>
   */
  List<String> getSupplierOrderIdList(
      String supplierId,
      String code,
      String orderState,
      Boolean directShipment,
      Boolean confirmState,
      Boolean cancelState,
      Boolean returnState,
      Boolean refuseState,
      String groupName,
      String receiveMan,
      long startTime,
      long endTime,
      Boolean excludeSelfState
  );

  /**
   * 根据订单状态和采购员 id 获取对应状态的订单数量
   *
   * @param orderState 订单状态
   * @param purchaseId 采购员 id
   */
  long countAllByOrderStateAndPurchaseId(
      String orderState, String purchaseId, String createMan, List<String> supplierOrderStateList);

  /**
   * 过滤掉结算金额和结算件数都为0的订单
   * @param code 采购订单号
   * @param orderState 订单状态
   * @param receiveMan 收件人（模糊）
   * @param supplierId 供应商id
   */
  /**
   * @param code 采购订单号
   * @param orderState 订单状态
   * @param receiveMan 收件人（模糊）
   * @param supplierId 供应商id
   */
  Page<SupplierOrder> getCanInvoiceOrderPage(String code, String orderState,
   String supplierOpenInvoiceState, String receiveMan, String supplierId,Boolean scp,Boolean selfState, int pageNo,
      int pageSize);

  /**
   * 过滤掉结算金额和结算件数都为0的订单
   * @param code
   * @param orderState
   * @param supplierOpenInvoiceState
   * @param receiveMan
   * @param supplierId
   * @param supplierInfo
   * @param scp
   * @param pageNo
   * @param pageSize
   * @param additionalQueryOrderIdList
   * @param orderIdList
   * @return
   */
  Page<SupplierOrder> getMobileCanInvoiceOrderPage(
      String code,
      String orderState,
      String supplierOpenInvoiceState,
      String receiveMan,
      String supplierId,
      String supplierInfo,
      Boolean scp,
      Boolean selfState,
      int pageNo,
      int pageSize,
      List<String> additionalQueryOrderIdList,
      List<String> orderIdList);

  /**
   * 获取未完成的订单
   *
   * @return
   */
  List<SupplierOrder> getUnfinishOrder();

  /**
   * 分页查询采购订单
   *
   * @param code 订单号
   * @param orderState 订单状态
   * @param startCreateTime 创建时间 开始
   * @param endCreateTime 创建时间 结束
   * @param supplierName 供应商名称
   * @param directShipment 是否厂家直发
   * @param confirmState 是否有待确认
   * @param cancelState 是否有取消
   * @param returnState 是否有退货
   * @param freeState 是否有免费订单
   * @param selfState 是否有自采订单
   * @param shipWaitStock 是否有发货待入库
   * @param purchaseGroupName 采购组织名称
   * @param receiveMan 收件人
   * @param toPageable 分页参数 必传
   * @deprecated 请使用 {@link #findPurchaseOrderPageRef(Map)}
   */
  @Deprecated
  Page<SupplierOrder> findPurchaseOrderPage(
      String purchaseId,
      String createMan,
      String code,
      SupplierOrderState orderState,
      Long startCreateTime,
      Long endCreateTime,
      String supplierName,
      Boolean directShipment,
      Boolean confirmState,
      Boolean cancelState,
      Boolean returnState,
      Boolean refuseState,
      Boolean freeState,
      Boolean selfState,
      Boolean shipWaitStock,
      Boolean pending,
      Boolean staging,
      Boolean unaudited,
      Boolean reject,
      String purchaseDept,
      String purchaseMan,
      String purchaseGroupName,
      String receiveMan,
      String supplierOpenInvoiceState,
      SupplierOrderFormType supplierOrderFormType,
      SupplierOrderFormStatus supplierOrderFormStatus,
      String salesOrderNo,
      String largeTicketProjectNumbers,
      String largeTicketProjectName,
      List<String> userNameList,
      boolean isAdmin,
      String creater,
      String updateMan,
      Long startUpdateTime,
      Long endUpdateTime,
      Long startAuditTime,
      Long endAuditTime,
      LogicalOperatorsEnums numOperator,
      BigDecimal num,
      LogicalOperatorsEnums priceOperator,
      BigDecimal price, Boolean scp,String allScp,String orderType,
      Boolean counteractState,
      Boolean loss,
      Pageable toPageable);

  /**
   * 分页查询采购订单 重构
   * @param queryMap
   * @return
   */
  Page<SupplierOrder> findPurchaseOrderPageRef(Map<String, Object> queryMap);

  /**
   * 查询采购订单统计
   * @param queryMap
   * @return
   */
  List<SupplierOrder> findPurchaseOrderStatistics(Map<String, Object> queryMap);

  /**
   * 查询采购订单统计
   * @param queryMap
   * @return
   */
  PurchaseOrderStatistics findPurchaseOrderStatistics2(Map<String, Object> queryMap);

  long findPurchaseOrderCount(
      String purchaseId,
      String createMan,
      String code,
      SupplierOrderState orderState,
      Long startCreateTime,
      Long endCreateTime,
      String supplierName,
      Boolean directShipment,
      Boolean confirmState,
      Boolean cancelState,
      Boolean returnState,
      Boolean refuseState,
      Boolean freeState,
      Boolean selfState,
      Boolean shipWaitStock,
      Boolean pending,
      Boolean staging,
      Boolean unaudited,
      Boolean reject,
      String purchaseDept,
      String purchaseMan,
      String purchaseGroupName,
      String receiveMan,
      String supplierOpenInvoiceState,
      SupplierOrderFormType supplierOrderFormType,
      SupplierOrderFormStatus supplierOrderFormStatus,
      String salesOrderNo,
      String largeTicketProjectNumbers,
      String largeTicketProjectName,
      List<String> userNameList);

  /**
   * 根据金额和数量获取订单
   * @param allValoremTax
   * @param supplierId
   * @param orderIds
   * @return
   */
  List<SupplierOrder> getListByFinalPriceAndNum(String userGroup, String allValoremTax,
      String supplierId,
      List<String> orderIds, Boolean historyOrder);

  /**
   * 获取匹配的入库单
   * @param allValoremTax
   * @param supplierId
   * @return
   */
  List<OrderMateDTO> getListByTypeAndPrice(String userGroup, String allValoremTax,
      String supplierId, Boolean historyOrder);

  Boolean getPrePay(String code);

  Map<String, Boolean> getPrePayMap(List<String> codes);

  long getSupplierOrderCount(String supplierId, String supplierOrderCode, String supplierOrderState, Boolean supplierDirectShipment, Boolean confirmState, Boolean cancelState, Boolean returnState,
      Boolean refuseState, String supplierOrderGroupName, String supplierOrderReceiveMan, String supplierOpenInvoiceState, Long supplierOrderStartTimeLong, Long supplierOrderEndTimeLong);


  /**
   * 根据表头筛选采购订单数据
   * @param queryMap
   * @return
   */
  List<Object> getOrderListByTableHeaderRef(Map<String, Object> queryMap);


  /**
   * (小程序)供应商订单发货列表 @Author: liuyq @Date:  2024年6月17日 14:02:30
   * <p>11919 供应商列表不应展示出已拒绝的订单</p>
   * <p>11917 发货进度已全部发货，不应展示在供应商列表里</p>
   * @param supplierId 供应商id
   * @param code 采购订单号
   * @param orderState 订单状态
   * @param confirmState 是否确认
   * @param groupName 采购组织
   * @param receiveMan 收件人
   * @param startTime 开始时间
   * @param endTime 结束时间
   * @return com.xhiot.boot.mvc.base.PageResult<com.xhgj.srm.jpa.entity.SupplierOrder>
   */
  Page<SupplierOrder> getSupplierOrderDeliveryPage(
      String supplierId,
      String code,
      String orderState,
      Boolean confirmState,
      String groupName,
      String receiveMan,
      List<String> shipCodeList,
      Long startTime,
      Long endTime,
      int pageNo,
      int pageSize);

  /**
   * 获取待确认数量 <AUTHOR> @Date: 2024年6月17日 14:33:13
   *
   * @param supplierId 供应商id
   * @return long
   */
  long getWaitOrderCount(String supplierId);

  /**
   * 获取供应商订单数量 <AUTHOR> @Date: 2024年6月26日 16:50:10 *
   *
   * <p>11919 供应商列表不应展示出已拒绝的订单 *
   *
   * <p>11917 发货进度已全部发货，不应展示在供应商列表里
   *
   * @param supplierId 供应商id
   * @return long
   */
  long getSupplierOrderDeliveryCount(
      String supplierId,
      String supplierOrderCode,
      List<String> supplierOrderState,
      Boolean supplierDirectShipment,
      Boolean confirmState,
      Boolean cancelState,
      Boolean returnState,
      Boolean refuseState,
      String supplierOrderGroupName,
      String supplierOrderReceiveMan,
      String supplierOpenInvoiceState,
      List<String> shipCodeList,
      Long supplierOrderStartTimeLong,
      Long supplierOrderEndTimeLong);

  /**
   * 查询合作金额
   * @param supplierId 供应商id
   * @param groupCode 采购组织
   * @param direct 是否仅查询定向合作
   * @param startTime 开始时间
   * @param endTime 结束时间
   */
  BigDecimal getTotalAmount(String supplierId, String groupCode, boolean direct, Long startTime, Long endTime);

  /**
   * 查询合作金额
   * @param supplierIds 供应商id
   * @param groupCode 采购组织
   * @param direct 是否仅查询定向合作
   * @param startTime 开始时间
   * @param endTime 结束时间
   * @return
   */
  Map<String, BigDecimal> getTotalAmountBatch(List<String> supplierIds, String groupCode,
      boolean direct, Long startTime, Long endTime);
}
