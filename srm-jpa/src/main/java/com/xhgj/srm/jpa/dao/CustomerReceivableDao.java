package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.jpa.entity.CustomerReceivable;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName CustomerReceivableDao
 **/
public interface CustomerReceivableDao extends BootBaseDao<CustomerReceivable> {
  /**
   *根据参数获取应收款信息 @Author: liuyq @Date: 2023/2/26 16:45
   *
   * @param projectNo 项目编号
   * @param invoiceNo 发票号码
   * @param invoiceTime 发票日期
   * @param price 含税金额
   * @return com.xhgj.srm.jpa.entity.CustomerReceivable
   */
  CustomerReceivable getCustomerReceivable(
      String orderId,  String projectNo, String invoiceNo, Long invoiceTime, BigDecimal price);

  /**
   *根据订单id 和项目编码 获取应收款信息 @Author: liuyq @Date: 2023/2/26 16:45
   *
   * @param projectNo 项目编号,必传
   * @param orderId 订单id,必传
   * @return com.xhgj.srm.jpa.entity.CustomerReceivable
   */
  CustomerReceivable getCustomerReceivableByOrderIdAndNumber(
      String orderId, String projectNo);

  /**
   * 清空 应收单 @Author: liuyq @Date: 2023/6/1 17:42
   *
   * @param
   * @return void
   */
  int delCustomerReceivableExcludeOrderIdList( List<String> excludeOrderIdList);

  /**
   * 清空 应收单 @Author: liuyq @Date: 2023/6/1 17:42
   *
   * @param
   * @return void
   */
  int delCustomerReceivableByOrderIdList( List<String> orderIdList);
}
