package com.xhgj.srm.jpa.dao;/**
 * @since 2025/2/12 9:16
 */

import com.xhgj.srm.common.vo.returnExchange.ReturnExchangeListVO;
import com.xhgj.srm.jpa.dto.returnExchange.ReturnExchangeStatistics;
import com.xhgj.srm.jpa.entity.ReturnExchangeOrder;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;
import com.xhiot.boot.mvc.base.PageResult;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface ReturnExchangeOrderDao extends BootBaseDao<ReturnExchangeOrder> {

  /**
   * 分页查询 退换货订单列表
   * @param query
   * @return
   */
  PageResult<ReturnExchangeListVO> getPage(Map<String, Object> query);

  /**
   * 查询 退换货订单列表 数量
   * @param query
   * @return
   */
  Long getCount(Map<String, Object> query);

  /**
   * 查询 退换货订单统计
   * @param queryMap
   * @return
   */
  ReturnExchangeStatistics getStatistics(Map<String, Object> queryMap);
}
