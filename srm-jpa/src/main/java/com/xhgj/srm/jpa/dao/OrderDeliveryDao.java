package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.jpa.entity.OrderDelivery;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;
import java.util.List;

public interface OrderDeliveryDao extends BootBaseDao<OrderDelivery> {

    /**
     * 订单id
     * @param orderId
     * @return
     */
    List<OrderDelivery> getOrderDeliveryByOrderId(String orderId);

    /**
     * 获取当天发货单数量
     * @return
     */
    long getOrderDeliveryCountByOrderId(String startDate,String endDate);

    /**
     * 获取最新发货单
     * @param id 订单 id
     */

    OrderDelivery getFirstOrderDeliveryByOrderId(String id, String sortType);

  /**
   * 获取需要更新发货状态的发货单
   * @return
   */
  List<OrderDelivery> getNeedUpdateDeliverState();
}
