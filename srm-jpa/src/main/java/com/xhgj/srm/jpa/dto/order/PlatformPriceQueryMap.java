package com.xhgj.srm.jpa.dto.order;

import lombok.Data;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class PlatformPriceQueryMap {

  /**
   * 表单
   */
  private List<InnerBaseForm> forms;


  @Data
  public static class InnerBaseForm {
    /**
     * 供应商id
     */
    private String supplierId;

    /**
     * 合同有效期开始
     */
    private Long startTime;

    /**
     * 合同有效期结束
     */
    private Long endTime;

    /**
     * 平台code
     */
    private String platformCode;
  }
}
