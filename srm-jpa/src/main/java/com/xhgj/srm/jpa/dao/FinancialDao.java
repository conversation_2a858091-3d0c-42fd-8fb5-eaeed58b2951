package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.jpa.entity.Financial;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;
import org.springframework.data.domain.Page;

import java.util.List;

public interface FinancialDao extends BootBaseDao<Financial> {

    /**
     * @Title: getBrandListBySid
     * @Description:获取该供应商下所有财务信息
     * <AUTHOR>
     * @date 2019年8月6日上午11:18:27
     */
    List<Financial> getFinancialListBySid(String sid);

    /**
     * @Title: getBrandListBySid
     * @Description:获取该供应商副本下所有财务信息
     * <AUTHOR>
     * @date 2019年8月6日上午11:18:27
     */
    List<Financial> getFinancialListByFbId(String fbId);

    /**
     * 删除相关的财务信息
     * @param supplierId
     */
    void deleteFinancial(String supplierId);

    /**
     *
     * @Author: liuyq
     * @Date: 2021/6/7 16:24
     * @param sid
     * @param pageNo
     * @param pageSize
     * @return org.springframework.data.domain.Page<com.xhgj.srm.jpa.entity.Financial>
     **/
    Page<Financial> getFinancialListBySid(String sid, String pageNo, String pageSize);

    /**
     * 根据组织内供应商 id 获得财务信息
     * @param supplierInGroupId 组织内供应商 id
     */
    List<Financial> getFinancialListBySupplierInGroupId(String supplierInGroupId);

    /**
     * 通过组织下供应商 id 获得个人供应商的财务信息
     * @param supplierInGroupId 组织下供应商 id 必传
     */
    Financial getPersonSupplierFinancial(String supplierInGroupId);

  /**
   * 通过组织下供应商 id 或供应商 id 删除相关的财务信息
   * @Auhor: liuyq @Date: 2022/7/22 15:30
   *
   * @param supplierId 供应商 id 必传
   * @param supplierInGroupId 组织下供应商 id
   * @return void
   */
  void delFinancialListBySupplierId(String supplierId, String supplierInGroupId);

  /**
   * 通过组织下供应商 id 集合获取财务信息
   * @param supplierInGroupIdList 组织下供应商集合 必传
   */
  List<Financial> getFinancialListBySupplierInGroupIdList(List<String> supplierInGroupIdList);


  /**
   * 更新组织下供应商财务信息
   * @param supplierId 供应商id
   * @param supplierInGroupId 组织下供应商id
   */
  void updateFinancialSupplierInGroup(String supplierInGroupId,String supplierId);

  List<Financial> getBySupplierId(String supplierId);

  /**
   * 根据供应商 id 和组织编码获取财务信息
   * @param supplierId 供应商 id 必传
   * @param groupCode 组织编码 必传
   * @return
   */
  List<Financial> getBySupplierIdAndGroupCode(String supplierId,String groupCode);
}
