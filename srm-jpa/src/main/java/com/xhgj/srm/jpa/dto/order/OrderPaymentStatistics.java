package com.xhgj.srm.jpa.dto.order;

import lombok.Data;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * 付款单统计接口
 */
@Data
public class OrderPaymentStatistics {
  /**
   * 订单数
   */
  private Integer orderCount;
  /**
   * 申请付款金额
   */
  private BigDecimal applyPrice;
  /**
   * 已付金额
   */
  private BigDecimal paymentPrice;

  /**
   * 转换小数位2位
   */
  public BigDecimal getApplyPrice() {
    if (applyPrice == null) {
      return BigDecimal.ZERO;
    }
    return applyPrice.setScale(2, RoundingMode.HALF_UP);
  }

  /**
   * 转换小数位2位
   */
  public BigDecimal getPaymentPrice() {
    if (paymentPrice == null) {
      return BigDecimal.ZERO;
    }
    return paymentPrice.setScale(2, RoundingMode.HALF_UP);
  }

}
