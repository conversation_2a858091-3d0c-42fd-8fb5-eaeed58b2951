package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.jpa.entity.Supplier;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface SupplierDao extends BootBaseDao<Supplier> {

  /**
   * Title：查询最大编码
   *
   * <p>Description:
   *
   * <p>
   *
   * <AUTHOR>
   * @date 2019年8月8日 下午3:33:46
   */
  String isSupplierCode();

  /**
   * 根据供应商企业名称获取供应商
   *
   * <AUTHOR>
   * @since 16:11
   * @return com.xhiot.project.pojo.Supplier 2019/9/22
   */
  Supplier getSupplierByEnterpriseNameNotDel(String enterpriseName, String id);

  /**
   * 根据供应商用户名获取供应商信息
   *
   * @return com.xhiot.project.pojo.Supplier
   * <AUTHOR>
   * @since 2019/8/2 15:23
   */
  Supplier getSupplierByEnterName(String name);

  Supplier getSupplierById(String id);

  Page<String> getEditCheckSupplierPage(
      String useGroup,
      String enterpriseName,
      String uscc,
      String corporate,
      String brands,
      String type,
      String startTime,
      String endTime,
      String userId,
      int pageNo,
      int pageSize);

  /**
   * 分页获取国内供应商
   *
   * @param enterpriseName 企业名称
   * @param pageNo 页码
   * @param pageSize 展示条数
   */
  Page<Supplier> getSupplierDomesticByPage(
      String enterpriseName, String enterpriseCode, String groupId, int pageNo, int pageSize);

  String getSupplierDomestic(String mdmCode, String enterpriseName, String groupId);

  /**
   * Title：获取供应商黑名单列表
   *
   * <p>Description:
   *
   * <p>
   *
   * <AUTHOR>
   * @date 2019年8月12日 下午3:40:04
   */
  Page<String> getSupplierBlackPage(
      String mdmCode,
      String enterpriseName,
      String enterpriseLevel,
      String useGroup,
      String enterpriseNature,
      String industry,
      String purchaserName,
      String brands,
      String mobile,
      String contacts,
      int pageNo,
      int pageSize);

  /**
   * 获取正常的供应商
   *
   * @param userId 用户 id 必传
   * @param createCode 组织 必传
   * @param enterpriseName 供应商名称
   * @param mdmCode mdm 编码
   * @param pageNo 页码
   * @param pageSize 分页条数
   * @return
   */
  Page<Object[]> getFrontNormalSupplierPage(
      String userId,
      String createCode,
      String enterpriseName,
      String mdmCode,
      int pageNo,
      int pageSize);

  List<Supplier> getNormalSupplierListByPurchaser(String userId);

  /**
   * 通过部门查询供应商
   *
   * <AUTHOR>
   * @since 2019年8月14日 下午8:51:13
   */
  List<Supplier> getEnterpriseByDepart(String departId);

  List<Supplier> getEnterpriseByOrg(String departId);

  List<Supplier> getEnterpriseByType();

  /** 获取所有正在使用的供应商(订单) */
  Page<String> getOrderSupplierPage(
      String enterpriseName, String erpCode, List<String> platforms, int pageNo, int pageSize);

  /** 获取所有正在使用的履约供应商(订单) */
  Page<String> getOrderSupplierPageByCondition(
      String enterpriseName, String erpCode, String platform, String status, Long effectiveStart,
      Long effectiveEnd, int pageNo, int pageSize);

  /**
   * 获取所有已生效的履约供应商
   *
   * @param enterpriseName 供应商名称
   * @param platform 平台名称
   * @return Page<String> 供应商id
   */
  Page<String> getTakeEffectOrderSupplierPage(
      String enterpriseName, String platform, String status, int pageNo, int pageSize);

  Page<Object[]> getOrderSupplierPage(
      String enterpriseName,
      List<String> platforms,
      String createCode,
      Boolean openOrder,
      Boolean openSupplierOrder,
      String mdmCode,
      String uscc,
      int pageNo,
      int pageSize);

  Page<Object[]> getOrderSupplierPageSearch(
      String enterpriseName,
      List<String> platforms,
      String createCode,
      Boolean openOrder,
      Boolean openSupplierOrder,
      String mdmCode,
      String uscc,
      String oldSupplierType,
      int pageNo,
      int pageSize);

  List<String> getIdListOrderSupplierList(
      String enterpriseName,
      List<String> platforms,
      String createCode,
      Boolean openOrder,
      Boolean openSupplierOrder,
      String mdmCode);

  long getSupplierOrderOpenCount();

  List<Supplier> getOrderSupplierList(String enterpriseName);

  List<String> getDomesticSupplierList(
      String erpCode,
      String uid,
      String name,
      String enterpriseLevel,
      String useGroup,
      String enterpriseNature,
      String industry,
      String brands,
      String contacts,
      String mobile,
      String purchaserName,
      String startDate,
      String endDate,
      String erpState,
      String isOpen);

  /** 获取同步状态为未上传协议的供应商 */
  List<Supplier> getEnterpriseBySynState(String synState, int score);

  /**
   * 根据名称模糊或曾用名精确查询供应商 id 列表
   *
   * @param nameOrUsedName 名称或曾用名
   * @param supType 供应商类型，注意目前仅国内供应商
   */
  List<String> getAllIdByNameLikeOrUsedName(String nameOrUsedName, String supType);

  /**
   * 根据个人供应商姓名和联系方式查询个人供应商
   *
   * @param personName 个人供应商姓名
   * @param mobile 联系方式
   */
  Supplier getSupplierByEnterNameAndMobile(String personName, String mobile);

  /**
   * 通过 mdm 编码获得供应商
   *
   * @param mdmCode mdm 编码
   */
  Supplier getByMdmCode(String mdmCode);

  /**
   * 根据主数据编码获取供应商名称
   * @param mdmCode 主数据编码
   */
  String getNameByMdmCode(String mdmCode);

  /**
   * 根据名称或曾用名精确查询供应商 id
   *
   * @param nameOrUsedName 名称或曾用名
   * @param supType 供应商类型
   * @param state 数据状态
   */
  String getIdByNameOrUsedNameAndState(String nameOrUsedName, String supType, String state);

  /** 获取正常及黑名单的供应商 */
  List<Supplier> getNormalAndBlackEnterprise();

  /** 获取创建组织为空的数据 */
  List<Supplier> getCreteCodeIsEmpty();

  /**
   * 根据供应商编码查询
   *
   * @param code 供应商编码
   * @return 供应商
   */
  Supplier getSupplierByEnterCode(String code);

  /**
   * 根据供应商名称和mdmCode查询
   *
   * @param mdmCode
   * @param name
   * @return
   */
  Supplier getByMdmCodeAndName(String mdmCode, String name);

  /**
   * 根据合作类型获取数据
   *
   * @param rangType
   * @return
   */
  List<Supplier> getAllByCooperateType(String rangType);

  /**
   * 分页查询一次性供应商
   *
   * @param supType 供应商类型
   * @param enterpriseName 企业名称
   * @param mdmCode mdm编码
   * @param orderTimeStart 开始
   * @param orderTimeEnd 结束
   */
  Page<Supplier> getProvisionalSupplierPage(
      String supType,
      String enterpriseName,
      String mdmCode,
      Long orderTimeStart,
      Long orderTimeEnd,
      Pageable toPageable);

  /**
   * 分页查询一次性供应商
   *
   * @param supType 供应商类型
   * @param enterpriseName 企业名称
   * @param mdmCode mdm编码
   * @param orderTimeStart 开始
   * @param orderTimeEnd 结束
   */
  Page<Supplier> getInteriorSupplierPage(
      String supType,
      String enterpriseName,
      String mdmCode,
      Long orderTimeStart,
      Long orderTimeEnd,
      Pageable toPageable);

  /**
   * 根据最新编辑时间获取供应商
   *
   * @param supType 供应商类型
   */
  Supplier getInteriorSupplierByEditTime(String supType);

  /**
   * 根据供应商名称或mdmCode查询
   *
   * @param mdmCode mdm编码
   * @param enterpriseName 供应商名称
   */
  Supplier getByMdmCodeOrName(String mdmCode, String enterpriseName);

  List<Supplier> getNameList(List<String> name, String supType);

  /**
   * 根据ids查询
   * @param supplierIds
   * @return
   */
  List<Supplier> getSupplierByIds(List<String> supplierIds);

  Supplier getSupplierInfoByMdmCodeAndEnterpriseName(String mdmCode, String enterpriseName);

  Supplier getSupplierInfoByMdmCodeOrEnterpriseName(String mdmCodeOrEnterpriseName);

}
