package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.jpa.dto.contract.ContractStatistics;
import com.xhgj.srm.jpa.entity.Contract;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import org.springframework.data.domain.Page;


public interface ContractDao extends BootBaseDao<Contract> {

  Page<String> getContractPage(
      String contractNum,
      String code,
      String supplierName,
      String startTime,
      String endTime,
      String useCode,
      String uploadStartTime,
      String uploadEndTime,
      String createUser,
      String isFile,
      String users,
      String level,
      String state,
      String purchaserName,
      String deptName,
      int pageNo,
      int pageSize);

  /**
   * 获取合同分页
   * @param queryMap
   * @return
   */
  Page<Contract> getContractPageRef(Map<String, Object> queryMap);

  List<Contract> getContractStatistics(Map<String, Object> queryMap);

  /**
   * 获取合同统计
   * @param queryMap
   * @return
   */
  ContractStatistics getContractStatistics2(Map<String, Object> queryMap);

  List<Contract> getContractList();

  List<Contract> getNormalContractList();

  Contract getContractByNum(String contractNum);

  List<Contract> getContractListBySupplier(String supplierId);

  List<String> getSearchContractList(
      String contractNum,
      String code,
      String supplierName,
      String startTime,
      String endTime,
      String useCode,
      String uploadStartTime,
      String uploadEndTime,
      String createUser,
      String isFile,
      String users,
      String level,
      String state,
      String purchaserName,
      String deptName);

  /**
   * 获得时间范围之后的该员工的供应商及对应的合同数量
   *
   * @param erpId 员工的 erp 编码 必填
   * @param timestampStart 查询范围 时间之后
   * @param supplierInGroupIdList 组织内供应商 id 必传不能为空
   */
  List<Object[]> getSupplierPurchasesCountByErpIdGroupBySupplierInGroupId(
      String erpId, long timestampStart, List<String> supplierInGroupIdList);

  /**
   * 根据用户 erpId 获得已签正常合同的 id
   *
   * @param erpId 用户 id
   * @param myResponsibleSupplierIdList 我的责任供应商业务组织数据 ID 列表 必传不能为空
   */
  List<String> getNormalContractSignedIdList(
      String erpId, List<String> myResponsibleSupplierIdList);


	/**
	 * 组织下当前采购为负责人的合同金额
	 *
	 * @param createManCode 创建人编码
	 * @param userGroup 组织编码 必传
	 * @param startTime 开始时间
	 * @param endTime 结束时间
	 * @return java.util.List<com.xhgj.srm.jpa.entity.Contract>
	 * @Author: liuyq
	 * @Date: 2022/7/11 16:14
	 **/
  BigDecimal getContractTotalMoneyByCreateManCodeAndGroupId(
			String createManCode,
			String userGroup,
			long startTime,
			long endTime
	);


	/**
	 * 查询组织下当前采购为负责人的合同金额和供应商id
	 *
	 * @param createManCode 创建人编码， 必传
	 * @param groupId 组织id， 必传
	 * @return java.util.List<java.lang.Object   [   ]>
	 * @Author: liuyq
	 * @Date: 2022/7/11 16:14
	 **/
	List<Object[]> getContractMoneyAndSupplierIdByCreateManCode(
			String createManCode,
			String groupId
	);


	/**
	 * 查询组织内用户负责合同累计金额和用户编码
	 * @Author: liuyq
	 * @Date: 2022/7/12 10:38
	 * @param groupId 组织id,必传
	 * @return java.util.List<java.lang.Object   [   ]>
	 **/
	List<Object[]> getContractMoneyAndSupplierIdByGroupId(
			String groupId
	);

	/**
	 * 根据用户的 erp 编码获得用户创建的合同
	 * @param erpId 用户 erp 编码
	 */
	List<String> getNormalContractByUserErpId(String erpId);

	/**
	 * 根据创建合同的用户名称查询创建人id为null的记录
	 * @param userName
	 * @return
	 */
	List<Contract> getCreateManIdIsNullContractByCreateUser(String userName);
}
