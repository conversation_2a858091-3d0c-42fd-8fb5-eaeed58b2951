package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.jpa.entity.CustomerReturn;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;

/** <AUTHOR> @ClassName CustomerReturnDao */
public interface CustomerReturnDao extends BootBaseDao<CustomerReturn> {
  /**
   * 根据订单id删除 回款单 Author: liuyq @Date: 2023/5/9 14:52
   *
   * @param receivableId 应收单id
   * @return void
   */
  int delCustomerReturnByReceivableId(String receivableId);
}
