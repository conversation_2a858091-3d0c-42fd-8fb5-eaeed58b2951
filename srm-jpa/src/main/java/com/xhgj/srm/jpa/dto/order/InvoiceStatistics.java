package com.xhgj.srm.jpa.dto.order;

import lombok.Data;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * 开票申请统计数据
 */
@Data
public class InvoiceStatistics {

  /**
   * 下单金额
   */
  private BigDecimal price;

  /**
   * 转换小数位2位
   */
  public BigDecimal getPrice() {
    if (price == null) {
      return BigDecimal.ZERO;
    }
    return price.setScale(2, RoundingMode.HALF_UP);
  }
}
