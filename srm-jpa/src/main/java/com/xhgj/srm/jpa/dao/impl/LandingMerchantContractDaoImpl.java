package com.xhgj.srm.jpa.dao.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.enums.FileReviewStateEnum;
import com.xhgj.srm.common.enums.PurchaseOrderInvoiceType;
import com.xhgj.srm.common.enums.landingContract.ContractStatus;
import com.xhgj.srm.jpa.dao.LandingMerchantContractDao;
import com.xhgj.srm.jpa.entity.LandingMerchantContract;
import com.xhiot.boot.core.common.util.ObjectUtils;
import com.xhiot.boot.core.common.util.StringUtils;
import com.xhiot.boot.framework.jpa.dao.AbstractBaseDao;
import com.xhiot.boot.framework.jpa.util.HqlUtil;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR> Shangyi
 * @date 2023/4/13
 */
@Repository
public class LandingMerchantContractDaoImpl extends AbstractExtDao<LandingMerchantContract>
    implements LandingMerchantContractDao {

  private static final BigDecimal TAX_RATE_13 = new BigDecimal("13");

  public static final byte ACCOUNT_PERIOD_TYPE_BACK_TO_BACK = 0;

  public static final byte ACCOUNT_PERIOD_TYPE_ACCOUNT_PERIOD = 1;


  @Override
  public Page<LandingMerchantContract> getContractPageRef(Map<String, Object> map) {
    StringBuilder sql = new StringBuilder();
    List<Object> params = new ArrayList<>();
    if (map.get("platforms") != null) {
      sql.append("select DISTINCT t.* from t_landing_contract t ");
      sql.append("left join t_supplier_performance sp on sp.landing_contract_id = t.id ");
    }else{
      sql.append("select t.* from t_landing_contract t ");
    }
    sql.append("left join t_supplier ts on t.second_signing_supplier_id = ts.id ");
    if (StrUtil.isNotBlank((CharSequence) map.get("createUser"))
        || StrUtil.isNotBlank((CharSequence) map.get("createUserId"))) {
      sql.append("left join t_user u on t.c_create_man = u.id ");
    }
    this.buildWhereQuery(sql, params, map);
    sql.append("order by t.c_create_time desc ");
    return findPageSql(sql.toString(), params.toArray(), (Integer) map.get("pageNo"), (Integer) map.get("pageSize"));
  }

  @Override
  public List<LandingMerchantContract> getContractStatistics2(Map<String, Object> map) {
    StringBuilder sql = new StringBuilder();
    List<Object> params = new ArrayList<>();
    if (map.get("platforms") != null) {
      sql.append("select DISTINCT t.id from t_landing_contract t ");
      sql.append("left join t_supplier_performance sp on sp.landing_contract_id = t.id ");
    }else{
      sql.append("select t.id from t_landing_contract t ");
    }
    sql.append("left join t_supplier ts on t.second_signing_supplier_id = ts.id ");
    if (StrUtil.isNotBlank((CharSequence) map.get("createUser"))
        || StrUtil.isNotBlank((CharSequence) map.get("createUserId"))) {
      sql.append("left join t_user u on t.c_create_man = u.id ");
    }
    this.buildWhereQuery(sql, params, map);
    List<Object> sqlObjList = getSqlObjList(sql.toString(), params.toArray());
    return sqlObjList.stream()
        .map(obj -> {
//          Object[] objs = (Object[]) obj;
          LandingMerchantContract contract = new LandingMerchantContract();
          contract.setId(Convert.toStr(obj));
          return contract;
        }).collect(Collectors.toList());
  }

  /**
   * 统一构建where查询条件
   */
  private void buildWhereQuery(StringBuilder sql, List<Object> params, Map<String, Object> map) {
    sql.append("where t.c_state = ? and ts.c_state = ? ");
    params.add(Constants.STATE_OK);
    params.add(Constants.STATE_OK);
    if (StrUtil.isNotBlank((CharSequence) map.get("associationStatus"))) {
      sql.append("and t.c_association_performance_status = ? ");
      params.add(map.get("associationStatus"));
    }
    if (StrUtil.isNotBlank((CharSequence) map.get("landingContractStatus"))) {
      sql.append("and t.c_contract_status = ? ");
      params.add(map.get("landingContractStatus"));
    }
    if (StrUtil.isNotBlank((CharSequence) map.get("enterpriseCode"))) {
      sql.append("and ts.c_code like ? ");
      params.add("%" + map.get("enterpriseCode") + "%");
    }
    if (StrUtil.isNotBlank((CharSequence) map.get("enterpriseName"))) {
      sql.append("and ts.c_enterpriseName like ? ");
      params.add("%" + map.get("enterpriseName") + "%");
    }
    if (StrUtil.isNotBlank((CharSequence) map.get("createUser"))) {
      sql.append("and u.c_realname like ? ");
      params.add("%" + map.get("createUser") + "%");
    }
    if (StrUtil.isNotBlank((CharSequence) map.get("contractNum"))) {
      sql.append("and t.c_contract_no like ? ");
      params.add("%" + map.get("contractNum") + "%");
    }
    if (StrUtil.isNotBlank((CharSequence) map.get("contractType"))) {
      sql.append("and t.c_type = ? ");
      params.add(map.get("contractType"));
    }
    if (map.get("createTimeStart") != null) {
      sql.append("and t.c_create_time >= ?  ");
      params.add(map.get("createTimeStart"));
    }
    if (map.get("createTimeEnd") != null) {
      sql.append("and t.c_create_time < ?  ");
      params.add(map.get("createTimeEnd"));
    }
    if (StrUtil.isNotBlank((CharSequence) map.get("signingType"))) {
      sql.append("and t.c_signing_type = ?  ");
      params.add(map.get("signingType"));
    }
    if (StrUtil.isNotBlank((CharSequence) map.get("signatureStatus"))) {
      sql.append("and t.c_signature_status = ? ");
      params.add(map.get("signatureStatus"));
    }
    // 付款方式
    if (StrUtil.isNotBlank((CharSequence) map.get("payType"))) {
      sql.append("and t.c_payment_type = ? ");
      params.add(map.get("payType"));
    }
    // 发票类型
    if (StrUtil.isNotBlank((CharSequence) map.get("invoiceType"))) {
      sql.append("and t.c_invoice_type = ? ");
      params.add(map.get("invoiceType"));
    }
    // 是否13%增值税专票
    if (map.get("is13VatSpecial") != null) {
      if ((Boolean) map.get("is13VatSpecial")) {
        sql.append("and ( COALESCE(t.c_invoice_type, '0') = ? and COALESCE(t.c_tax_rate, 0) = ?) ");
        params.add(PurchaseOrderInvoiceType.VAT_SPECIAL.getKey());
        params.add(TAX_RATE_13);
      } else {
        sql.append(
            "and ( COALESCE(t.c_invoice_type, '0') != ? or COALESCE(t.c_tax_rate, 0) != ?) ");
        params.add(PurchaseOrderInvoiceType.VAT_SPECIAL.getKey());
        params.add(TAX_RATE_13);
      }
    }
    // 有无保证金
    if (map.get("depositState") != null) {
      if ((Boolean) map.get("depositState")) {
        // 有保证金 大于0
        sql.append("and COALESCE(t.c_deposit, 0) > ? ");
        params.add(Constants.DEPOSIT_LINE);
      } else {
        // 无保证金 --可能小于0或null
        sql.append("and COALESCE(t.c_deposit, 0) <= ? ");
        params.add(Constants.DEPOSIT_LINE);
      }
    }
    // 合同归档状态
    if (map.get("contractFile") != null) {
      if ((Boolean) map.get("contractFile")) {
        // 有合同附件
        sql.append(
            "and ( COALESCE(t.c_file_review_state, '0') = ? and exists(select id from t_file tf");
        sql.append(" where tf.c_relationId = t.id and tf.c_relationType = ? and tf.c_state = ?)) ");
        params.add(FileReviewStateEnum.THROUGH_THE.getKey());
        params.add(Constants.FILE_TYPE_LANDING_CONTRACT);
        params.add(Constants.STATE_OK);
      } else {
        // 无合同附件
        sql.append(
            "and ( COALESCE(t.c_file_review_state, '0') != ? or not exists(select id from t_file tf");
        sql.append(" where tf.c_relationId = t.id and tf.c_relationType = ? and tf.c_state = ?)) ");
        params.add(FileReviewStateEnum.THROUGH_THE.getKey());
        params.add(Constants.FILE_TYPE_LANDING_CONTRACT);
        params.add(Constants.STATE_OK);
      }
    }
    // 创建人id过滤
    if (StrUtil.isNotBlank((CharSequence) map.get("createUserId"))) {
      sql.append("and u.id = ? ");
      params.add(map.get("createUserId"));
    }
    // 我方签约主体
    if (StrUtil.isNotBlank((CharSequence) map.get("firstSigningGroupId"))) {
      sql.append("and t.first_signing_group_id = ? ");
      params.add(map.get("firstSigningGroupId"));
    }
    // 合作类型
    if (StrUtil.isNotBlank((CharSequence) map.get("typeOfCooperation"))) {
      sql.append("and t.c_type_of_cooperation = ? ");
      params.add(map.get("typeOfCooperation"));
    }
    // 合作有效期开始
    if (map.get("effectiveStart") != null) {
      sql.append("and t.c_effective_start >= ? ");
      params.add(map.get("effectiveStart"));
    }
    // 合作有效期结束
    if (map.get("effectiveEnd") != null) {
      sql.append("and t.c_effective_end <= ? ");
      params.add(map.get("effectiveEnd"));
    }
    // 账期类型
    if (map.get("accountPeriodType") != null) {
      if ((Byte) map.get("accountPeriodType") == ACCOUNT_PERIOD_TYPE_BACK_TO_BACK) {
        sql.append("and t.c_back_to_back = ? ");
        params.add(true);
      } else if ((Byte) map.get("accountPeriodType") == ACCOUNT_PERIOD_TYPE_ACCOUNT_PERIOD) {
        sql.append("and t.c_back_to_back = ? ");
        params.add(false);
      }
    }
    // 平台，多平台支持 platforms
    if (map.get("platforms") != null) {
      List<String> platforms = (List<String>) map.get("platforms");
      sql.append("and sp.c_platform_code in ( ");
      for (int i = 0; i < platforms.size(); i++) {
        if (i == platforms.size() - 1) {
          sql.append(" ? ");
        } else {
          sql.append(" ?, ");
        }
        params.add(platforms.get(i));
      }
      sql.append(") ");
    }
    // needBundle
    Boolean needBundle = Convert.toBool(map.get("needBundle"));
    if (needBundle != null) {
      sql.append("and t.c_need_bundle = ? ");
      params.add(needBundle);
    }
    String projectCategory = Convert.toStr(map.get("projectCategory"));
    if (StrUtil.isNotBlank(projectCategory)) {
      // 逗号分隔
      String[] projectCategorys = projectCategory.split(",");
      ArrayList<String> list = ListUtil.toList(projectCategorys);
      sql.append(" and ( ");
      for (int i = 0; i < list.size(); i++) {
        if (i != 0) {
          sql.append("or ");
        }
        sql.append(" t.c_project_category like ? ");
        params.add("%" + list.get(i) + "%");
      }
      sql.append(" ) ");
    }

  }

  @Override
  public LandingMerchantContract getByContractNo(String contractNo) {
    String sql = "select * from t_landing_contract t where t.c_state = ? and t.c_contract_no = ? ";
    return getUniqueSqlEntity(sql, Constants.STATE_OK, contractNo);
  }

  @Transactional(rollbackFor = Exception.class)
  @Override
  public void updateAssociationStatusById(String id, String associationStatus) {
    String hql = "update LandingMerchantContract set status = ? where id = ?  ";
    Object[] params = new Object[] {associationStatus, id};
    executeUpdate(hql, params);
  }

  @Override
  public LandingMerchantContract getEntryRegistrationOrderId(String id) {
    String sql =
        "select * from t_landing_contract t where t.c_state = ? and t.c_entry_registration_order_id = ? ";
    return getUniqueSqlEntity(sql, Constants.STATE_OK, id);
  }

  @Override
  public List<LandingMerchantContract> getAllContractsBasedOnCompanyID(String id) {
    String hql =
        "from LandingMerchantContract  where  state = ? and  secondSigningSupplierId = ?";
    Object[] params = new Object[] {Constants.STATE_OK, id};
    return getHqlList(hql, params);
  }

  @Override
  public List<LandingMerchantContract> getFindAllByIds(List<String> ids) {
    Assert.notEmpty(ids);
    StringBuilder hql = new StringBuilder("from LandingMerchantContract  where  c_state = ? ");
    Object[] params = new Object[] {Constants.STATE_OK};
    params = HqlUtil.appendFieldIn(hql, params, "id", ids);
    return getHqlList(hql.toString(), params);
  }

    @Override
    public Page<LandingMerchantContract> getFronContractPage(String contractNum, String contractType,
        String signingType, String enterpriseName, String landingContractStatus, Integer pageNo,
        Integer pageSize, String secondSigningSupplierId) {
      StringBuilder sql =
          new StringBuilder(
              "select t.* from t_landing_contract t "
                  + "left join t_supplier ts on t.first_signing_group_id = ts.id ");
      sql.append("where t.c_state = ?  ");
      Object[] params = new Object[] {Constants.STATE_OK};
      if (!StringUtils.isNullOrEmpty(enterpriseName)) {
        sql.append("and ts.c_enterpriseName like ? ");
        params = ObjectUtils.objectAdd(params, "%" + enterpriseName + "%");
      }
      if (!StringUtils.isNullOrEmpty(contractNum)) {
        sql.append("and t.c_contract_no like ? ");
        params = ObjectUtils.objectAdd(params, "%" + contractNum + "%");
      }
      if (!StringUtils.isNullOrEmpty(contractType)) {
        sql.append("and t.c_type = ? ");
        params = ObjectUtils.objectAdd(params, contractType);
      }
      if (!StringUtils.isNullOrEmpty(signingType)) {
        sql.append("and t.c_signing_type = ?  ");
        params = ObjectUtils.objectAdd(params, signingType);
      }
      if (!StringUtils.isNullOrEmpty(landingContractStatus)) {
        sql.append("and t.c_contract_status = ? ");
        params = ObjectUtils.objectAdd(params, landingContractStatus);
      }
      if (!StringUtils.isNullOrEmpty(secondSigningSupplierId)) {
        sql.append("and t.second_signing_supplier_id = ? ");
        params = ObjectUtils.objectAdd(params, secondSigningSupplierId);
      }
      sql.append("order by t.c_create_time desc ");
      return findPageSql(sql.toString(), params, pageNo, pageSize);
    }

    @Override
    public List<LandingMerchantContract> getAllByEffectiveEnd(long effectiveEndStart,
        long effectiveEndEnd) {
      StringBuilder hql = new StringBuilder("from LandingMerchantContract  where  c_state = ? and"
          + " contractStatus = ? and effectiveEnd >= ? and effectiveEnd <= ? ");
      Object[] params = new Object[] {Constants.STATE_OK, ContractStatus.EFFECTIVE.getCode(),
          effectiveEndStart,effectiveEndEnd};
      return getHqlList(hql.toString(), params);
    }

  @Override
  public List<LandingMerchantContract> getAllByEffectiveStart(long start, long end) {
    StringBuilder hql = new StringBuilder("from LandingMerchantContract  where  c_state = ? and "
        + "contractStatus = ? and fileReviewState = ? and effectiveStart >= ? and effectiveStart "
        + "<=  "
        + "?  ");
    Object[] params = new Object[] {Constants.STATE_OK, ContractStatus.VOID.getCode(),
        FileReviewStateEnum.THROUGH_THE.getKey(), start,end};
    return getHqlList(hql.toString(), params);
  }

  @Override
  public List<LandingMerchantContract> getContractStatusIsNUll() {
    StringBuilder hql = new StringBuilder("from LandingMerchantContract  where  c_state = ? and "
        + "contractStatus IS NULL ");
    Object[] params = new Object[] {Constants.STATE_OK};
    return getHqlList(hql.toString(), params);
  }
  @Override
  public String getByPlatformCodeAndSupplierId(String platformCode,String supplierId) {
    Assert.notEmpty(platformCode);
    Assert.notEmpty(supplierId);
    StringBuilder hql = new StringBuilder("select lc.id from t_landing_contract lc "
        + "left join t_entry_registration_order ero on lc.c_entry_registration_order_id = ero.id "
        + "left join t_supplier_performance sp on lc.id = sp.landing_contract_id "
        + "where  lc.c_state = ? and c_contract_status = ? and lc.second_signing_supplier_id=? and (ero.platform=? "
        + "or sp.c_platform_code = ? )"
        + "order by lc.c_create_time desc ");
    Object[] params = new Object[] {Constants.STATE_OK,ContractStatus.EFFECTIVE.getCode(),
        supplierId,
        platformCode,platformCode};
    return (String) getFirstSqlObj(hql.toString(), params);
  }

  @Override
  public List<LandingMerchantContract> findBySecSupplierAndPlatformCode(String secSupplierId,
      String platformCode) {
    Assert.notBlank(secSupplierId);
    Assert.notBlank(platformCode);
    StringBuilder sql =
        new StringBuilder(
            "select tlc.* from t_landing_contract tlc inner join " + "t_supplier_performance tsp ");
    sql.append("on tlc.second_signing_supplier_id = tsp.supplier_id and tlc.id = tsp"
        + ".landing_contract_id ");
    sql.append(
        "where tlc.c_state = ? and tsp.c_state = ? and tsp.c_status = ? and tlc"
            + ".c_contract_status in (?, ?) ");
    sql.append("and tlc.second_signing_supplier_id = ? and tsp.c_platform_code = ? ");
    Object[] params =
        new Object[] {
          Constants.STATE_OK,
          Constants.STATE_OK,
          Constants.SUPPLIER_PERFORMANCE_STATUS_EFFECT,
            ContractStatus.EFFECTIVE.getCode(),
            ContractStatus.VOID.getCode(),
          secSupplierId,
          platformCode
        };
    return getSqlList(sql.toString(), params);
  }

  @Override
  public List<String> getIdByEffectiveEnd() {
    String sql = "select id from t_landing_contract where c_effective_end < ? ";
    Object[] params = new Object[] {System.currentTimeMillis()};
    return getSqlObjList(sql, params);
  }

  @Override
  public List<String> getAllIds() {
    StringBuilder sql = new StringBuilder("select id from t_landing_contract where c_state = ? ");
    Object[] params = new Object[] {Constants.STATE_OK};
    return getSqlObjList(sql.toString(), params);
  }
}
