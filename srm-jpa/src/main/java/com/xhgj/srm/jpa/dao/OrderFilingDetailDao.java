package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.jpa.entity.OrderFilingDetail;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;

import java.util.List;

public interface OrderFilingDetailDao extends BootBaseDao<OrderFilingDetail> {


    /**
     * 获取报备单详情
     * @param filingId
     * @return
     */
    List<OrderFilingDetail> getOrderFilingDetailListByFiling(String filingId);

    /**
     * 删除报备单详情
     * @param filingId
     */
    void deleteFilingDetailByFilingId(String filingId);

}
