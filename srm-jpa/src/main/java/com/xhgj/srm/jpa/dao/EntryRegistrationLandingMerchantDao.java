package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.jpa.entity.EntryRegistrationLandingMerchant;
import com.xhgj.srm.jpa.entity.User;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;

import java.util.List;

/**入驻报备单落地商信息*/
public interface EntryRegistrationLandingMerchantDao extends BootBaseDao<EntryRegistrationLandingMerchant> {

  /**
   * 跟具报备单id查询 落地商信息
   * @param id
   * @return
   */
    EntryRegistrationLandingMerchant getEntryRegistrationOrderId(String id);

  /**
   * 根据报备单ids逻辑删除落地商信息
   *
   * @param EROIds 报备单ids
   */
  void logicDeleteByEntryRegistrationOrderIds(List<String> EROIds, User user);
}
