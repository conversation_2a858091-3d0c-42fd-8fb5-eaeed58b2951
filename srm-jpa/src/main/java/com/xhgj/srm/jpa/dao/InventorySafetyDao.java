package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.common.vo.inventorySafety.InventorySafetyListVO;
import com.xhgj.srm.jpa.entity.InventorySafety;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;
import com.xhiot.boot.mvc.base.PageResult;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022/7/21 23:29
 */
public interface InventorySafetyDao extends BootBaseDao<InventorySafety> {

  /**
   * 获取库存安全库存列表
   * @param queryMap
   * @return
   */
  PageResult<InventorySafetyListVO> getPage(Map<String, Object> queryMap);

  /**
   * 更新库存安全库存状态
   * @param nowLong
   */
  void updateStatus(long nowLong);
}
