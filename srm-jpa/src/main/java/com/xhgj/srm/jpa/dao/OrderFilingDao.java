package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.jpa.dto.filing.FilingStatistics;
import com.xhgj.srm.jpa.entity.OrderFiling;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;
import java.util.List;
import java.util.Map;
import org.springframework.data.domain.Page;

public interface OrderFilingDao extends BootBaseDao<OrderFiling> {

  /**
   * 分页查询报备单
   *
   * @param supplierId
   * @param filingNo
   * @param platform
   * @param price
   * @param customer
   * @param state
   * @param startTime
   * @param endTime
   * @param arriveStartTime
   * @param arriveEndTime
   * @param orderNo
   * @param filingType
   * @param pageNo
   * @param pageSize
   * @return
   */
  Page<OrderFiling> getOrderFilingPageBySupplier(
      String supplierId,
      String filingNo,
      String platform,
      String price,
      String customer,
      String state,
      String startTime,
      String endTime,
      String arriveStartTime,
      String arriveEndTime,
      String orderNo,
      String filingType,
      String fillingState,
      String dockingSalesName,
      int pageNo,
      int pageSize);
  /**
   * 分页查询报备单
   *
   * @param supplierId
   * @param filingNo
   * @param platform
   * @param price
   * @param customer
   * @param state
   * @param startTime
   * @param endTime
   * @param arriveStartTime
   * @param arriveEndTime
   * @param orderNo
   * @param filingType
   * @param pageNo
   * @param pageSize
   * @return
   */
  Page<OrderFiling> getMobileOrderFilingPageBySupplier(
      String supplierId,
      String filingNo,
      String platform,
      String price,
      String customer,
      String state,
      String startTime,
      String endTime,
      String arriveStartTime,
      String arriveEndTime,
      String orderNo,
      String filingType,
      String fillingState,
      String dockingSalesName,
      String queryMobile,
      int pageNo,
      int pageSize);
  /**
   * 获取正常报备单数量
   *
   * @param startDate
   * @param endDate
   * @return
   */
  long getOrderFilingCountByOrderId(String startDate, String endDate);

  /**
   * 根据报备单号获取报备单
   *
   * @param filingNo
   * @return
   */
  OrderFiling getOrderFilingByFilingNo(String filingNo);

    /**
     * 分页获取采购报备单
     *
     * @param userIds 所属用户ids
     * @param supplierName 供应商名称
     * @param filingNo 报备单号
     * @param platform 平台
     * @param price 报备金额
     * @param customer 客户单位
     * @param state 状态
     * @param startTime 报备时间(开始)
     * @param endTime 报备时间(截止)
     * @param arriveStartTime 到期时间(开始)
     * @param arriveEndTime 到期时间(截止)
     * @param orderNo 客户订单号
     * @param filingType 报备类型
     * @param pageNo 当前页
     * @param pageSize 每页展示数量
     * @return
     */
    Page<String> getOrderFilingPageByPurchase(String userIds, String filingNo, String platform,
        String price, String customer, String state, String startTime,
        String endTime, String arriveStartTime, String arriveEndTime, String supplierName,
        String orderNo, String filingType, String dockingSalesName, String remark,int pageNo,
        int pageSize);

    /**
     * 根据map查询
     * @param map
     * @return
     */
    Page<OrderFiling> findByMap(Map<String, Object> map);

  /**
   * 获取报备单统计信息
   * @param queryMap
   * @return
   */
  List<OrderFiling> getFilingStatistics(Map<String, Object> queryMap);

  /**
   * 获取报备单统计信息
   */
  FilingStatistics getFilingStatistics2(Map<String, Object> queryMap);

  /**
   * 获取供应商报备单
   *
   * @param supplierId 供应商id
   * @param filingNo 报备单号
   * @param platform 平台
   * @param price 报备金额
   * @param num 报备数量
   * @param customer 客户单位
   * @param state 状态
   * @param filingStartTime 报备时间(开始)
   * @param filingEndTime 报备时间(截止)
   * @param arriveStartTime 到期时间(开始)
   * @param arriveEndTime 到期时间(截止)
   * @param orderStartTime 下单时间(开始)
   * @param orderEndTime 下单时间(截止)
   * @return
   */
  List<OrderFiling> getOrderFilingListBySupplier(
      String supplierId,
      String filingNo,
      String platform,
      String price,
      String num,
      String customer,
      String state,
      String filingStartTime,
      String filingEndTime,
      String arriveStartTime,
      String arriveEndTime,
      String orderStartTime,
      String orderEndTime);

  Page<OrderFiling> findFixDataPage(int pageNo, Integer pageSize);
  /** 修改已过期报备单状态 */
  void updateFilingState();
}
