package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.jpa.entity.Platform;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;
import java.util.List;

/**
 * Created by Geng Shy on 2023/10/19
 */
public interface PlatformDao extends BootBaseDao<Platform> {

  /**
   * @param code 平台编码
   * @param name 平台名称
   * @return 平台集合
   */
  List<Platform> findLikeByCodeAndName(String code, String name,String platformAbbreviation);

  /**
   * 根据平台编码获取平台信息
   */
  Platform findByCode(String platform);

  /**
   * 根据项目大类修改项目缩写
   * @param abbreviation 项目缩写
   * @param projectCategory 项目大类
   * @param userId 用户id
   */
  void updateAbbreviationByProjectCategory(String abbreviation, String projectCategory, String userId);

  /**
   * 根据平台编码获取平台名称
   * @param codes 平台编码集合
   * @return
   */
  List<String> findProjectNamesByCodes(List<String> codes);

  /**
   * 根据平台编码列表查询集和
   * @param platformCodes
   * @return
   */
  List<Platform> getAllByCodes(List<String> platformCodes);

  /**
   * 通过项目名称或者项目缩写查询项目名称
   * @param projectName
   * @param abbreviation
   */
  List<Platform> searchProjectNames(String projectName, String abbreviation);

  /**
   * 通过项目名称查询平台信息
   * @param projectName
   * @return
   */
  List<Platform>  getListByProjectName(String projectName);

  List<Platform> searchProjectCategory(String projectCategory);
}
