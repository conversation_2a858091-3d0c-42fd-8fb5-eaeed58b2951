package com.xhgj.srm.jpa.dao;


import com.xhgj.srm.common.vo.order.OrderNeedPaymentListVO;
import com.xhgj.srm.common.vo.order.OrderNeedPaymentStatistics;
import com.xhgj.srm.jpa.entity.OrderNeedPayment;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;
import com.xhiot.boot.mvc.base.PageResult;

import java.util.Map;

public interface OrderNeedPaymentDao extends BootBaseDao<OrderNeedPayment> {

     PageResult<OrderNeedPaymentListVO> getPage(Map<String, Object> queryMap);

     OrderNeedPaymentStatistics getOrderAmountStatistics(Map<String, Object> queryMap);
}
