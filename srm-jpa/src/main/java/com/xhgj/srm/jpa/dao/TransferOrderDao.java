package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.common.vo.transferOrder.TransferOrderListVO;
import com.xhgj.srm.jpa.entity.TransferOrder;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;
import com.xhiot.boot.mvc.base.PageResult;
import java.util.Map;

public interface TransferOrderDao extends BootBaseDao<TransferOrder> {

  /**
   * 获取调拨单列表
   * @param queryMap
   * @return
   */
  PageResult<TransferOrderListVO> getPage(Map<String, Object> queryMap);
}
