package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.jpa.entity.SupplierFb;

import java.util.List;

public interface SupplierFbDao {
    /**
     * 根据供应商获取副本
     * @param id
     * @return
     * @throws Exception
     * @Title: getSupplierFbBySid
     * <AUTHOR>
     * @date 2019年8月13日上午10:00:24
     */
    SupplierFb getSupplierFbBySid(String id);

    /**
     * 根据供应商获取副本
     * @param id
     * @return
     * @throws Exception
     * @Title: getSupplierFbBySid
     * <AUTHOR>
     * @date 2019年8月13日上午10:00:24
     */
    SupplierFb getSupplierFbBySupId(String id);


    /**
     * 获取供应商下所有副本
     * @Author: liuyq
     * @Date: 2021/7/9 8:42
     * @param supplierId
     * @return java.util.List<com.xhgj.srm.jpa.entity.SupplierFb>
     **/
    List<SupplierFb> getSupplierFbsBySupId(String supplierId);
}
