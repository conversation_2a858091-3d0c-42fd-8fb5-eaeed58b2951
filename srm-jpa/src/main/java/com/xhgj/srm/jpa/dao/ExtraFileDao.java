package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.jpa.entity.ExtraFile;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;
import java.util.List;

public interface ExtraFileDao extends BootBaseDao<ExtraFile> {

  /**
   * 根据关联id查询所有附件
   *
   * @param relationid 关联 id
   * <AUTHOR> 2019年5月20日 上午11:52:18
   * <AUTHOR> 2019年8月14日11:30:53
   */
  List<ExtraFile> getFileListByRId(String relationid);

  /**
   * 根据关联 id 删除自定义附件
   *
   * @param relationId 关联 id，必传
   * @param relationName 自定义附件名称
   */
  void deleteByRelationId(String relationId, String relationName);

  /**
   * 更新多组织供应商自定义附件
   * @param supplierInGroupId
   * @param supplierId
   */
  void updateExtraFileSupplierInGroup(String supplierInGroupId, String supplierId);
}
