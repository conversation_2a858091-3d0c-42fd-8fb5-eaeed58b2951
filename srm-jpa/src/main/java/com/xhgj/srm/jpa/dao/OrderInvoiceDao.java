package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.jpa.entity.OrderInvoice;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/9
 */
public interface OrderInvoiceDao extends BootBaseDao<OrderInvoice> {


  /**
   * 根据开票单号模糊查询
   * @param invoiceApplicationNumber
   * @return
   */
  List<OrderInvoice> findOrderInvoiceByInvoiceApplicationNumberLike(String invoiceApplicationNumber);

  /**
   * 根据开票单号查询
   * @param invoiceApplicationNumber
   * @return
   */
  OrderInvoice findOrderInvoiceByInvoiceApplicationNumber(String invoiceApplicationNumber);

}
