package com.xhgj.srm.jpa.dto.form;

import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.jpa.dto.BaseDefaultSearchSchemeForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * 物料查询基础表单
 */
@ApiModel(description = "物料查询基础表单")
@Data
public class ProductSearchBaseForm implements BaseDefaultSearchSchemeForm {

  /**
   * 分页页码
   */
  @ApiModelProperty("分页页码")
  private Integer pageNo = 1;

  /**
   * 分页大小
   */
  @ApiModelProperty("分页大小")
  private Integer pageSize = 25;

  /**
   * 供应商id
   */
  @ApiModelProperty("供应商id")
  private String supplierId;

  /**
   * 品牌
   */
  @ApiModelProperty("品牌")
  private String brand;

  /**
   * 类目id
   */
  @ApiModelProperty("类目id")
  private String categoryId;

  /**
   * 编码
   */
  @ApiModelProperty("编码")
  private String code;

  /**
   * 商品名称
   */
  @ApiModelProperty("商品名称")
  private String name;

  /**
   * 规格
   */
  @ApiModelProperty("规格")
  private String model;

  /**
   * 单位编码
   */
  @ApiModelProperty("单位编码")
  private String unitCode;
  /**
   * 供应商价格
   */
  @ApiModelProperty("供应商价格")
  private String supplierPrice;

  /**
   * 平台编码
   */
  @ApiModelProperty("平台编码")
  private String platformCodes;

  /**
   * srm物料类型
   */
  @ApiModelProperty("srm物料类型")
  private String srmProductType;

  /**
   * 物流商编码
   */
  @ApiModelProperty("物流商编码")
  private String shipperCode;

  /**
   * 方案id
   */
  @ApiModelProperty("方案id")
  private String schemeId;

  /**
   * 用户id
   */
  @ApiModelProperty("用户id")
  private String userId;

  /**
   * 构建queryMap
   * @return
   */
  public Map<String,Object> toQueryMap() {
    Map<String, Object> queryMap = new HashMap<>();
    // brandName
    if (StrUtil.isNotBlank(brand)) {
      queryMap.put("brandName", brand);
    }
    // code
    if (StrUtil.isNotBlank(code)) {
      queryMap.put("code", code);
    }
    // categoryId
    if (StrUtil.isNotBlank(categoryId)) {
      queryMap.put("categoryId", categoryId);
    }
    // supplierId
    if (StrUtil.isNotBlank(supplierId)) {
      queryMap.put("supplierId", supplierId);
    }
    // manuCode
    if (StrUtil.isNotBlank(model)) {
      queryMap.put("manuCode", model);
    }
    // name
    if (StrUtil.isNotBlank(name)) {
      queryMap.put("name", name);
    }
    // unitCode
    if (StrUtil.isNotBlank(unitCode)) {
      queryMap.put("unitCode", unitCode);
    }
    // supplierPrice
    if (StrUtil.isNotBlank(supplierPrice)) {
      queryMap.put("supplierPrice", supplierPrice);
    }
    // platformCodes
    if (StrUtil.isNotBlank(platformCodes)) {
      queryMap.put("platformCodes", platformCodes);
    }
    // srmProductType
    if (StrUtil.isNotBlank(srmProductType)) {
      queryMap.put("srmProductType", srmProductType);
    }
    // isDisabled
    queryMap.put("isDisabled", 0);
    // shipperCode
    if (StrUtil.isNotBlank(shipperCode)) {
      queryMap.put("shipperCode", shipperCode);
    }
    // pageNo
    queryMap.put("pageNo", getPageNo());
    // pageSize
    queryMap.put("pageSize", getPageSize());
    // sort
    queryMap.put("sort", "1");
    return queryMap;
  }

  public Integer getPageNo() {
    if (pageNo == null || pageNo < 1) {
      pageNo = 1;
    }
    return pageNo;
  }

  public Integer getPageSize() {
    if (pageSize == null || pageSize < 1) {
      pageSize = 25;
    }
    return pageSize;
  }
}
