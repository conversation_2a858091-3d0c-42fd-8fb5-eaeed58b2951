package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.jpa.dto.landingContract.LandingContractStatistics;
import com.xhgj.srm.jpa.entity.LandingMerchantContract;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;
import java.util.List;
import java.util.Map;
import org.springframework.data.domain.Page;

/**
 * <AUTHOR> <PERSON>
 * @date 2023/4/13
 */
public interface LandingMerchantContractDao extends BootBaseDao<LandingMerchantContract> {
  Page<LandingMerchantContract> getContractPageRef(Map<String, Object> map);

  /**
   * 查询统计数据 方式二 数据库查询后放入内存计算
   */
  List<LandingMerchantContract> getContractStatistics2(Map<String, Object> map);

  /**
   * 根据合同号查询
   * @param contractNo
   */
  LandingMerchantContract getByContractNo(String contractNo);

  /**
   * 修改合同关联履约信息的状态
   * @param id 合同id
   * @param associationStatus   1:已关联; 2:未关联;
   */
  void updateAssociationStatusById(String id, String associationStatus);

  /**
   * 根据入住报备id获取合同
   * @param id
   * @return
   */
    LandingMerchantContract getEntryRegistrationOrderId(String id);

  /**
   * 根据公司ID获取全部合同
   * @param id
   * @return
   */
  List<LandingMerchantContract> getAllContractsBasedOnCompanyID(String id);

  /**
   * 根据合同id集合获取全部合同
   * @param ids
   * @return
   */
  List<LandingMerchantContract> getFindAllByIds(List<String> ids);

  /**
   * 前台获取合同列表 - 分页
   * @param contractNum
   * @param contractType
   * @param signingType
   * @param enterpriseName
   * @param landingContractStatus
   * @param pageNo
   * @param pageSize
   * @param secondSigningSupplierId 供应商Id
   * @return
   */
  Page<LandingMerchantContract> getFronContractPage(String contractNum, String contractType,
      String signingType, String enterpriseName, String landingContractStatus, Integer pageNo, Integer pageSize , String secondSigningSupplierId);


  List<LandingMerchantContract> getAllByEffectiveEnd(long effectiveEndStart, long effectiveEndEnd);

  List<LandingMerchantContract> getAllByEffectiveStart(long start, long end);

  /**
   * 获取合同状态为null
   */
  List<LandingMerchantContract> getContractStatusIsNUll();

  String getByPlatformCodeAndSupplierId(String platformCode, String id);

  /**
   * 根据供应商id和平台编码查询生效的合同
   *
   * @param secSupplierId 对方供应商id
   * @param platformCode 下单平台编码
   * @return
   */
  List<LandingMerchantContract> findBySecSupplierAndPlatformCode(
      String secSupplierId, String platformCode);

  /**
   * 根据合同失效时间超七天获取所有合同id
   *
   * @return
   */
  List<String> getIdByEffectiveEnd();

  List<String> getAllIds();
}
