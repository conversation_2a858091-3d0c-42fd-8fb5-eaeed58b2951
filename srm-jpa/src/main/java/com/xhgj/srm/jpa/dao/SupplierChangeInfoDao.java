package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.jpa.entity.SupplierChangeInfo;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * @ClassName SupplierChangeInfoDao
 * Create by Liuyq on 2021/6/20 14:44
 **/
public interface SupplierChangeInfoDao {


    /**
     * 获取工商信息变更列表
     * @Author: liuyq
     * @Date: 2021/6/20 14:43
     * @param supplierId
     * @param pageNo
     * @param pageSize
     * @return org.springframework.data.domain.Page<com.xhgj.srm.jpa.entity.Brand>
     **/
    Page<SupplierChangeInfo> getSupplierChangeInfoPage(String supplierId, String pageNo, String pageSize);

  /**
   * 获取工商信息变更列表
   *  @Author: liyq @Date: 2022/7/22 17:47
   *
   * @param supplierId 供应商id
   * @param userGroup 组织编码
   * @param pageNo 页数
   * @param pageSize 条数
   * @return org.springframework.data.domain.Page<com.xhgj.srm.jpa.entity.SupplierChangeInfo>
   */
  Page<SupplierChangeInfo> getSupplierChangeInfoPage(
      String supplierId,String userGroup, Integer pageNo, Integer pageSize);

    List<SupplierChangeInfo> getWaitSupplierChangeInfo(String supplierId);
}
