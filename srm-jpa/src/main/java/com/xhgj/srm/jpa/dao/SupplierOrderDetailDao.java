package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.common.dto.SearchSupplierOrderDetailDTO;
import com.xhgj.srm.common.dto.SearchSupplierOrderDetailForm;
import com.xhgj.srm.common.enums.LogicalOperatorsEnums;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormStatus;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormType;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderState;
import com.xhgj.srm.jpa.dto.OrderProductDetailInfoDTO;
import com.xhgj.srm.jpa.dto.ProductDetailInfoDTO;
import com.xhgj.srm.jpa.dto.purchase.order.PurchaseOrderProductStatistics;
import com.xhgj.srm.jpa.entity.SupplierOrderDetail;
import com.xhgj.srm.jpa.entity.SupplierOrderToForm;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * <AUTHOR>
 * @since 2022/11/28 18:59
 */
public interface SupplierOrderDetailDao extends BootBaseDao<SupplierOrderDetail> {

  /**
   * 通过表单关联 id 获得关联行 erp id
   *
   * @param orderToFormId 关联表单 id 必传
   */
  List<String> getErpIdListByOrderToFormId(String orderToFormId);

  /**
   * 分页查询采购订单
   * @param queryMap
   * @return
   */
  Page<SupplierOrderDetail> findPurchaseOrderDetailPageRef(Map<String, Object> queryMap);


  /**
   * 获取采购单统计信息
   * @param queryMap
   * @return
   */
  PurchaseOrderProductStatistics getPagePurchaseOrderStatistics2(Map<String, Object> queryMap);


  /**
   * 根据入库单号获取退货明细
   * @param warehousingFormId 发货单 id 必传
   * @return
   */
  List<SupplierOrderDetail> getByWarehousingFormId(String warehousingFormId);

  /**
   * 获取采购订单发货数量
   *
   * @param purchaseId 采购订单id
   */
  BigDecimal getPurchaseOrderShipQty(String purchaseId);

  /**
   * 统计取消数量
   * @param type 单据类型
   * @param
   */
  BigDecimal getTotalCancel(String type, String supplierOrderId);

  /**
   * 统计退货数量
   * @param type 单据类型
   * @param supplierOrderId 采购订单id
   */
  BigDecimal getTotalReturn (String type, String supplierOrderId);

  /**
   * 获取物料信息
   * @param supplierId
   * @return
   */
  List<ProductDetailInfoDTO> productDetailInfo(String userGroup, String supplierId,
      List<String> orderIds,
      List<String> orderToFormIds,String orderNo,String productVoucher,String model, Boolean historyOrder);

  /**
   * 获取物料关联明细 忽略可开票数量
   */
  List<ProductDetailInfoDTO> getLinkProductDetail(List<String> orderIdList, List<String> orderToFormIdList);

  /**
   * 根据订单id获取物料明细
   * @param id
   * @return
   */
  List<OrderProductDetailInfoDTO> getDetailInfoByOrderId(String id);

  /**
   * 根据入库单id获取物料明细
   * @param id
   * @return
   */
  List<OrderProductDetailInfoDTO> getWarehousingOrderDetail(String id);

  /**
   * 获取详情id
   */
  List<String> findAllIds();

  /**
   * @description: 已退库物料列表
   * @param: supplierName 供应商名称
   * @param: productName 物料名称
   * @param: productCode 物料编码
   * @param: invoiceNums 关联发票号
   **/
  List<ProductDetailInfoDTO> getReturnedMaterialsList(String supplierId, String productName,
      String productCode, String invoiceNums, Boolean historyOrder);

  /**
   * 根据表头筛选采购物料数据
   * @param queryMap
   * @return
   */
  List<Object> getProductListByTableHeaderRef(Map<String, Object> queryMap);

  /**
   * 根据采购订单号修改供应商开票状态
   *
   * @param code 采购订单号
   * @param supplierOrderDetailDao 供应商开票状态
   */
  void updateOrderCode(String code, String supplierOpenInvoiceState);

  /**
   * 根据查询条件及订单id获取订单 <AUTHOR> @Date: 2024年6月17日 17:25:44
   *
   * @param orderToFormId 订单id
   * @param keyWord 物料编码以及品牌商品名称型号
   * @param pageNo 页数
   * @param pageSize 条数
   * @return org.springframework.data.domain.Page<com.xhgj.srm.jpa.entity.OrderDetail>
   */
  Page<SupplierOrderDetail> getOrderDetailPageByOrderId(
      String orderToFormId, String keyWord, Integer pageNo, Integer pageSize, List<String> ids);

  /**
   * @description: 根据purchaseApplyForOrderId和orderState查询订单明细
   * @param: purchaseApplyForOrderId 采购申请id
   * @param: orderState 订单状态
   **/
  SupplierOrderDetail getFirstByPurchaseApplyForOrderIdNotInOrderStateOrderByCreateTimeDesc(String purchaseApplyForOrderId, String orderState);

  /**
   * 根据采购订单ids查询 可用的入库明细(不包含冲销 、 取消)
   * @param supplierOrderIds
   * @return
   */
  List<SupplierOrderDetail> findAvaibleWarehousingDetailByPurchaseOrderId(List<String> supplierOrderIds);

  /**
   * 根据采购订单ids查询 可用的入库明细(不包含冲销 、 取消)
   * @param supplierOrderIds
   * @return
   */
  List<SupplierOrderDetail> findAvaibleOutDetailByPurchaseOrderId(List<String> supplierOrderIds);


  /**
   * 根据供应商订单id获取退货数量
   * @param supplierOrderId 供应商订单id
   * @return
   */
  BigDecimal getSupplierOrderReturnQty(String supplierOrderId);

  /**
   * 根据供应商订单id获取退换单剩余退货数量
   * @param supplierOrderId
   * @return
   */
  BigDecimal getReturnExchangeRemainNum(String supplierOrderId);

  /**
   * 通过物料编码和批次 批量查询采购入库单明细
   */
  List<SearchSupplierOrderDetailDTO> findByProductCodeAndBatchNo(List<SearchSupplierOrderDetailForm> forms);
}
