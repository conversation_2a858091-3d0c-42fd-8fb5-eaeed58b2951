package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.jpa.entity.SupplierChangeRecord;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;
import org.springframework.data.domain.Page;
import java.util.List;

/**
  *@ClassName SupplierChangeRecordDao
  *<AUTHOR>
  *@Date 2023/11/22 14:38
*/
public interface SupplierChangeRecordDao extends BootBaseDao<SupplierChangeRecord> {

  /**
   * 获取修改记录
   * @param supplierId 供应商id
   * @param pageNo 页数
   * @param pageSize 显示条数
   * @return
   */
  Page<SupplierChangeRecord> getSupplierRateRecodeList(String supplierId, Integer pageNo, Integer pageSize, List<Byte> types);
}
