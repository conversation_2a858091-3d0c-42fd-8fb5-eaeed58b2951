package com.xhgj.srm.jpa.dao;/**
 * @since 2024/11/29 14:40
 */

import com.xhgj.srm.jpa.entity.LandingContractBundle;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface LandingContractBundleDao extends BootBaseDao<LandingContractBundle> {

  /**
   * 根据供应商合同匹配表单查询
   * @param matchQuery
   */
  List<LandingContractBundle> findBySupplierBundleMatchQueryForm(Map<String, Object> matchQuery);
}

