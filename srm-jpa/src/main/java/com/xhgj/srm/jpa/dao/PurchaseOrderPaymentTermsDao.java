package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.jpa.dto.purchase.order.PurchaseOrderPaymentTermsDaoParam;
import com.xhgj.srm.jpa.dto.purchase.order.PurchaseOrderPaymentTermsStatistics;
import com.xhgj.srm.jpa.entity.PurchaseOrderPaymentTerms;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;
import org.springframework.data.domain.Page;

public interface PurchaseOrderPaymentTermsDao extends BootBaseDao<PurchaseOrderPaymentTerms> {

  /**
   * 采购订单付款方式分页
   */
  Page<PurchaseOrderPaymentTerms> findPurchaseOrderPaymentTermsPage(
      PurchaseOrderPaymentTermsDaoParam param);

  /**
   * 采购订单付款方式统计
   * @param param
   * @return
   */
  PurchaseOrderPaymentTermsStatistics findPurchaseOrderPaymentTermsStatistics(PurchaseOrderPaymentTermsDaoParam param);
}
