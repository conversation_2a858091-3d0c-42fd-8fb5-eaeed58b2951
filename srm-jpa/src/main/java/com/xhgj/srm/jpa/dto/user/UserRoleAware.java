package com.xhgj.srm.jpa.dto.user;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 用户扩展类---处理role多选问题
 */
public interface UserRoleAware {

  String getRole();

  /**
   * 获取roleList
   * @return
   */
  default List<String> getRoleList() {
    if (StrUtil.isBlank(this.getRole())) {
      return new ArrayList<>();
    }
    return new ArrayList<>(Arrays.asList(this.getRole().split(",")));
  }

  default String getRoleName() {
    List<String> getRoleNameList = this.getRoleNameList();
    if (CollUtil.isEmpty(getRoleNameList)) {
      return "";
    }
    return StrUtil.join(StrUtil.COMMA, getRoleNameList);
  }

  default List<String> getRoleNameList() {
    List<String> roleList = this.getRoleList();
    if (CollUtil.isEmpty(roleList)) {
      return new ArrayList<>();
    }
    return roleList.stream().map(Constants.SUPPLIER_USER_ROLE_TO_NAME::get).filter(StrUtil::isNotBlank)
        .collect(Collectors.toList());
  }
}
