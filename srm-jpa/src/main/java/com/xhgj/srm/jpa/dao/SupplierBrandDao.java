package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.jpa.entity.SupplierBrand;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;
import org.springframework.data.domain.Page;
import java.util.List;

/**
 * @description  Dao
 * <AUTHOR>
 * @date 2023-07-19 15:17:32
 */
public interface SupplierBrandDao extends BootBaseDao<SupplierBrand> {

  /**
   * * 根据品牌中英文名称获取品牌信息
   * --包含 待审核、驳回、通过状态
   *
   * @param brandNameCn brandNameEn 必填
   * <AUTHOR>
   * @date 2023/07/19 14:38
   * @return SupplierBrand
   */
  SupplierBrand getSupplierBrandByBrandName(String brandNameCn,String brandNameEn,String brandId,
      String brandMpmId);

  /**
   * * 根据mpm品牌id和审核状态获取品牌信息
   *
   * @param mpmBrandId  必填
   * <AUTHOR>
   * @date 2023/07/19 14:38
   * @return SupplierBrand
   */
  SupplierBrand getSupplierBrandByMpmBrandIdAndMpmState(String mpmBrandId,String mpmState);


  /**
   * @param brandCode 品牌编码
   * @param brandNameCn 品牌中文名称
   * @param brandNameEn 品牌英文名称
   * @param mpmState 品牌状态
   * @param pageNo
   * @param pageSize
   *     <p>weiquan
   * @return
   */
  Page<SupplierBrand> getSupplierBrandPageByParam(
      String brandCode,
      String brandNameCn,
      String brandNameEn,
      String mpmState,
      String manageType,
      List<String> mpmStatus,
      String supplierId,
      Integer pageNo,
      Integer pageSize
  );

  /**
   * * 通过mpm品牌Id删除询价商品
   *
   * @param mpmBrandId
   * <AUTHOR>
   * @date 2023/07/21
   */
  void deleteByMpmBrandIdAndMpmState(String mpmBrandId,String mpmState);

  /**
   * * 获取当前最大的品牌编码
   *
   * <AUTHOR>
   * @date 2023/07/21
   */
  Integer getMaxBrandCode();
}

