package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.common.enums.PaymentAuditStateEnum;
import com.xhgj.srm.jpa.dto.payment.apply.record.PaymentApplyRecordDaoPageParam;
import com.xhgj.srm.jpa.dto.payment.apply.record.PaymentApplyStatistics;
import com.xhgj.srm.jpa.entity.PaymentApplyRecord;
import org.springframework.data.domain.Page;
import java.util.ArrayList;
import java.util.List;

public interface PaymentApplyRecordDao {

  /**
   * 分页查询
   */
  Page<PaymentApplyRecord> getPage(PaymentApplyRecordDaoPageParam param);

  /**
   * 查询统计(left join未去重)
   */
  List<PaymentApplyRecord> getStatistics(PaymentApplyRecordDaoPageParam param);

  /**
   * 查询统计(left join未去重)
   */
  PaymentApplyStatistics getStatistics2(PaymentApplyRecordDaoPageParam param);

  List<PaymentApplyRecord> getListByParam(String supplierOrderNo,String applyType,String applyState);

  List<PaymentApplyRecord> getListByParam(List<String> supplierOrderNos, String applyType,
      String applyState);

  List<PaymentApplyRecord> getListByParam(String supplierOrderNo,String applyType,
      String applyState,String invoiceNumber);

  /**
   * 发票过账凭证 根据财务凭证号和状态查询<br>
   * (发票过账凭证和预付款凭证使用financial_vouchers_id关联)<br>
   * 非使用financial_vouchers_id关联的数据慎用
   * @param financialVoucherIds
   * @param paymentAuditStateEnums
   * @return
   */
  List<PaymentApplyRecord> findAllByVoucherIdInAndStateIn(List<String> financialVoucherIds,
      ArrayList<PaymentAuditStateEnum> paymentAuditStateEnums);
}
