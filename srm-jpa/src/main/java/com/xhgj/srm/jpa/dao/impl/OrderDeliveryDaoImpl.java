package com.xhgj.srm.jpa.dao.impl;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.Constants_order;
import com.xhgj.srm.jpa.dao.OrderDeliveryDao;
import com.xhgj.srm.jpa.entity.OrderDelivery;
import com.xhiot.boot.core.common.util.DateUtils;
import com.xhiot.boot.framework.jpa.dao.AbstractBaseDao;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class OrderDeliveryDaoImpl  extends AbstractExtDao<OrderDelivery> implements OrderDeliveryDao {

    @Override
    public List<OrderDelivery> getOrderDeliveryByOrderId(String orderId) {
        String hql = "from OrderDelivery od where od.state != ? and od.order.id = ? order by od.createTime desc";
        Object[] params = new Object[] { Constants.STATE_DELETE,orderId};
        return getHqlList(hql, params);
    }

    @Override
    public OrderDelivery getFirstOrderDeliveryByOrderId(String orderId, String sortType) {
        String hql = "from OrderDelivery od where od.state != ? and od.order.id = ? order by od"
            + ".createTime " + sortType;
        Object[] params = new Object[] { Constants.STATE_DELETE,orderId};
        return (OrderDelivery)getFirstHqlObj(hql, params);
    }

    @Override
    public long getOrderDeliveryCountByOrderId(String startDate, String endDate) {
        long count =0;
        String hql = " select count(od.id) from OrderDelivery od where od.createTime >= ? and od.createTime < ?  ";
        Object[] params = new Object[] {DateUtils.parseNormalDateToTimeStamp(startDate),DateUtils.parseNormalDateToTimeStamp(endDate)};
        try {
            count = count(hql,params);
        } catch (Exception e) {
            log.error(ExceptionUtil.stacktraceToString(e));
        }
        return count;
    }

  @Override
  public List<OrderDelivery> getNeedUpdateDeliverState() {
    String hql = "from OrderDelivery od where od.state = ? and od.deliveryState = ? ";
    Object[] params = new Object[] { Constants.STATE_OK, Constants_order.DELIVERY_STATE_WAIT};
    return getHqlList(hql, params);
  }
}
