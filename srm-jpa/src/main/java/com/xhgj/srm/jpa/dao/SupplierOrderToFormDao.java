package com.xhgj.srm.jpa.dao;

import com.xhgj.srm.common.enums.LogicalOperatorsEnums;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormStatus;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormType;
import com.xhgj.srm.jpa.dto.RetreatWarehousePageDTO;
import com.xhgj.srm.jpa.dto.WarehousingDTO;
import com.xhgj.srm.jpa.dto.purchase.order.PurchaseOrderOutBoundDeliveryStatistics;
import com.xhgj.srm.jpa.dto.purchase.order.PurchaseOrderWarehousingStatistics;
import com.xhgj.srm.jpa.entity.SupplierOrderToForm;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import com.xhiot.boot.mvc.base.PageResult;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * <AUTHOR> @ClassName SupplierOrderToFormDao
 */
public interface SupplierOrderToFormDao extends BootBaseDao<SupplierOrderToForm> {
  /**
   * 根据类型获取单据列表 @Author: liuyq @Date: 2022/12/4 17:21
   *
   * @param supplierId 供应商id
   * @param code 采购订单号
   * @param type 单据类型
   * @param directShipment 是否厂直发
   * @param groupName 采购组织
   * @param receiveMan 收件人
   * @param startTime 开始时间
   * @param endTime 结束时间
   * @return org.springframework.data.domain.Page<com.xhgj.srm.jpa.entity.SupplierOrder>
   */
  Page<String> getPage(
      String supplierId,
      List<String> typeList,
      List<String> statusList,
      String code,
      String number,
      String type,
      Boolean directShipment,
      String groupName,
      String receiveMan,
      long startTime,
      long endTime,
      String status,
      String num,
      String returnPrice,
      int pageNo,
      int pageSize);


  /**
   *  @Author: liuyq @Date: 2022/12/14 15:05
   *
   * @param type 单据类型
   * @param statusList 单据状态
   * @param supplierOrderId 供应商id，必传
   * @return java.util.List<com.xhgj.srm.jpa.entity.SupplierOrderToForm>
   */
  List<SupplierOrderToForm> getSupplierOrderFormByTypeAndStatus(
      String type, List<String> statusList, String supplierOrderId);

  /**
   * 根据供应商订单 id 和单据类型单据状态获取发货单数量
   */
  long getSumSupplierOrderFormByTypeAndStatus(String type,
        String supplierOrderId, List<String> statusList);

  /**
   * 后台分页查询退货取消
   *
   * @param startCreateTime 创建时间范围 【开始】
   * @param endCreateTime 创建时间范围 【结束】
   * @param type 类型
   * @param number 单号
   * @param orderCode 采购订单号
   * @param purchaseGroupName 采购组织
   * @param purchaseName 采购员
   * @param directShipment 是否厂家直发
   * @param batchNumber 单据批次号
   * @param status 订单状态
   * @param toPageable 分页参数
   */
  Page<SupplierOrderToForm> getPageAdmin(
      Long startCreateTime,
      Long endCreateTime,
      List<SupplierOrderFormType> type,
      String number,
      String orderCode,
      String purchaseGroupName,
      String purchaseName,
      Boolean directShipment,
      String batchNumber,
      List<SupplierOrderFormStatus> status,
      Pageable toPageable);


  /**
   * 根据单子类型、冲销状态和供应商订单 id 获得对应的单据
   *
   * @param type 类型 必传
   * @param supplierOrderId 供应商订单 id 必传
   */
  List<SupplierOrderToForm> getAllByTypeAndSupplierOrderIdAndStateOrderByTimeAsc(
      String type, String supplierOrderId);

  /**
   * 分页查询入库单
   * @param queryMap
   * @return
   */
  PageResult<WarehousingDTO> warehousingPageRef(Map<String, Object> queryMap);

  /**
   * 入库统计
   * @param queryMap
   * @return
   */
  List<WarehousingDTO> warehousingStatistics(Map<String, Object> queryMap);

  /**
   * 入库统计
   * 聚合
   * @param queryMap
   * @return
   */
  PurchaseOrderWarehousingStatistics warehousingStatistics2(Map<String, Object> queryMap);

  /**
   * 分页查询退库单
   */
  PageResult<RetreatWarehousePageDTO> outBoundDeliveryPageRef(Map<String, Object> queryMap);

  /**
   * 出库统计
   */
  List<RetreatWarehousePageDTO> outBoundDeliveryStatistics(Map<String, Object> queryMap);

  /**
   * 出库统计
   */
  PurchaseOrderOutBoundDeliveryStatistics outBoundDeliveryStatistics2(Map<String, Object> queryMap);



  /**
   *  @Author: liuyq @Date: 2022/12/14 15:05
   *
   * @param type 单据类型
   * @param supplierOrderId 供应商id，必传
   * @return java.util.List<com.xhgj.srm.jpa.entity.SupplierOrderToForm>
   */
  List<SupplierOrderToForm> getSupplierOrderFormByTypeAndState(
      String type, String supplierOrderId);

  /**
   * 根据物料id和表单类型获取单据
   * @param orderProductId 物料id
   * @param type 表单类型
   * @return
   */
  Optional<SupplierOrderToForm> findNewestByOrderProductIdAndType(String orderProductId, String type);
}
