package com.xhgj.srm.request.service.third.xhgj;

import cn.hutool.core.lang.Assert;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.http.Method;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.xhgj.srm.request.config.AbstractXhgjConfig;
import com.xhgj.srm.request.config.XhgjPartnerConfig;
import com.xhgj.srm.request.dto.partner.BusinessInfoDTO;
import com.xhgj.srm.request.dto.partner.PartnerDTO;
import com.xhgj.srm.request.dto.partner.PartnerIcpDTO;
import com.xhgj.srm.request.dto.partner.SrmSaveOrUpdateSupplier;
import com.xhgj.srm.request.service.third.partner.PartnerService;
import com.xhiot.boot.mvc.base.PageResult;
import com.xhiot.boot.mvc.base.ResultBean;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2022/8/1 10:20
 */
@Component
@Slf4j
public class XhgjPartnerRequest implements BaseXhgjRequest {
  private final XhgjPartnerConfig config;
  @Resource
  PartnerService partnerService;

  public XhgjPartnerRequest(XhgjPartnerConfig config) {
    this.config = config;
  }

  @Override
  public AbstractXhgjConfig getConfig() {
    return config;
  }

  @Override
  public Logger getLogger() {
    return log;
  }

  /**
   * 通过 mdm 编码工商信息
   *
   * @param mdmCode mdm 编码 必传
   */
  public Optional<BusinessInfoDTO> getTianYanInfo(String mdmCode, String name) {
    return Optional.ofNullable(partnerService.srmGetTianYanInfo(mdmCode, name));
  }


  public Optional<List<PartnerIcpDTO>> getPartnerIprByKeyWord(String keyWord) {
    return Optional.ofNullable(partnerService.getPartnerIprByKeyWord(keyWord));
  }

  /**
   * 通过 mdm 编码获得合作商
   *
   * @param mdmCode mdm 编码 必传
   */
  public Optional<PartnerDTO> getPartnerByCode(String mdmCode) {
    Assert.notEmpty(mdmCode);
    return Optional.ofNullable(partnerService.getPartnerByCode(mdmCode));
  }

  /**
   * 通过个人供应商名称和手机号获得个人供应商
   *
   * @param name 个人供应商名称
   * @param mobile 个人供应商手机号
   */
  public Optional<PartnerDTO> getPersonPartner(String name, String mobile) {
    Assert.notEmpty(name);
    Assert.notEmpty(mobile);
    return Optional.ofNullable(partnerService.searchPersonPartner(name, mobile));
  }

  /**
   * srm 同步天眼查
   *
   * @param userName 同步人 必传
   * @param partnerCode 合作商编码 必传
   */
  public Optional<BusinessInfoDTO> sysnTianYan(String userName, String partnerCode) {
    Assert.notEmpty(userName);
    Assert.notEmpty(partnerCode);
    return Optional.ofNullable(partnerService.srmSyncTianYanInfo(userName, partnerCode));
  }

  /**
   * 新增或修改主数据（需要审核）
   *
   * @param srmSaveOrUpdateSupplier 修改的主数据内容
   * @return MDM 审核 id
   */
  public Optional<String> saveOrUpdateMainDataWithAssess(
      SrmSaveOrUpdateSupplier srmSaveOrUpdateSupplier) {
    Assert.notNull(srmSaveOrUpdateSupplier);
    return Optional.ofNullable(partnerService.SrmSaveOrUpdate(srmSaveOrUpdateSupplier));
  }

  /**
   * 新增主数据（无需审核）
   *
   * @param srmSaveOrUpdateSupplier 修改的主数据内容
   * @return MDM 主数据编码
   */
  public Optional<String> saveOrUpdateMainData(SrmSaveOrUpdateSupplier srmSaveOrUpdateSupplier) {
    Assert.notNull(srmSaveOrUpdateSupplier);
    return Optional.ofNullable(partnerService.srmSaveNoAssess(srmSaveOrUpdateSupplier));
  }

  /**
   * 根据名称查找 MDM 中是否有该企业（含天眼查）
   *
   * @param name 合作商名称 必传
   * @param type 合作商类型 必传
   */
  public Optional<PartnerDTO> getPartnerByName(String name, String type) {
    Assert.notEmpty(name);
    Assert.notEmpty(type);
    return Optional.ofNullable(partnerService.getPartnerByName(name, type));
  }

  /**
   * srm 查询合作商
   *
   * @param name 合作商名称 必传
   * @param type 合作商类型 必传
   */
  public Optional<List<PartnerDTO>> searchPartner(String name, String type) {
    Assert.notEmpty(name);
    Assert.notEmpty(type);
    return Optional.ofNullable(partnerService.srmSearchPartner(name, type));
  }
  /**
   * srm 通过名称获得天眼查详情
   *
   * @param name 合作商名称 必传
   */
  public Optional<PartnerDTO> getTianYanDetails(String name) {
    Assert.notEmpty(name);
    return Optional.ofNullable(partnerService.getTianYanDetails(name));
  }
  /**
   * srm 标记供应商
   *
   * @param mdmCode mdm 编码
   */
  public Optional<Boolean> markSupplier(String mdmCode) {
    return Optional.ofNullable(partnerService.markSupplier(mdmCode));
  }

  @Override
  public HttpRequest handlePost(String fullUrl, Map<String, Object> param) {
    return HttpUtil.createRequest(Method.POST, fullUrl).form(param);
  }
}
