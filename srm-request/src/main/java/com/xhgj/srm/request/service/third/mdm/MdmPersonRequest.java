package com.xhgj.srm.request.service.third.mdm;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.dtflys.forest.http.ForestResponse;
import com.xhgj.srm.request.config.AbstractXhgjConfig;
import com.xhgj.srm.request.config.XhgjPersonConfig;
import com.xhgj.srm.request.dto.mdm.OrgDomainPageDTO;
import com.xhgj.srm.request.service.third.erp.sap.dto.MdmCostCenterParam;
import com.xhgj.srm.request.service.third.xhgj.BaseXhgjRequest;
import com.xhgj.srm.request.vo.BaseXhgjRes;
import com.xhiot.boot.mvc.base.ResultBean;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class MdmPersonRequest implements BaseXhgjRequest {

  private final XhgjPersonConfig config;

  /** 获取url-方法-地址 */
  private String mdm_person_url = "/getOrganizationListByThird";

  /** 获取url-方法-参数 */
  private Integer pageNo = 1;
  /** 获取url-方法-参数 */
  private Integer pageSize = 10000;

  public MdmPersonRequest(XhgjPersonConfig config) {
    this.config = config;
  }

  @Override
  public AbstractXhgjConfig getConfig() {
    return config;
  }

  @Override
  public Logger getLogger() {
    return log;
  }

  /** mdm 查询组织部门 */
  public Optional<OrgDomainPageDTO> getOrganizationList() {
    Map<String, Object> params = new HashMap<>(3);
    params.put("pageNo", pageNo);
    params.put("pageSize", pageSize);
    return resolveResult(
        post(mdm_person_url, params), new TypeReference<ResultBean<OrgDomainPageDTO>>() {});
  }


  public Optional<OrgDomainPageDTO> getOrganizationListByCondition(MdmCostCenterParam param) {
    Map<String, Object> params = new HashMap<>(3);
    params.put("pageNo", pageNo);
    params.put("pageSize", pageSize);
    params.put("orgCode", param.getOrgCode() + "%");
    params.put("orgName", param.getOrgName());

    return resolveResult(
        post(mdm_person_url, params), new TypeReference<ResultBean<OrgDomainPageDTO>>() {});
  }
}
