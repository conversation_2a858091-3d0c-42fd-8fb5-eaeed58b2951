package com.xhgj.srm.request.service.third.rePush.impl;/**
 * @since 2025/5/7 11:34
 */

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dtflys.forest.Forest;
import com.dtflys.forest.http.ForestRequest;
import com.dtflys.forest.http.ForestResponse;
import com.dtflys.forest.utils.RequestNameValue;
import com.dtflys.forest.utils.TypeReference;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderSyncStatus;
import com.xhgj.srm.jpa.entity.SupplierOrderSync;
import com.xhgj.srm.request.service.third.api.interceptor.RetryInterceptor;
import com.xhgj.srm.request.service.third.rePush.RePushService;
import com.xhgj.srm.request.utils.RePushContext;
import com.xhiot.boot.core.common.exception.CheckException;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Service
public class RePushServiceImpl implements RePushService {

  @Resource
  RetryInterceptor retryInterceptor;

  @Override
  public void rePushPost(SupplierOrderSync reOne) {
    // 构建request
    ForestRequest<?> request = Forest.post(reOne.getUrl());
    request.addBody(JSON.parseObject(reOne.getReq()));
    request.connectTimeout(30000);
    request.readTimeout(180000);
    JSONObject jsonObject = JSON.parseObject(reOne.getHeader());
    Set<Entry<String, Object>> entries = jsonObject.entrySet();
    // 构建headers
    List<RequestNameValue> requestNameValues = new ArrayList<>();
    int i = 0;
    for (Entry<String, Object> entry : entries) {
      i++;
      String key = entry.getKey();
      Object value = entry.getValue();
      RequestNameValue requestNameValue = new RequestNameValue(key, value, i);
      requestNameValues.add(requestNameValue);
    }
    request.addHeaders(requestNameValues);
    request.addInterceptor(retryInterceptor);
    // 执行请求
    ForestResponse<String> execute = request.execute(new TypeReference<ForestResponse<String>>() {});
  }

}
