package com.xhgj.srm.request.service.third.erp.sap.dto;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.xhgj.srm.common.enums.PayTypeSAPEnums;
import com.xhgj.srm.common.enums.PaymentApplyTypeEnums;
import com.xhgj.srm.common.enums.VoucherAccountPeriodEnum;
import com.xhgj.srm.common.enums.VoucherAccountPeriodEnum.VoucherAccountPeriod;
import com.xhgj.srm.jpa.entity.FinancialVoucher;
import com.xhgj.srm.jpa.entity.PaymentApplyDetail;
import com.xhgj.srm.jpa.entity.PaymentApplyRecord;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.request.service.third.erp.sap.dto.SapDrawApplyParam.DATADTO.HEADDTO;
import com.xhgj.srm.request.service.third.erp.sap.dto.SapDrawApplyParam.DATADTO.HEADDTO.ITEMDTO;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.DateUtils;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 提款申请
 */
public class SapDrawApplyParam {

  @JSONField(name = "DATA")
  private DATADTO data;

  public DATADTO getData() {
    return data;
  }

  public void setData(DATADTO data) {
    this.data = data;
  }

  public static class DATADTO {

    @JSONField(name = "HEAD")
    private HEADDTO head;

    public HEADDTO getHead() {
      return head;
    }

    public void setHead(HEADDTO head) {
      this.head = head;
    }

    public static class HEADDTO {

      /**
       * 付款申请单号
       */
      @JSONField(name = "SRMID")
      private String srmid;
      /**
       * 采购组织
       */
      @JSONField(name = "BUKRS")
      private String bukrs;
      /**
       * 1 部分应付款；2 预付款；3 应付款；4 付款退款
       */
      @JSONField(name = "ZZMFKLX")
      private String zzmfklx;

      /**
       * 供应商主数据编码
       */
      @JSONField(name = "LIFNR")
      private String lifnr;
      /**
       * 申请人工号
       */
      @JSONField(name = "ZZMSQRGH")
      private String zzmsqrgh;
      /**
       * 申请人姓名
       */
      @JSONField(name = "ZZMSQR")
      private String zzmsqr;

      /**
       * 付款申请总金额
       */
      @JSONField(name = "ZZMFKSQZJE")
      private String ZZMFKSQZJE;

      @JSONField(name = "ITEM")
      private List<ITEMDTO> item;

      public String getSrmid() {
        return srmid;
      }

      public void setSrmid(String srmid) {
        this.srmid = srmid;
      }

      public String getZZMFKSQZJE() {
        return ZZMFKSQZJE;
      }

      public void setZZMFKSQZJE(String ZZMFKSQZJE) {
        this.ZZMFKSQZJE = ZZMFKSQZJE;
      }

      public String getBukrs() {
        return bukrs;
      }

      public void setBukrs(String bukrs) {
        this.bukrs = bukrs;
      }

      public String getZzmfklx() {
        return zzmfklx;
      }

      public void setZzmfklx(String zzmfklx) {
        this.zzmfklx = zzmfklx;
      }

      public String getLifnr() {
        return lifnr;
      }

      public void setLifnr(String lifnr) {
        this.lifnr = lifnr;
      }

      public String getZzmsqrgh() {
        return zzmsqrgh;
      }

      public void setZzmsqrgh(String zzmsqrgh) {
        this.zzmsqrgh = zzmsqrgh;
      }

      public String getZzmsqr() {
        return zzmsqr;
      }

      public void setZzmsqr(String zzmsqr) {
        this.zzmsqr = zzmsqr;
      }

      public List<ITEMDTO> getItem() {
        return item;
      }

      public void setItem(List<ITEMDTO> item) {
        this.item = item;
      }

      public static class ITEMDTO {

        /**
         * 按照顺序从1排序
         */
        @JSONField(name = "ZZMFKSQHXMH")
        private String zzmfksqhxmh;
        /**
         * SAP财务凭证号
         */
        @JSONField(name = "BELNR")
        private String belnr;
        /**
         * SAP会计年度
         */
        @JSONField(name = "GJAHR")
        private String gjahr;
        /**
         * 凭证金额 应付金额
         */
        @JSONField(name = "FCSL")
        private String fcsl;
        /**
         * 凭证金额 付款申请金额
         */
        @JSONField(name = "ZZMSFJE")
        private String zzmsfje;
        /**
         * 付款起算日期 默认传31
         */
        @JSONField(name = "BSCHL")
        private String bschl;
        /**
         * 预计付款日期
         */
        @JSONField(name = "ZFBDT")
        private String zfbdt;
        /**
         * 付款条件 不传
         */
        @JSONField(name = "ZTERM")
        private String zterm;
        /**
         * 天数
         */
        @JSONField(name = "ZBD1T")
        private String zbd1t;
        /**
         * 预计付款日期
         */
        @JSONField(name = "ZZMQWFKRQ")
        private String zzmqwfkrq;
        /**
         * 付款方式编码
         */
        @JSONField(name = "ZLSCH")
        private String zlsch;
        /**
         * 付款方式编码对应的文案 不传
         */
        @JSONField(name = "TEXT2")
        private String text2;
        /**
         * 申请类型
         * （1加急申请；2延长申请；3冻结申请；4提款申请）
         */
        @JSONField(name = "ZZMFKZT")
        private String zzmfkzt;
        /**
         * 传递此行的采购订单号
         */
        @JSONField(name = "ZZMMXBZ")
        private String zzmmxbz;
        /**
         * 开户银行的联行号
         * （冻结/加急/延长时，取供应商默认的开户行联行号）
         */
        @JSONField(name = "BANKL")
        private String bankl;
        /**
         * 银行账号
         * （冻结/加急/延长时，取供应商默认的银行账号）
         */
        @JSONField(name = "BANKN")
        private String bankn;
        /**
         * 账户名称
         * （冻结/加急/延长时，取供应商默认的账户名称）
         */
        @JSONField(name = "KOINH")
        private String koinh;

        /**
         * 备注文本
         * （申请类型为加急解冻延长冻结）
         */
        @JSONField(name = "ZZMBTBZ")
        private String zzmbtbz;

        public String getZzmfksqhxmh() {
          return zzmfksqhxmh;
        }

        public void setZzmfksqhxmh(String zzmfksqhxmh) {
          this.zzmfksqhxmh = zzmfksqhxmh;
        }

        public String getBelnr() {
          return belnr;
        }

        public void setBelnr(String belnr) {
          this.belnr = belnr;
        }

        public String getGjahr() {
          return gjahr;
        }

        public void setGjahr(String gjahr) {
          this.gjahr = gjahr;
        }

        public String getFcsl() {
          return fcsl;
        }

        public void setFcsl(String fcsl) {
          this.fcsl = fcsl;
        }

        public String getZzmsfje() {
          return zzmsfje;
        }

        public void setZzmsfje(String zzmsfje) {
          this.zzmsfje = zzmsfje;
        }

        public String getBschl() {
          return bschl;
        }

        public void setBschl(String bschl) {
          this.bschl = bschl;
        }

        public String getZfbdt() {
          return zfbdt;
        }

        public void setZfbdt(String zfbdt) {
          this.zfbdt = zfbdt;
        }

        public String getZterm() {
          return zterm;
        }

        public void setZterm(String zterm) {
          this.zterm = zterm;
        }

        public String getZbd1t() {
          return zbd1t;
        }

        public void setZbd1t(String zbd1t) {
          this.zbd1t = zbd1t;
        }

        public String getZzmqwfkrq() {
          return zzmqwfkrq;
        }

        public void setZzmqwfkrq(String zzmqwfkrq) {
          this.zzmqwfkrq = zzmqwfkrq;
        }

        public String getZlsch() {
          return zlsch;
        }

        public void setZlsch(String zlsch) {
          this.zlsch = zlsch;
        }

        public String getText2() {
          return text2;
        }

        public void setText2(String text2) {
          this.text2 = text2;
        }

        public String getZzmfkzt() {
          return zzmfkzt;
        }

        public void setZzmfkzt(String zzmfkzt) {
          this.zzmfkzt = zzmfkzt;
        }

        public String getZzmmxbz() {
          return zzmmxbz;
        }

        public void setZzmmxbz(String zzmmxbz) {
          this.zzmmxbz = zzmmxbz;
        }

        public String getBankl() {
          return bankl;
        }

        public void setBankl(String bankl) {
          this.bankl = bankl;
        }

        public String getBankn() {
          return bankn;
        }

        public void setBankn(String bankn) {
          this.bankn = bankn;
        }

        public String getKoinh() {
          return koinh;
        }

        public void setKoinh(String koinh) {
          this.koinh = koinh;
        }

        public String getZzmbtbz() {
          return zzmbtbz;
        }

        public void setZzmbtbz(String zzmbtbz) {
          this.zzmbtbz = zzmbtbz;
        }
      }
    }
  }

  public static SapDrawApplyParam getInstance(
      PaymentApplyRecord paymentApplyRecord,
      List<PaymentApplyDetail> paymentApplyDetails,
      List<FinancialVoucher> financialVouchers,
      String supplierMdmCode,
      User user,
      String sapType,
      Map<FinancialVoucher, String> financialVoucherStringMap) {
    int i = 1;
    ArrayList<ITEMDTO> items = new ArrayList<>();
    for (PaymentApplyDetail paymentApplyDetail : paymentApplyDetails) {
      FinancialVoucher financialVoucher = financialVouchers.stream()
          .filter(f -> paymentApplyDetail.getFinancialVouchersIdList().contains(f.getId()))
          .findFirst().orElse(null);
      if (financialVoucher == null) {
        throw new CheckException("未找到对应的财务凭证");
      }
      // 获取financialVoucher
      String zfbdtDate = DateUtils.formatTimeStampToStr(financialVoucher.getExpectedPaymentDate(),
          DatePattern.PURE_DATE_PATTERN);
      HEADDTO.ITEMDTO itemdto = new HEADDTO.ITEMDTO();
      String adjustedTime = financialVoucherStringMap.get(financialVoucher);
      if (StrUtil.isNotBlank(financialVoucherStringMap.get(financialVoucher))) {
        String zzmqwfkrq = DateUtils.formatTimeStampToStr(Long.parseLong(adjustedTime),
            DatePattern.PURE_DATE_PATTERN);
        itemdto.setZzmqwfkrq(zzmqwfkrq);
      } else {
        itemdto.setZzmqwfkrq(zfbdtDate);
      }
      itemdto.setZzmfksqhxmh(String.valueOf(i++));
      itemdto.setBelnr(financialVoucher.getFinancialVoucherNo());
      itemdto.setGjahr(financialVoucher.getAccountingYear());
      BigDecimal applyAdvancePrice = paymentApplyDetail.getApplyAdvancePrice();
      itemdto.setFcsl(applyAdvancePrice.toPlainString());
      itemdto.setZzmsfje(applyAdvancePrice.toPlainString());
      //默认传31
      final String default_bschl = "31";
      itemdto.setBschl(default_bschl);
      itemdto.setZfbdt(zfbdtDate);
      VoucherAccountPeriod voucherAccountPeriod =
          VoucherAccountPeriodEnum.getByDesc(financialVoucher.getAccountPeriod());
      itemdto.setZbd1t(
          voucherAccountPeriod != null ? voucherAccountPeriod.getDays().toString() : "");
      String paymentType = paymentApplyDetail.getPayType();
      if (paymentType == null) {
        paymentType = financialVoucher.getPaymentType();
      }
      PayTypeSAPEnums payTypeSAPEnums = PayTypeSAPEnums.fromName(paymentType);
      if (payTypeSAPEnums == null) {
        payTypeSAPEnums = PayTypeSAPEnums.fromKey(paymentType);
      }
      itemdto.setZlsch(
          payTypeSAPEnums != null ? payTypeSAPEnums.getCode() : PayTypeSAPEnums.TRANSFER.getCode());
      itemdto.setText2(
          payTypeSAPEnums != null ? payTypeSAPEnums.getName() : PayTypeSAPEnums.TRANSFER.getName());
      //默认传4 提款申请
      itemdto.setZzmfkzt(sapType);
      itemdto.setBankl(paymentApplyDetail.getBankCode());
      itemdto.setBankn(paymentApplyDetail.getBankAccount());
      itemdto.setKoinh(paymentApplyDetail.getAccountName());
      itemdto.setZzmmxbz(paymentApplyDetail.getSupplierOrderNo());
      // 如果是加急解冻延长冻结 则传备注文本
      if (PaymentApplyTypeEnums.EXTEND.getKey().equals(paymentApplyRecord.getApplyType())
          || PaymentApplyTypeEnums.URGENT.getKey().equals(paymentApplyRecord.getApplyType())
          || PaymentApplyTypeEnums.FREEZE.getKey().equals(paymentApplyRecord.getApplyType())
          || PaymentApplyTypeEnums.THAW.getKey().equals(paymentApplyRecord.getApplyType())) {
        itemdto.setZzmbtbz(paymentApplyDetail.getRemark());
      }
      items.add(itemdto);
    }
    BigDecimal totalPrice = financialVouchers.stream()
        .map(FinancialVoucher::getRelatedAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
    SapDrawApplyParam sapDrawApplyParam = new SapDrawApplyParam();
    DATADTO datadto = new DATADTO();
    HEADDTO headdto = new HEADDTO();
    headdto.setSrmid(paymentApplyRecord.getPaymentApplyNo().substring(4, 14));
    headdto.setBukrs(financialVouchers.get(0).getGroupCode());
    // 20240326 改为默认传1
    final String default_zzmfklx = "1";
    headdto.setZzmfklx(default_zzmfklx);
    headdto.setZZMFKSQZJE(totalPrice.toPlainString());
    headdto.setLifnr(supplierMdmCode);
    headdto.setZzmsqrgh(user.getCode());
    headdto.setZzmsqr(user.getRealName());
    headdto.setItem(items);
    datadto.setHead(headdto);
    sapDrawApplyParam.setData(datadto);
    return sapDrawApplyParam;
  }
}
