package com.xhgj.srm.request.service.third.xhgj;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.http.Method;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.xhgj.srm.request.config.AbstractXhgjConfig;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.mvc.base.ResultBean;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;

/**
 * <AUTHOR>
 * @since 2022/8/8 14:42
 */
public interface BaseXhgjRequest {

  AbstractXhgjConfig getConfig();

  Logger getLogger();
  /**
   * 构建完整接口路径
   *
   * @param url 接口路径（半路径）
   */
  default String buildFullUrl(String url) {
    String serviceUrl = getConfig().getServiceUrl();
    if (StrUtil.isBlank(serviceUrl)) {
      throw new CheckException(
          StrUtil.emptyToDefault(getConfig().getServiceName(), "存在外部系统") + "地址未配置，请联系管理员处理！");
    }
    return StrUtil.removeSuffix(serviceUrl, "/") + StrUtil.addPrefixIfNot(url, "/");
  }

  /**
   * 发送 get 请求
   *
   * @param url 接口地址（半路径）
   * @param param 参数
   */
  default String get(String url, Map<String, Object> param) {
    return executeAndGetBody(url, param, Method.GET);
  }

  default String post(String url, Map<String, Object> param) {
    return executeAndGetBody(url, param, Method.POST);
  }

  default String executeAndGetBody(String url, Map<String, Object> param, Method method) {
    String fullUrl = buildFullUrl(url);
    HttpRequest request;
    if (Objects.equals(method, Method.POST)) {
      request = handlePost(fullUrl, param);
    } else if (Objects.equals(method, Method.GET)) {
      request =
          HttpUtil.createGet(HttpUtil.urlWithForm(fullUrl, param, CharsetUtil.CHARSET_UTF_8, true));
    } else {
      throw new IllegalArgumentException();
    }
    try (HttpResponse response = request.execute()) {
      String body = response.body();
      if (BooleanUtil.isTrue(getConfig().getEnableLog())) {
        getLogger().info("调用接口：【" + fullUrl + "】");
        getLogger().info("入参：【" + param + "】");
        getLogger().info("出参：【" + body + "】");
      }
      return body;
    } catch (Exception e) {
      getLogger().error(ExceptionUtil.stacktraceToString(e));
      throw new CheckException(
          "调用【" + getConfig().getServiceName() + "】接口异常：" + ExceptionUtil.getSimpleMessage(e));
    }
  }

  default HttpRequest handlePost(String fullUrl, Map<String, Object> param) {
    return HttpUtil.createRequest(Method.POST, fullUrl).body(JSON.toJSONString(param));
  }

  /**
   * 解析 MDM 接口响应
   *
   * @param json json 字符串
   */
  default <T> Optional<T> resolveResult(String json, TypeReference<ResultBean<T>> typeReference) {
    String serviceName = getConfig().getServiceName();
    try {
      ResultBean<T> resultBean = JSON.parseObject(json, typeReference);
      if (resultBean.getCode() == ResultBean.SUCCESS) {
        return Optional.ofNullable(resultBean.getData());
      } else {
        throw new CheckException("【" + serviceName + "】接口提示：" + resultBean.getMsg());
      }
    } catch (Exception e) {
      if (e instanceof CheckException) {
        throw e;
      } else {
        getLogger().error(ExceptionUtil.stacktraceToString(e, -1));
        throw new CheckException("解析【" + serviceName + "】接口响应异常：" + e);
      }
    }
  }
}
