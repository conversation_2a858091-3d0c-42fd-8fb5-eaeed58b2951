package com.xhgj.srm.request.service.third.xhgj;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.dtflys.forest.http.ForestResponse;
import com.xhgj.srm.common.enums.ShortMessageEnum;
import com.xhgj.srm.request.config.AbstractXhgjConfig;
import com.xhgj.srm.request.config.XhgjSMSConfig;
import com.xhgj.srm.request.dto.sms.SendSmsParams;
import com.xhgj.srm.request.service.third.api.SMSApi;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.mvc.base.ResultBean;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2022/12/19 9:04
 */
@Slf4j
@Component
public class XhgjSMSRequest implements BaseXhgjRequest {

  private final XhgjSMSConfig config;

  @Resource
  SMSApi smsApi;

  private static final String MSG_SUCCESS_EN = "success";

  private static final String MSG_SUCCESS_CN = "操作成功";

  private static final int SUCCESS_CODE = 0;

  /** 发送短信 */
  private static final String SEND_SMS = "/sms/sendSms";

  public XhgjSMSRequest(XhgjSMSConfig config) {
    this.config = config;
  }

  @Override
  public AbstractXhgjConfig getConfig() {
    return config;
  }

  @Override
  public Logger getLogger() {
    return log;
  }

  /**
   * @param shortMessageEnum
   * @param mobile
   * @param params
   */
  public Boolean sendSms(
      ShortMessageEnum shortMessageEnum, String mobile, Map<String, String> params) {
    SendSmsParams sendSmsParams = new SendSmsParams();
    sendSmsParams.setSendType(shortMessageEnum.getType());
    sendSmsParams.setParams(params);
    sendSmsParams.setMobile(mobile);
    List<String> allowMobileList = config.getAllowMobileList();
    if (CollUtil.isNotEmpty(allowMobileList)) {
      if (!allowMobileList.contains(mobile)) {
        return Boolean.FALSE;
      }
    }
    ForestResponse<ResultBean<Boolean>> resultBeanForestResponse = smsApi.sendSms(sendSmsParams);
    if (resultBeanForestResponse == null) {
      throw new CheckException("短信发送失败");
    }
    ResultBean<Boolean> resultBean = resultBeanForestResponse.getResult();
    if (resultBean == null) {
      throw new CheckException("短信发送失败");
    }
    if (checkFail(resultBean.getMsg(), resultBean.getCode())) {
      throw new CheckException("短信发送信息异常： " + resultBean.getMsg());
    }
    if (resultBean.getData() == null) {
      throw new CheckException("短信发送失败");
    }
    return resultBean.getData();
//    return resolveResult(
//            post(SEND_SMS, JSON.parseObject(JSON.toJSONString(sendSmsParams))),
//            new TypeReference<ResultBean<Boolean>>() {})
//        .orElse(false);
  }

  private boolean checkFail(String msg, int code) {
    if (MSG_SUCCESS_EN.equals(msg) || MSG_SUCCESS_CN.equals(msg) || code == SUCCESS_CODE) {
      return false;
    }
    return true;
  }
}
