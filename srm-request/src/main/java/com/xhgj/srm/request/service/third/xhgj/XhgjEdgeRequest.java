package com.xhgj.srm.request.service.third.xhgj;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.xhgj.srm.common.dto.MDMBankInfoDTO;
import com.xhgj.srm.request.config.AbstractXhgjConfig;
import com.xhgj.srm.request.config.XhgjEdgeConfig;
import com.xhgj.srm.request.dto.edge.CountryDTO;
import com.xhgj.srm.request.dto.edge.CountryDomain;
import com.xhgj.srm.request.dto.edge.IndustryDTO;
import com.xhgj.srm.request.dto.edge.ProvinceCityDTO;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.mvc.base.ResultBean;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2022/8/8 14:42
 */
@Component
@Slf4j
public class XhgjEdgeRequest implements BaseXhgjRequest {
  @Autowired private XhgjEdgeConfig config;
  @Resource
  ApplicationContext applicationContext;

  @Override
  public AbstractXhgjConfig getConfig() {
    return config;
  }

  @Override
  public Logger getLogger() {
    return log;
  }

  private static final String URL_GET_ALL_COUNTRY = "/country/getAll";
  private static final String URL_GET_ALL_INDUSTRY = "/industry/getAll";
  private static final String URL_GET_ALL_PROVINCE_CITY = "/area/getProvinceCity";

  private static final String URL_GET_COUNTRY_BY_NAME = "/country/getByName";

  /**
   * 根据银行名称或是联行号获取MDM内部数据与银行数据
   */
  private static final String FIND_PERSON_BANK_INFO_BY_BANK_CODE = "/bank/getBankTableDTO";

  public Optional<List<CountryDTO>> getAllCountry() {
    return resolveResult(
        get(URL_GET_ALL_COUNTRY, null), new TypeReference<ResultBean<List<CountryDTO>>>() {});
  }

  public Optional<List<IndustryDTO>> getAllIndustry() {
    return resolveResult(
        get(URL_GET_ALL_INDUSTRY, null), new TypeReference<ResultBean<List<IndustryDTO>>>() {});
  }

  @Cacheable(value = "srm.provinceCity", key = "'provinceCity'")
  public Optional<List<ProvinceCityDTO>> getAllProvinceCity() {
    return resolveResult(
        get(URL_GET_ALL_PROVINCE_CITY, null),
        new TypeReference<ResultBean<List<ProvinceCityDTO>>>() {});
  }

  /**
   * 清除缓存
   */
  @Scheduled(cron = "0 0 3 * * ?")
  @CacheEvict(value = "srm.provinceCity", key = "'provinceCity'")
  public void refreshAllProvinceCity() {
    log.info("刷新省市区缓存");
  }

  public Optional<List<ProvinceCityDTO>> getAllProvinceCityWithCountry() {
    List<ProvinceCityDTO> allProvinceCity =
        applicationContext.getBean(XhgjEdgeRequest.class).getAllProvinceCity().orElse(new ArrayList<>());
    List<ProvinceCityDTO> result = new ArrayList<>();
    ProvinceCityDTO country = new ProvinceCityDTO();
    country.setCode("1123");
    country.setChildren(allProvinceCity);
    country.setPcode(null);
    country.setName("全国");
    result.add(country);
    return Optional.of(result);
  }

  public Optional<CountryDomain> getCountryByName(String name) {
    Assert.notEmpty(name);
    Map<String, Object> param = new HashMap<>();
    param.put("name", name);
    return resolveResult(
        get(URL_GET_COUNTRY_BY_NAME, param), new TypeReference<ResultBean<CountryDomain>>() {});
  }

  public MDMBankInfoDTO pageQueryBankBranchByEdge(String bankName, String bankCode,String bankNameOrBankCode) {
    MDMBankInfoDTO mdmBankInfoDTO;
    bankName = StrUtil.emptyIfNull(bankName);
    bankCode = StrUtil.emptyIfNull(bankCode);
    bankNameOrBankCode = StrUtil.emptyIfNull(bankNameOrBankCode);
    String url =
        config.getServiceUrl() + FIND_PERSON_BANK_INFO_BY_BANK_CODE+ "?bankName=" + bankName
            + "&bankCode=" + bankCode +"&bankNameOrBankCode="+bankNameOrBankCode;
    try {
      String json = HttpUtil.get(url);
       mdmBankInfoDTO =
          JSON.parseObject(json, new TypeReference<MDMBankInfoDTO>() {});
    } catch (Exception e) {
      log.error("边缘服务-edge-服务异常");
      throw new CheckException("边缘服务-edge-服务异常", e);
    }
    return mdmBankInfoDTO;
  }
}
