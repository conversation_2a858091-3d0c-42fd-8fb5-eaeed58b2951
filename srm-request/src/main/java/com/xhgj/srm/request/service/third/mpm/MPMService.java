package com.xhgj.srm.request.service.third.mpm;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.xhgj.srm.common.dto.MdmBrand;
import com.xhgj.srm.common.dto.MdmBrandPageData;
import com.xhgj.srm.common.dto.OrderPlatformDTO;
import com.xhgj.srm.request.dto.mpm.BrandParamWrapper;
import com.xhgj.srm.request.dto.mpm.GwPlatformProductSearchForm;
import com.xhgj.srm.request.dto.mpm.MPMShiBeiQueryForm;
import com.xhgj.srm.request.dto.product.ApplyForPriceAdjustmentParam;
import com.xhgj.srm.request.dto.supplierCategory.CategoryFindDto;
import com.xhgj.srm.request.vo.mpm.GwPlatformProductVO;
import com.xhgj.srm.request.vo.mpm.MPMAuditorVO;
import com.xhgj.srm.request.vo.mpm.MPMCategoryTaxInfo;
import com.xhgj.srm.request.vo.mpm.MPMCategoryVO;
import com.xhgj.srm.request.vo.mpm.MPMIntelligentCodeVO;
import com.xhgj.srm.request.vo.mpm.MPMPlatformVO;
import com.xhgj.srm.request.vo.mpm.MPMUnitVO;
import com.xhgj.srm.request.vo.mpm.ShiBeiProductSalePriceVO;
import com.xhgj.srm.request.vo.supplierCategory.CategoryTreeResultVO;
import com.xhiot.boot.mvc.base.PageResult;
import org.springframework.stereotype.Service;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;


public interface MPMService {

  /**
   * MPM 慧穗云 - 智能赋码 获取
   */
  List<MPMIntelligentCodeVO> intelligentCoding(String productName);

  /**
   * mpm更新导出类目/单位/品牌参考数据
   * @return
   */
  String refreshExportReferenceData();

/**
 * 品牌放弃申请
 */
  String giveUpBrandAssess(String srmBrandId);

  /**
   * 获取物料分页列表
   */
  <T> PageResult<T> getProductTablePageBySupplier(Map<String, Object> queryMap, TypeReference<PageResult<T>> typeRef);

  /**
   * 根据物料codes获取物料信息
   * @param codes 物料编码
   * @param userGroup 用户组
   * @param typeRef
   * @return
   * @param <T>
   */
  <T> List<T> getProductInfoListByCodes(List<String> codes, String userGroup ,TypeReference<List<T>> typeRef);


  /**
   * 获取物料详情数据
   */
  <T> T getProductDetail(String code, TypeReference<T> typeRef);

  /**
   * 获取计量单位列表
   *
   * @param key 名称、code、拼音
   */
  PageResult<MPMUnitVO> getUnitList(String key, Integer pageNo, Integer pageSize);

  /**
   * 获取物料审核人
   */
  List<MPMAuditorVO> getSrmProductAuditorBySrmIds(List<String> srmIds);

  /**
   * 获取MPM品牌
   */
  MdmBrand getMPMBrand(String brandCode);

  /**
   * 根据品牌id获取MPM品牌
   * @param brandId 物料ID
   * @return MdmBrandPageData
   */
  MdmBrandPageData getMPMBrandById(String brandId);
  /**
   * 根据类目code获取MPM类目编码Id
   */
  String getCategoryIdByCode(String code);

  /**
   * 根据类目code获取MPM类目编码Id
   * 不抛异常
   */
  String getCategoryIdByCodeSafe(String code);

  /**
   * 获取MPM平台
   */
  List<MPMPlatformVO> getMPMPlatform(String platformCode);

  /**
   * 获取平台列表(SRM + MPM)
   */
  List<OrderPlatformDTO> getPlatformList(List<OrderPlatformDTO> srmPlatformList);

  /**
   * mpm品牌新增
   * @param brandParamWrapper
   * @return
   */
  String brandAdd(BrandParamWrapper brandParamWrapper);

  /**
   * 获取类目数据
   * @param name
   * @param pageNo
   * @param pageSize
   * @return
   */
  PageResult<MPMCategoryVO> getCategoryListPage(String name, Integer pageNo, Integer pageSize);

  /**
   * 获取类目数据 树形
   */
  List<CategoryTreeResultVO> getCategoryListTree(String includePrefixCodes,String excludePrefixCodes);

  /**
   * 获取类目数据 树形缓存
   */
  List<CategoryTreeResultVO> getCategoryListTreeCache(String includePrefixCodes, String excludePrefixCodes);

  /**
   * 清除类目数据 树形缓存
   */
  void clearCategoryListTreeCache();

  /**
   * 通过categoryCode 获取类目数据 缓存
   * @param categoryCode
   * @return
   */
  CategoryFindDto findCategory(String categoryCode);

  /**
   * 获取国网物料调价列表
   */
  PageResult<GwPlatformProductVO> getGwPlatformProductList(GwPlatformProductSearchForm form);

  /**
   * 国网物料调价
   */
  void applyForPriceAdjustment(ApplyForPriceAdjustmentParam form);

  /**
   * 获取拾贝销售价格接口
   */
  BigDecimal getShiBeiSalePrice(MPMShiBeiQueryForm form);

  /**
   * mpm物料新增审核
   */
  String doAddProduct(JSONObject jsonObject);

  /**
   * mpm物料修改审核
   */
  String updateProduct(JSONObject jsonObject);

  /**
   * MDM查询品牌
   */
  PageResult<MdmBrandPageData> getMdmBrand(String brandName, int pageNo, int pageSize);

  /**
   * 根据根据类目编码获取类目税编信息
   */
  MPMCategoryTaxInfo getCategoryTaxInfo(String categoryCode);
}
