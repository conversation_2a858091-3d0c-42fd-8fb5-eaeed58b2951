package com.xhgj.srm.request.service.third.hZero;

import com.xhgj.srm.jpa.entity.AsmDisOrder;
import com.xhgj.srm.jpa.entity.AsmDisOrderItem;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderToFormV2;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderV2;
import com.xhgj.srm.registration.entity.EntryRegistrationEntity;
import com.xhgj.srm.request.dto.hZero.process.PaymentApplyProcessParam;
import com.xhgj.srm.request.dto.hZero.process.StartProcessParam;
import com.xhgj.srm.request.dto.hZero.process.StartProcessVo;
import com.xhgj.srm.request.dto.hZero.process.SupplierOrderReturnProcessForm.ReturnDetail;
import com.xhgj.srm.request.dto.hZero.process.SupplierOrderWarehouseApplyForm;
import java.util.List;

/**
 * @Author: fanghuanxu
 * @Date: 2025/2/25 11:34
 * @Description: h0服务
 */
public interface HZeroService {

  /**
   *启动流程实例（启动时有业务单据json信息）
   * @param param
   */
  StartProcessVo startProcessWithoutFile(StartProcessParam param);
  /**
   * 启动组装拆卸单流程
   * @param asmDisOrder 组装拆卸单
   * @param fp 成品
   * @param items 子件
   * @return
   */
  StartProcessVo startAsmDisOrderProcess(AsmDisOrder asmDisOrder, AsmDisOrderItem fp, List<AsmDisOrderItem> items);

  /**
   * 发起准入报备单流程
   * @param createMan
   * @param order
   * @return
   */
  StartProcessVo startEntryOrderProcess(User createMan, EntryRegistrationEntity order);

  /**
   * 创建入库申请单质检单生成
   */
  void createWarehouseApplyQualityCheck(List<SupplierOrderWarehouseApplyForm> forms);

  /**
   * 发起采购订单退货流程
   */
  StartProcessVo startSupplierOrderReturnProcess(SupplierOrderV2 supplierOrder,
      SupplierOrderToFormV2 returnOrderForm, List<ReturnDetail> details, User user);

  /**
   * 付款申请单流程参数
   * @return
   */
  StartProcessVo startPaymentApplyProcess(PaymentApplyProcessParam param);
}
