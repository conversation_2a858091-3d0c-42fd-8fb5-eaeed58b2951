package com.xhgj.srm.request.service.third.wms.impl;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.dtflys.forest.http.ForestResponse;
import com.xhgj.srm.common.constants.Constants_Sap;
import com.xhgj.srm.request.service.third.api.WMSApi;
import com.xhgj.srm.request.service.third.erp.sap.dto.WMSSyncAsmDisOrderParam;
import com.xhgj.srm.request.service.third.erp.sap.dto.WMSSyncInvTransferInfoReturn;
import com.xhgj.srm.request.service.third.erp.sap.dto.WMSTransferOrderParam;
import com.xhgj.srm.request.service.third.wms.WMSService;
import com.xhiot.boot.core.common.exception.CheckException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

/**
 * WMSServiceImpl
 */
@Service
@Slf4j
public class WMSServiceImpl implements WMSService {

  @Resource
  private WMSApi wmsApi;


  @Override
  public void wmsTransferOrderParam(WMSTransferOrderParam param) {
    try {
      ForestResponse<String> response = wmsApi.wmsTransferOrderParam(param);
      if (StrUtil.isBlank(response.getResult())) {
        throw new CheckException("未返回调用WMS凭证结果");
      }
      WMSSyncInvTransferInfoReturn wmsReturn =
          JSON.parseObject(response.getResult(), WMSSyncInvTransferInfoReturn.class);
      if (!StrUtil.equals(wmsReturn.getData().getType(), Constants_Sap.SUCCESS_TYPE)) {
        throw new CheckException("调用WMS凭证接口失败:" + JSON.toJSONString(wmsReturn));
      }
    } catch (Exception e) {
      log.error("调用WMS凭证接口失败",e);
      throw new CheckException("调用WMS凭证接口失败接口异常：" + ExceptionUtil.getSimpleMessage(e));
    }
  }

  @Override
  public void wmsSyncAsmDisOrderParam(WMSSyncAsmDisOrderParam param) {
    try {
      ForestResponse<String> response = wmsApi.wmsSyncAsmDisOrderParam(param);
      if (StrUtil.isBlank(response.getResult())) {
        throw new CheckException("未返回调用组装拆卸单数据同步WMS结果");
      }
      WMSSyncInvTransferInfoReturn wmsReturn =
          JSON.parseObject(response.getResult(), WMSSyncInvTransferInfoReturn.class);
      if (!StrUtil.equals(wmsReturn.getData().getType(), Constants_Sap.SUCCESS_TYPE)) {
        throw new CheckException("调用组装拆卸单数据同步WMS接口失败:" + JSON.toJSONString(wmsReturn));
      }
    } catch (Exception e) {
      log.error("调用组装拆卸单数据同步WMS接口失败",e);
      throw new CheckException("调用组装拆卸单数据同步WMS接口失败异常：" + ExceptionUtil.getSimpleMessage(e));
    }

  }
}
