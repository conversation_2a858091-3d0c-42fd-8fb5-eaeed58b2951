package com.xhgj.srm.request.service.third.dock;


import com.alibaba.fastjson.JSONArray;
import com.xhgj.srm.request.dto.dock.DownloadDeliveryDocumentForm;
import com.xhgj.srm.request.vo.dock.DownloadDeliveryDocumentVO;
import com.xhgj.srm.request.vo.dock.FileByteAndType;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface DockService {
  /**
   * 获取特殊平台下载验收单地址
   */
  List<DownloadDeliveryDocumentVO> getDownloadDeliveryDocument(DownloadDeliveryDocumentForm form);

  /**
   * 获取文件流
   * @param form
   * @return
   */
  FileByteAndType getDownloadDeliveryDocumentForByte(DownloadDeliveryDocumentForm form);

  /**
   * 获取物流状态
   * @param expressNo
   * @param expressCode
   * @param mobile
   * @return
   */
  JSONArray getLogisticsStatus(String expressNo, String expressCode, String mobile);
}
