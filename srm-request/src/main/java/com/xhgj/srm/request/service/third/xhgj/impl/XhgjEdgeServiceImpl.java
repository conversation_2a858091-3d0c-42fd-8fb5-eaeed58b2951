package com.xhgj.srm.request.service.third.xhgj.impl;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.dtflys.forest.http.ForestResponse;
import com.xhgj.srm.common.dto.MDMBankInfoDTO;
import com.xhgj.srm.request.dto.edge.CountryDTO;
import com.xhgj.srm.request.dto.edge.CountryDomain;
import com.xhgj.srm.request.dto.edge.IndustryDTO;
import com.xhgj.srm.request.dto.edge.ProvinceCityDTO;
import com.xhgj.srm.request.service.third.api.XhgjEdgeApi;
import com.xhgj.srm.request.service.third.xhgj.XhgjEdgeRequest;
import com.xhgj.srm.request.service.third.xhgj.XhgjEdgeService;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.mvc.base.ResultBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Service
@Slf4j
public class XhgjEdgeServiceImpl implements XhgjEdgeService {

  @Resource
  XhgjEdgeApi xhgjEdgeApi;
  @Resource
  ApplicationContext applicationContext;

  @Override
  @Cacheable(value = "srm.provinceCity", key = "'provinceCity'")
  public List<ProvinceCityDTO> getAllProvinceCity() {
    try {
      ResultBean<Optional<List<ProvinceCityDTO>>> result =
          xhgjEdgeApi.getAllProvinceCity().getResult();
      if (result.getCode() == ResultBean.SUCCESS) {
        return result.getData().orElse(new ArrayList<>());
      }
    } catch (Exception e) {
      log.error("边缘服务-edge-服务异常");
      throw new CheckException("边缘服务-edge-服务异常", e);
    }
    return Collections.emptyList();
  }

  @Override
  public List<CountryDTO> getAllCountry() {
    try {
      ResultBean<List<CountryDTO>> result =
          xhgjEdgeApi.getAllCountry().getResult();
      if (result.getCode() == ResultBean.SUCCESS) {
        return result.getData();
      }
    } catch (Exception e) {
      log.error("边缘服务-edge-服务异常");
      throw new CheckException("边缘服务-edge-服务异常", e);
    }
    return Collections.emptyList();
  }

  @Override
  public List<IndustryDTO> getAllIndustry() {
    try {
      ResultBean<List<IndustryDTO>> result =
          xhgjEdgeApi.getAllIndustry().getResult();
      if (result.getCode() == ResultBean.SUCCESS) {
        return result.getData();
      }
    } catch (Exception e) {
      log.error("边缘服务-edge-服务异常");
      throw new CheckException("边缘服务-edge-服务异常", e);
    }
    return Collections.emptyList();
  }

  @Override
  public Optional<CountryDomain> getCountryByName(String name) {
    try {
      ResultBean<Optional<CountryDomain>> countryByName =
          xhgjEdgeApi.getCountryByName(name).getResult();
      if (countryByName.getCode() == ResultBean.SUCCESS) {
        return countryByName.getData();
      }
    } catch (Exception e) {
      log.error("边缘服务-edge-服务异常");
      throw new CheckException("边缘服务-edge-服务异常", e);
    }
    return Optional.empty();
  }

  @Override
  public MDMBankInfoDTO pageQueryBankBranchByEdge(String bankName, String bankCode,
      String bankNameOrBankCode) {
    MDMBankInfoDTO mdmBankInfoDTO;
    bankName = StrUtil.emptyIfNull(bankName);
    bankCode = StrUtil.emptyIfNull(bankCode);
    bankNameOrBankCode = StrUtil.emptyIfNull(bankNameOrBankCode);
    try {
      mdmBankInfoDTO =
          Optional.ofNullable(xhgjEdgeApi.pageQueryBankBranchByEdge(bankName, bankCode,
              bankNameOrBankCode).getResult()).orElse(new MDMBankInfoDTO());
    } catch (Exception e) {
      log.error("边缘服务-edge-服务异常");
      throw new CheckException("边缘服务-edge-服务异常", e);
    }
    return mdmBankInfoDTO;
  }


  /**
   * 清除缓存
   */
  @Scheduled(cron = "0 0 3 * * ?")
  @CacheEvict(value = "srm.provinceCity", key = "'provinceCity'")
  public void refreshAllProvinceCity() {
    log.info("刷新省市区缓存");
  }

  public Optional<List<ProvinceCityDTO>> getAllProvinceCityWithCountry() {
    List<ProvinceCityDTO> allProvinceCity =
        applicationContext.getBean(XhgjEdgeServiceImpl.class)
            .getAllProvinceCity();
    List<ProvinceCityDTO> result = new ArrayList<>();
    ProvinceCityDTO country = new ProvinceCityDTO();
    country.setCode("1123");
    country.setChildren(allProvinceCity);
    country.setPcode(null);
    country.setName("全国");
    result.add(country);
    return Optional.of(result);
  }
}
