package com.xhgj.srm.request.service.third.oms.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.dtflys.forest.annotation.BindingVar;
import com.dtflys.forest.http.ForestResponse;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.dto.OrderLargeTicketProjectNo;
import com.xhgj.srm.common.dto.PaymentStatus;
import com.xhgj.srm.common.dto.PlatformLargeTicketProjectDTO;
import com.xhgj.srm.common.utils.dingding.DingUtils;
import com.xhgj.srm.jpa.entity.File;
import com.xhgj.srm.jpa.entity.Order;
import com.xhgj.srm.jpa.repository.FileRepository;
import com.xhgj.srm.request.dto.oms.OMSCustomerFilingSheetAddParam;
import com.xhgj.srm.request.dto.oms.OMSFilingSheetAddParam;
import com.xhgj.srm.request.dto.oms.PlatformLargeTicketParam;
import com.xhgj.srm.request.dto.supplierRate.SupplierRateBatchAddParam;
import com.xhgj.srm.request.service.third.api.OMSOrderApi;
import com.xhgj.srm.request.service.third.api.OMSPlatformApi;
import com.xhgj.srm.request.service.third.api.OMSPlatformPortalApi;
import com.xhgj.srm.request.service.third.oms.OMSService;
import com.xhgj.srm.request.vo.BaseXhgjRes;
import com.xhgj.srm.request.vo.SupplierRateDetailVO;
import com.xhgj.srm.request.vo.oms.OmsOrderCustomerInfo;
import com.xhgj.srm.request.vo.oms.OmsOrderCustomerInfo.OMSFileDto;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.config.BootConfig;
import com.xhiot.boot.mvc.base.ResultBean;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * OMS服务实现
 */
@Service
public class OMSServiceImpl implements OMSService {

  @Resource
  OMSPlatformApi omsPlatformApi;
  @Resource
  OMSPlatformPortalApi omsPlatformPortalApi;
  @Resource
  OMSOrderApi omsOrderApi;
  @Resource
  FileRepository fileRepository;
  @Resource
  BootConfig bootConfig;

  private static final String MSG_SUCCESS_EN = "success";

  private static final String MSG_SUCCESS_CN = "操作成功";

  private static final int SUCCESS_CODE = 0;


  private boolean checkFail(String msg, int code) {
    if (MSG_SUCCESS_EN.equals(msg) || MSG_SUCCESS_CN.equals(msg) || code == SUCCESS_CODE) {
      return false;
    }
    return true;
  }

  @Override
  public void filingSheetAdd(OMSFilingSheetAddParam form) {
    BaseXhgjRes<String> baseXhgjRes = omsPlatformApi.filingSheetAdd(form).getResult();
    if (baseXhgjRes == null) {
      throw new CheckException("报备单派单至履约失败");
    }
    if (checkFail(baseXhgjRes.getMsg(), baseXhgjRes.getCode())) {
      throw new CheckException("报备单派单至履约异常： " + baseXhgjRes.getMsg());
    }
  }

  @Override
  public void customerFilingSheetAdd(OMSCustomerFilingSheetAddParam form) {
    BaseXhgjRes<String> baseXhgjRes = omsPlatformApi.customerFilingSheetAdd(form).getResult();
    if (baseXhgjRes == null) {
      throw new CheckException("客户订单号报备单派单至履约失败");
    }
    if (checkFail(baseXhgjRes.getMsg(), baseXhgjRes.getCode())) {
      throw new CheckException("客户订单号报备单派单至履约异常： " + baseXhgjRes.getMsg());
    }
  }

  @Override
  public void supplierRateAdd(SupplierRateBatchAddParam form) {
    BaseXhgjRes<String> baseXhgjRes = omsPlatformApi.supplierRateAdd(form).getResult();
    if (baseXhgjRes == null) {
      throw new CheckException("同步供应商折扣比例失败");
    }
    if (checkFail(baseXhgjRes.getMsg(), baseXhgjRes.getCode())) {
      throw new CheckException("同步供应商折扣比例异常： " + baseXhgjRes.getMsg());
    }
  }

  @Override
  public String getSupplierRate(String supplierId, String price, String platform) {
    BaseXhgjRes<String> baseXhgjRes = omsPlatformApi.getSupplierRate(supplierId, price, platform).getResult();
    if (baseXhgjRes == null) {
      throw new CheckException("获取供应商折扣比例失败");
    }
    if (checkFail(baseXhgjRes.getMsg(), baseXhgjRes.getCode())) {
      throw new CheckException("获取供应商折扣比例异常： " + baseXhgjRes.getMsg());
    }
    return baseXhgjRes.getData();
  }

  @Override
  public SupplierRateDetailVO getSupplierRateDetail(String supplierId, String platform) {
    BaseXhgjRes<SupplierRateDetailVO> baseXhgjRes = omsPlatformApi.getSupplierRateDetail(supplierId, platform).getResult();
    if (baseXhgjRes == null) {
      throw new CheckException("获取供应商折扣比例详情失败");
    }
    if (checkFail(baseXhgjRes.getMsg(), baseXhgjRes.getCode())) {
      throw new CheckException("获取供应商折扣比例详情异常： " + baseXhgjRes.getMsg());
    }
    return baseXhgjRes.getData();
  }

  @Override
  public OmsOrderCustomerInfo getOmsOrderCustomerInfo(String orderNo, String platformCode) {
    BaseXhgjRes<OmsOrderCustomerInfo> result =
        omsPlatformPortalApi.getOrderCustomerInfo(orderNo, platformCode).getResult();
    if (result == null) {
      throw new CheckException("获取履约订单客户信息失败");
    }
    if (checkFail(result.getMsg(), result.getCode())) {
      throw new CheckException("获取履约订单客户信息异常： " + result.getMsg());
    }
    // 防止NULL
    return Optional.ofNullable(result.getData()).orElse(new OmsOrderCustomerInfo(new ArrayList<>()));
  }

  @Override
  public void updateOmsOrderCustomerInfo(Order order) {
    OmsOrderCustomerInfo omsOrderCustomerInfo =
        this.getOmsOrderCustomerInfo(order.getOrderNo(), order.getType());
    // 获取订单履约的相关客户信息文件
    List<File> fileList = fileRepository.findAllByRelationIdAndRelationTypeAndState(order.getId(),
        Constants.FILE_TYPE_ORDER_CUSTOMER_INFO, Constants.STATE_OK).orElse(new ArrayList<>());
    List<String> originUrls = fileList.stream().map(File::getUrl).collect(Collectors.toList());
    List<OMSFileDto> customerFileList = omsOrderCustomerInfo.getCustomerFileList();
    // 做patch更新
    // 筛选出需要新增的文件，根据文件路径区分是否需要新增
    List<OMSFileDto> addList = customerFileList.stream()
        .filter(file -> !originUrls.contains(file.getFileUrl())).collect(Collectors.toList());
    // 筛选出需要删除的文件，根据文件路径区分是否需要删除
    List<File> deleteList = fileList.stream()
        .filter(file -> customerFileList.stream().noneMatch(omsFileDto -> omsFileDto.getFileUrl().equals(file.getUrl())))
        .collect(Collectors.toList());
    // 新增文件
    if (CollUtil.isNotEmpty(addList)) {
      List<File> addOnes = addList.stream().map(item -> {
        File file = new File();
        file.setRelationType(Constants.FILE_TYPE_ORDER_CUSTOMER_INFO);
        file.setRelationId(order.getId());
        file.setName(item.getFileName());
        file.setUrl(item.getFileUrl());
//        file.setType();
        file.setCreateTime(System.currentTimeMillis());
        file.setState(Constants.STATE_OK);
        file.setDescription(file.getName());
        return file;
      }).collect(Collectors.toList());
      fileRepository.saveAll(addOnes);
    }
    // 删除文件
    if (CollUtil.isNotEmpty(deleteList)) {
      deleteList.forEach(file -> {
        file.setState(Constants.STATE_DELETE);
      });
      fileRepository.saveAll(deleteList);
    }
  }

  @Override
  public List<OrderLargeTicketProjectNo> getOrderLargeTicketProjectNo(String orderNo, String dockingOrderType) {
    ForestResponse<ResultBean<List<OrderLargeTicketProjectNo>>> forestResponse =
        omsPlatformApi.getOrderLargeTicketProjectNo(orderNo, dockingOrderType);
    if (forestResponse == null) {
      throw new CheckException("获取订单大票号码失败");
    }
    ResultBean<List<OrderLargeTicketProjectNo>> result = forestResponse.getResult();
    if (result == null) {
      throw new CheckException("获取订单大票号码失败");
    }
    if (checkFail(result.getMsg(), result.getCode())) {
      throw new CheckException("获取订单大票号码异常： " + result.getMsg());
    }
    return result.getData();
  }

  @Override
  public String getOrderLargeTicketProjectNoOld(String orderNo, String dockingOrderType) {
    ForestResponse<ResultBean<String>> forestResponse = omsOrderApi.getOrderLargeTicketProjectNo(orderNo, dockingOrderType);
    if (forestResponse == null) {
      throw new CheckException("获取订单大票旧履约号码失败");
    }
    ResultBean<String> result = forestResponse.getResult();
    if (result == null) {
      throw new CheckException("获取订单大票旧履约号码失败");
    }
    if (checkFail(result.getMsg(), result.getCode())) {
      throw new CheckException("获取订单大票旧履约号码异常： " + result.getMsg());
    }
    return result.getData();
  }

  @Override
  public List<PaymentStatus> getOrderCustomerReturnStatus(String orderNo, String platformCode, String supplierOrderId) {
    ForestResponse<ResultBean<List<PaymentStatus>>> forestResponse = omsPlatformApi.getOrderCustomerReturnStatus(orderNo, platformCode, supplierOrderId);
    if (forestResponse == null) {
      throw new CheckException("获取订单回款状态信息失败");
    }
    ResultBean<List<PaymentStatus>> result = forestResponse.getResult();
    if (result == null) {
      this.sendOrderCustomerReturnStatusWarningMessage(forestResponse.getRequest().getUrl(), JSON.toJSONString(result));
      throw new CheckException("获取订单回款状态信息失败");
    }
    if (checkFail(result.getMsg(), result.getCode())) {
      this.sendOrderCustomerReturnStatusWarningMessage(forestResponse.getRequest().getUrl(), JSON.toJSONString(result));
      throw new CheckException("获取订单回款状态信息异常： " + result.getMsg());
    }
    return result.getData();
  }

  private void sendOrderCustomerReturnStatusWarningMessage(String requestMessage,
      String responseMessage) {
    String env = bootConfig.getEnv();
    DingUtils.sendMsgByWarningRobot(
        "【" + env + "环境 " + bootConfig.getAppName() + "】调用履约平台查询订单回款状态接口异常："
            + "请求参数：【"+ requestMessage +"】"
            + "履约平台返回:" + responseMessage + "，请及时处理！", env);
  }

  public  List<PlatformLargeTicketProjectDTO>  getLargeTicketInfoByProjectNoList(PlatformLargeTicketParam param) {
    BaseXhgjRes<List<PlatformLargeTicketProjectDTO>> baseXhgjRes =
        omsPlatformApi.getLargeTicketInfoByProjectNoList(param).getResult();
    if (baseXhgjRes == null) {
      throw new CheckException("履约获取大票接口失败");
    }
    if (checkFail(baseXhgjRes.getMsg(), baseXhgjRes.getCode())) {
      throw new CheckException("履约获取大票接口异常： " + baseXhgjRes.getMsg());
    }
    return Optional.ofNullable(baseXhgjRes.getData()).orElse(new ArrayList<>());
  }
}

