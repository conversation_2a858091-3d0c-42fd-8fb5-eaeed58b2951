package com.xhgj.srm.request.service.third.mpm.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dtflys.forest.http.ForestResponse;
import com.xhgj.srm.common.dto.MdmBrand;
import com.xhgj.srm.common.dto.MdmBrandPageData;
import com.xhgj.srm.common.dto.OrderPlatformDTO;
import com.xhgj.srm.jpa.dao.SupplierPerformanceDao;
import com.xhgj.srm.request.dto.mpm.BrandParamWrapper;
import com.xhgj.srm.request.dto.mpm.GwPlatformProductSearchForm;
import com.xhgj.srm.request.dto.mpm.MPMShiBeiQueryForm;
import com.xhgj.srm.request.dto.product.ApplyForPriceAdjustmentParam;
import com.xhgj.srm.request.dto.supplierCategory.CategoryFindDto;
import com.xhgj.srm.request.factory.MapStructFactory;
import com.xhgj.srm.request.service.third.api.MPMApi;
import com.xhgj.srm.request.service.third.mpm.MPMService;
import com.xhgj.srm.request.vo.BaseXhgjListRes;
import com.xhgj.srm.request.vo.BaseXhgjRes;
import com.xhgj.srm.request.vo.mdm.MdmBrandPageDataDto;
import com.xhgj.srm.request.vo.mpm.GwPlatformProductVO;
import com.xhgj.srm.request.vo.mpm.MPMAuditorVO;
import com.xhgj.srm.request.vo.mpm.MPMCategoryTaxInfo;
import com.xhgj.srm.request.vo.mpm.MPMCategoryVO;
import com.xhgj.srm.request.vo.mpm.MPMIntelligentCodeVO;
import com.xhgj.srm.request.vo.mpm.MPMPlatformVO;
import com.xhgj.srm.request.vo.mpm.MPMUnitVO;
import com.xhgj.srm.request.vo.supplierCategory.CategoryTreeResultVO;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.StringUtils;
import com.xhiot.boot.mvc.base.PageResult;
import com.xhiot.boot.mvc.base.ResultBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Queue;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 对接mpm接口业务方法
 */
@Service
@Slf4j
public class MPMServiceImpl implements MPMService {
  @Resource
  MPMApi mpmApi;
  @Resource
  SupplierPerformanceDao supplierPerformanceDao;
  @Resource
  ApplicationContext applicationContext;

  private static final String MSG_SUCCESS_EN = "success";

  private static final String MSG_SUCCESS_CN = "操作成功";

  private static final int SUCCESS_CODE = 0;


  private boolean checkFail(String msg, int code) {
    if (MSG_SUCCESS_EN.equals(msg) || MSG_SUCCESS_CN.equals(msg) || code == SUCCESS_CODE) {
      return false;
    }
    return true;
  }

  /**
   *  MPM 慧穗云 - 智能赋码 获取
   * @param productName
   * @return
   */
  @Override
  public List<MPMIntelligentCodeVO> intelligentCoding(String productName) {
    ForestResponse<ResultBean<List<MPMIntelligentCodeVO>>> forestResponse = mpmApi.intelligentCoding(productName);
    ResultBean<List<MPMIntelligentCodeVO>> result = forestResponse.getResult();
    // 判断是否有数据
    if (result == null) {
      throw new CheckException("未查询到物料智能赋码信息");
    }
    if (checkFail(result.getMsg(), result.getCode())) {
      throw new CheckException("未查询到物料智能赋码信息：" + result.getMsg());
    }
    if (result.getData() == null) {
      throw new CheckException("未查询到物料智能赋码信息");
    }
    return result.getData();
  }

  /**
   * mpm更新导出类目/单位/品牌参考数据
   * @return
   */
  @Override
  public String refreshExportReferenceData() {
    return mpmApi.refreshExportReferenceData();
  }

  /**
   * 品牌放弃申请
   * @param srmBrandId
   * @return
   */
  @Override
  public String giveUpBrandAssess (String srmBrandId) {
    return mpmApi.giveUpBrandAssess(srmBrandId);
  }

  /**
   * 获取物料分页列表
   * @param queryMap
   * @param typeRef
   * @return
   * @param <T>
   */
  @Override
  public <T> PageResult<T> getProductTablePageBySupplier(Map<String, Object> queryMap,
      TypeReference<PageResult<T>> typeRef) {
    String result = mpmApi.getProductTablePageBySupplier(queryMap).getResult();
    if (StrUtil.isBlank(result)) {
      throw new CheckException("未查询到物料信息");
    }
    // JSON转换
    BaseXhgjRes<T> baseXhgjRes = JSON.parseObject(result, BaseXhgjRes.class);
    if (baseXhgjRes == null) {
      throw new CheckException("未查询到物料信息");
    }
    if (checkFail(baseXhgjRes.getMsg(), baseXhgjRes.getCode())) {
      throw new CheckException("查询物料信息异常： " + baseXhgjRes.getMsg());
    }
    if (baseXhgjRes.getData() == null) {
      throw new CheckException("未查询到物料信息");
    }
    // JSON转换
    PageResult<T> pageResult = JSON.parseObject(JSON.toJSONString(baseXhgjRes.getData()), typeRef);
    if (pageResult == null) {
      throw new CheckException("未查询到物料信息");
    }
    return pageResult;
  }

  @Override
  public <T> List<T> getProductInfoListByCodes(List<String> codes, String userGroup,
      TypeReference<List<T>> typeRef) {
    if (CollUtil.isEmpty(codes)) {
      return Collections.emptyList();
    }
    return getProductInfoListByCodes(codes, userGroup, "0", typeRef);
  }

  /**
   * 根据物料codes获取物料信息
   * @param codes 物料编码
   * @param userGroup 用户组
   * @param isDisable 是否禁用 0-启用 1-禁用 默认只查启用
   * @param typeRef 类型
   * @return
   * @param <T>
   */
  public <T> List<T> getProductInfoListByCodes(List<String> codes, String userGroup, String isDisable, TypeReference<List<T>> typeRef) {
    ResultBean<String> resultBean = mpmApi.getProductInfoByUserCodes(codes, userGroup, isDisable).getResult();
    if (checkFail(resultBean.getMsg(), resultBean.getCode())) {
      throw new CheckException("查询物料信息异常： " + resultBean.getMsg());
    }
    if (StrUtil.isBlank(resultBean.getData())) {
      throw new CheckException("未查询到物料信息");
    }
    String data = resultBean.getData();
    // JSON转换
    return JSON.parseObject(data, typeRef);
  }

  /**
   * 获取物料详情数据
   * @param code
   * @param typeRef
   * @return
   * @param <T>
   */
  @Override
  public <T> T getProductDetail(String code, TypeReference<T> typeRef) {
    String result = mpmApi.getProductDetail(code).getResult();
    if (StrUtil.isBlank(result)) {
      throw new CheckException("未查询到物料信息");
    }
    // JSON转换
    BaseXhgjRes<T> baseXhgjRes = JSON.parseObject(result, BaseXhgjRes.class);
    if (baseXhgjRes == null) {
      throw new CheckException("未查询到物料信息");
    }
    if (checkFail(baseXhgjRes.getMsg(), baseXhgjRes.getCode())) {
      throw new CheckException("查询物料信息异常： " + baseXhgjRes.getMsg());
    }
    if (baseXhgjRes.getData() == null) {
      throw new CheckException("未查询到物料信息");
    }
    T res = JSON.parseObject(JSON.toJSONString(baseXhgjRes.getData()), typeRef);
    if (res == null) {
      throw new CheckException("未查询到物料信息");
    }
    return res;
  }

  /**
   * 获取计量单位列表
   * @param key 名称、code、拼音
   * @return
   */
  @Override
  public PageResult<MPMUnitVO> getUnitList(String key, Integer pageNo, Integer pageSize) {
    String result = mpmApi.getUnitList(key, pageNo, pageSize).getResult();
    if (StrUtil.isBlank(result)) {
      throw new CheckException("未查询到计量单位信息");
    }
    // JSON转换
    BaseXhgjRes<PageResult<MPMUnitVO>> baseXhgjRes = JSON.parseObject(result, new TypeReference<BaseXhgjRes<PageResult<MPMUnitVO>>>(){});
    if (baseXhgjRes == null) {
      throw new CheckException("未查询到计量单位信息");
    }
    if (checkFail(baseXhgjRes.getMsg(), baseXhgjRes.getCode())) {
      throw new CheckException("查询计量单位信息异常： " + baseXhgjRes.getMsg());
    }
    if (baseXhgjRes.getData() == null) {
      throw new CheckException("未查询到计量单位信息");
    }
    return baseXhgjRes.getData();
  }

  /**
   * 获取物料审核人
   */
  @Override
  public List<MPMAuditorVO> getSrmProductAuditorBySrmIds(List<String> srmIds) {
    if (CollUtil.isEmpty(srmIds)) {
      return new ArrayList<>();
    }
    Map<String, Object> map = new HashMap();
    map.put("ids", srmIds);
    ForestResponse<String> srmProductAuditorBySrmIds = mpmApi.getSrmProductAuditorBySrmIds(map);
    if (StrUtil.isBlank(srmProductAuditorBySrmIds.getResult())) {
      throw new CheckException("未查询到物料审核人信息");
    }
    // JSON转换
    BaseXhgjListRes<MPMAuditorVO> baseXhgjListRes = JSON.parseObject(srmProductAuditorBySrmIds.getResult(), new TypeReference<BaseXhgjListRes<MPMAuditorVO>>() {});
    if (baseXhgjListRes == null) {
      throw new CheckException("未查询到物料审核人信息");
    }
    if (checkFail(baseXhgjListRes.getMsg(), baseXhgjListRes.getCode())) {
      throw new CheckException("查询物料审核人信息异常： " + baseXhgjListRes.getMsg());
    }
    if (baseXhgjListRes.getData() == null) {
      throw new CheckException("未查询到物料审核人信息");
    }
    return baseXhgjListRes.getData();
  }

  /**
   * 获取MPM品牌
   */
  @Override
  public MdmBrand getMPMBrand(String brandCode) {
    String result = mpmApi.getBrandCode(brandCode).getResult();
    if (StrUtil.isBlank(result)) {
      throw new CheckException("未查询到品牌信息");
    }
    // JSON转换
    BaseXhgjRes<MdmBrand> baseXhgjRes = JSON.parseObject(result, new TypeReference<BaseXhgjRes<MdmBrand>>() {});
    if (baseXhgjRes == null) {
      throw new CheckException("未查询到品牌信息");
    }
    if (checkFail(baseXhgjRes.getMsg(), baseXhgjRes.getCode())) {
      throw new CheckException("查询品牌信息异常： " + baseXhgjRes.getMsg());
    }
    if (baseXhgjRes.getData() == null) {
      throw new CheckException("未查询到品牌信息");
    }
    baseXhgjRes.getData().setCode(brandCode);
    return baseXhgjRes.getData();
  }

  /**
   * 获取MPM品牌
   */
  @Override
  public MdmBrandPageData getMPMBrandById(String brandId) {
    String result = mpmApi.getBrandCodeById(brandId).getResult();
    if (StrUtil.isBlank(result)) {
      throw new CheckException("未查询到品牌信息");
    }
    // JSON转换
    BaseXhgjRes<MdmBrandPageData> baseXhgjRes = JSON.parseObject(result, new TypeReference<BaseXhgjRes<MdmBrandPageData>>() {});

    if (baseXhgjRes == null) {
      throw new CheckException("未查询到品牌信息");
    }
    if (checkFail(baseXhgjRes.getMsg(), baseXhgjRes.getCode())) {
      throw new CheckException("查询品牌信息异常： " + baseXhgjRes.getMsg());
    }
    if (baseXhgjRes.getData() == null) {
      throw new CheckException("未查询到品牌信息");
    }

    return baseXhgjRes.getData();
  }


  /**
   * 根据类目code获取MPM类目编码Id
   * @param code
   * @return
   */
  @Override
  public String getCategoryIdByCode(String code) {
    String result = mpmApi.getCategoryIdByCategoryCode(code).getResult();
    if (StrUtil.isBlank(result)) {
      throw new CheckException("未查询到类目信息");
    }
    // JSON转换
    BaseXhgjRes<String> baseXhgjRes = JSON.parseObject(result, new TypeReference<BaseXhgjRes<String>>() {});
    if (baseXhgjRes == null) {
      throw new CheckException("未查询到类目信息");
    }
    if (checkFail(baseXhgjRes.getMsg(), baseXhgjRes.getCode())) {
      throw new CheckException("查询类目信息异常： " + baseXhgjRes.getMsg());
    }
    if (baseXhgjRes.getData() == null) {
      throw new CheckException("未查询到类目信息");
    }
    return baseXhgjRes.getData();
  }

  /**
   * 根据类目code获取MPM类目编码Id
   * 不抛异常
   * @param code
   * @return
   */
  @Override
  public String getCategoryIdByCodeSafe(String code) {
    String result = mpmApi.getCategoryIdByCategoryCode(code).getResult();
    if (StrUtil.isBlank(result)) {
      return null;
    }
    // JSON转换
    BaseXhgjRes<String> baseXhgjRes = JSON.parseObject(result, new TypeReference<BaseXhgjRes<String>>() {});
    if (baseXhgjRes == null) {
      return null;
    }
    if (checkFail(baseXhgjRes.getMsg(), baseXhgjRes.getCode())) {
      return null;
    }
    if (baseXhgjRes.getData() == null) {
      return null;
    }
    return baseXhgjRes.getData();
  }

  /**
   * 获取MPM平台
   * @return
   */
  @Override
  public List<MPMPlatformVO> getMPMPlatform(String platformCode) {
    ForestResponse<String> result = mpmApi.getPlatformAndDockingStatus(platformCode);
    if (result == null) {
      throw new CheckException("未查询到平台信息");
    }
    BaseXhgjListRes<MPMPlatformVO> baseXhgjListRes = JSON.parseObject(result.getResult(), new TypeReference<BaseXhgjListRes<MPMPlatformVO>>() {});
    if (baseXhgjListRes == null) {
      throw new CheckException("未查询到平台信息");
    }
    if (checkFail(baseXhgjListRes.getMsg(), baseXhgjListRes.getCode())) {
      throw new CheckException("查询平台信息异常： " + baseXhgjListRes.getMsg());
    }
    if (baseXhgjListRes.getData() == null) {
      throw new CheckException("未查询到平台信息");
    }
    return baseXhgjListRes.getData();
  }

  @Override
  public List<OrderPlatformDTO> getPlatformList(List<OrderPlatformDTO> srmPlatformList) {
    Set<String> existingCodes = srmPlatformList.stream()
        .map(OrderPlatformDTO::getPlatformCode)
        .collect(Collectors.toSet()); // 记录已有的code
    List<MPMPlatformVO> mpmPlatform =
        this.getMPMPlatform(null).stream().filter(item -> !Objects.isNull(item.getCode())).collect(
            Collectors.toList());
    Map<String, String> code2MPMPlatformVO = mpmPlatform.stream()
        .collect(Collectors.toMap(MPMPlatformVO::getCode, MPMPlatformVO::getDesc));
    // 去重
    for (MPMPlatformVO mpmPlatformVO : mpmPlatform) {
      if (StrUtil.isNotBlank(mpmPlatformVO.getCode()) && StringUtils.isNullOrEmpty(
          mpmPlatformVO.getDockingStatus()) && !existingCodes.contains(mpmPlatformVO.getCode())) {
        OrderPlatformDTO orderPlatformDTO = new OrderPlatformDTO();
        orderPlatformDTO.setPlatformCode(mpmPlatformVO.getCode());
        orderPlatformDTO.setPlatformName(mpmPlatformVO.getName());
        orderPlatformDTO.setPushRequirements(mpmPlatformVO.getDesc());
        srmPlatformList.add(orderPlatformDTO);
      }
    }
    // 设置推送
    srmPlatformList.forEach(item -> {
      item.setPushRequirements(code2MPMPlatformVO.get(item.getPlatformCode()));
    });
    return srmPlatformList;
  }

  @Override
  public String brandAdd(BrandParamWrapper brandParamWrapper) {
    ForestResponse<String> result = mpmApi.addBrand(brandParamWrapper);
    if (result == null) {
      throw new CheckException("新增品牌失败");
    }
    BaseXhgjRes<String> baseXhgjListRes = JSON.parseObject(result.getResult(), new TypeReference<BaseXhgjRes<String>>() {});
    if (baseXhgjListRes == null) {
      throw new CheckException("新增品牌失败");
    }
    if (checkFail(baseXhgjListRes.getMsg(), baseXhgjListRes.getCode())) {
      throw new CheckException("新增品牌失败异常： " + baseXhgjListRes.getMsg());
    }
    return baseXhgjListRes.getMsg();
  }

  @Override
  public PageResult<MPMCategoryVO> getCategoryListPage(String name, Integer pageNo, Integer pageSize) {
    String result = mpmApi.getCategoryList(name, pageNo, pageSize).getResult();
    if (StrUtil.isBlank(result)) {
      throw new CheckException("未查询到类目信息");
    }
    // JSON转换
    BaseXhgjRes<PageResult<MPMCategoryVO>> baseXhgjRes = JSON.parseObject(result, new TypeReference<BaseXhgjRes<PageResult<MPMCategoryVO>>>(){});
    if (baseXhgjRes == null) {
      throw new CheckException("未查询到类目信息");
    }
    if (checkFail(baseXhgjRes.getMsg(), baseXhgjRes.getCode())) {
      throw new CheckException("查询到类目信息异常： " + baseXhgjRes.getMsg());
    }
    if (baseXhgjRes.getData() == null) {
      throw new CheckException("未查询到类目信息");
    }
    return baseXhgjRes.getData();
  }

  @Override
  public List<CategoryTreeResultVO> getCategoryListTree(String includePrefixCodes,String excludePrefixCodes) {
    ForestResponse<ResultBean<List<CategoryTreeResultVO>>> result = mpmApi.getCategoryTree("", "");
    if (result == null) {
      throw new CheckException("未查询到类目信息");
    }
    ResultBean<List<CategoryTreeResultVO>> resultBean = result.getResult();
    if (resultBean == null) {
      throw new CheckException("未查询到类目信息");
    }
    if (checkFail(resultBean.getMsg(), resultBean.getCode())) {
      throw new CheckException("查询到类目信息异常： " + resultBean.getMsg());
    }
    if (resultBean.getData() == null) {
      throw new CheckException("未查询到类目信息");
    }
    return resultBean.getData();
  }

  @Override
  @Cacheable(value = "srm.categoryListTree", key = "#includePrefixCodes + #excludePrefixCodes")
  public List<CategoryTreeResultVO> getCategoryListTreeCache(String includePrefixCodes, String excludePrefixCodes) {
    return this.getCategoryListTree(includePrefixCodes, excludePrefixCodes);
  }

  @Override
  @CacheEvict(value = "srm.categoryListTree", allEntries = true)
  public void clearCategoryListTreeCache() {
    log.info("清除类目数据树形缓存");
    MPMServiceImpl proxy = applicationContext.getBean(MPMServiceImpl.class);
    proxy.clearCategoryFindDto();
  }

  @Override
  @Cacheable(value = "srm.categoryFind", key = "#categoryCode")
  public CategoryFindDto findCategory(String categoryCode) {
    MPMServiceImpl proxy = applicationContext.getBean(MPMServiceImpl.class);
    List<CategoryTreeResultVO> categoryListTreeCache = proxy.getCategoryListTreeCache("", "");
    return bfsFindByCode(categoryListTreeCache, categoryCode);
  }

  @Override
  public PageResult<GwPlatformProductVO> getGwPlatformProductList(GwPlatformProductSearchForm form) {
    ForestResponse<ResultBean<PageResult<GwPlatformProductVO>>> result = mpmApi.getGwPlatformProductList(form);
    if (result == null) {
      throw new CheckException("未查询到国网物料信息");
    }
    ResultBean<PageResult<GwPlatformProductVO>> resultBean = result.getResult();
    if (resultBean == null) {
      throw new CheckException("未查询到国网物料信息");
    }
    if (checkFail(resultBean.getMsg(), resultBean.getCode())) {
      throw new CheckException("查询国网物料信息异常： " + resultBean.getMsg());
    }
    if (resultBean.getData() == null) {
      throw new CheckException("未查询到国网物料信息");
    }
    return resultBean.getData();
  }

  @Override
  public void applyForPriceAdjustment(ApplyForPriceAdjustmentParam form) {
    ForestResponse<ResultBean<String>> result = mpmApi.applyForPriceAdjustment(form);
    if (result == null) {
      throw new CheckException("国网物料调价失败");
    }
    ResultBean<String> resultBean = result.getResult();
    if (resultBean == null) {
      throw new CheckException("国网物料调价失败");
    }
    if (checkFail(resultBean.getMsg(), resultBean.getCode())) {
      throw new CheckException("国网物料调价失败异常： " + resultBean.getMsg());
    }
  }

  @Override
  public BigDecimal getShiBeiSalePrice(MPMShiBeiQueryForm form) {
    ForestResponse<ResultBean<BigDecimal>> result = mpmApi.getShiBeiSalePrice(form.toQueryMap());
    if (result == null) {
      throw new CheckException("未查询到拾贝销售价格信息");
    }
    ResultBean<BigDecimal> resultBean = result.getResult();
    if (resultBean == null) {
      throw new CheckException("未查询到拾贝销售价格信息");
    }
    if (checkFail(resultBean.getMsg(), resultBean.getCode())) {
      throw new CheckException("查询拾贝销售价格信息异常： " + resultBean.getMsg());
    }
    if (resultBean.getData() == null) {
      throw new CheckException("未查询到拾贝销售价格信息");
    }
    return resultBean.getData();
  }

  @Override
  public String doAddProduct(JSONObject jsonObject) {
    ForestResponse<BaseXhgjRes<String>> forestResponse = mpmApi.doAddProduct(jsonObject);
    BaseXhgjRes<String> result = forestResponse.getResult();
    if (result == null) {
      throw new CheckException("新增商品至mdm失败，返回结果为空");
    }
    if (checkFail(result.getMsg(), result.getCode())) {
      throw new CheckException("新增商品至mdm失败，" + result.getMsg());
    }
    return result.getData();
  }

  @Override
  public String updateProduct(JSONObject jsonObject) {
    ForestResponse<BaseXhgjRes<String>> forestResponse = mpmApi.updateProduct(jsonObject);
    BaseXhgjRes<String> result = forestResponse.getResult();
    if (result == null) {
      throw new CheckException("修改商品至mdm失败，返回结果为空");
    }
    if (checkFail(result.getMsg(), result.getCode())) {
      throw new CheckException("修改商品至mdm失败，" + result.getMsg());
    }
    return result.getData();
  }

  @Override
  public PageResult<MdmBrandPageData> getMdmBrand(String brandName, int pageNo, int pageSize) {
    ForestResponse<ResultBean<PageResult<MdmBrandPageDataDto>>> forestResponse = mpmApi.searchByName(brandName, pageNo, pageSize);
    ResultBean<PageResult<MdmBrandPageDataDto>> result = forestResponse.getResult();
    if (result == null) {
      throw new CheckException("查询品牌失败，返回结果为空");
    }
    if (checkFail(result.getMsg(), result.getCode())) {
      throw new CheckException("查询品牌失败，" + result.getMsg());
    }
    PageResult<MdmBrandPageDataDto> data = result.getData();
    List<MdmBrandPageDataDto> content = data.getContent();
    List<MdmBrandPageData> dtoResult = content.stream().map(
            MapStructFactory.INSTANCE::toMdmBrandPageData)
        .collect(Collectors.toList());
    // 转换为PageResult<MdmBrandPageData>
    return new PageResult<>(dtoResult, data.getTotalCount(),
        data.getTotalPages(), data.getPageNo(), data.getPageSize());
  }

  @Override
  public MPMCategoryTaxInfo getCategoryTaxInfo(String categoryCode) {
    ForestResponse<ResultBean<MPMCategoryTaxInfo>> forestResponse = mpmApi.getCategoryTaxInfoByCode(categoryCode);
    ResultBean<MPMCategoryTaxInfo> result = forestResponse.getResult();
    if (result == null) {
      throw new CheckException("查询类目税编信息失败，返回结果为空");
    }
    if (checkFail(result.getMsg(), result.getCode())) {
      throw new CheckException("查询类目税编信息失败，" + result.getMsg());
    }
    MPMCategoryTaxInfo data = result.getData();
    if (data == null) {
      throw new CheckException("查询类目税编信息失败，返回结果为空");
    }
    return data;
  }

  /**
   * 清除categoryCode 获取类目数据 缓存
   */
  @CacheEvict(value = "srm.categoryFind", allEntries = true)
  public void clearCategoryFindDto() {
    log.info("清除categoryCode 获取类目数据 缓存");
  }

  /**
   * 广度优先搜索
   * @param resultVOS
   * @param categoryCode
   * @return
   */
  private CategoryFindDto bfsFindByCode(List<CategoryTreeResultVO> resultVOS, String categoryCode) {
    Queue<CategoryTreeResultVO> queue = new LinkedList<>();
    Queue<List<String>> nameQueue = new LinkedList<>();  // 用来保存每个节点的路径
    Queue<List<String>> pathQueue = new LinkedList<>();
    // 将根节点加入队列
    for (CategoryTreeResultVO category : resultVOS) {
      queue.add(category);
      nameQueue.add(Collections.singletonList(category.getName()));  // 初始路径为当前节点的 code
      pathQueue.add(Collections.singletonList(category.getPrefixCode()));
    }

    // 广度优先遍历
    while (!queue.isEmpty()) {
      CategoryTreeResultVO current = queue.poll();
      List<String> currentName = nameQueue.poll();
      List<String> currentPath = pathQueue.poll();

      // 如果当前节点的 code 和目标 code 匹配，返回该节点和路径
      if (current.getPrefixCode().equals(categoryCode)) {
        CategoryFindDto result = new CategoryFindDto();
        result.setCode(current.getPrefixCode());
        result.setName(current.getName());
        result.setPath(currentPath);
        result.setNames(currentName);
        return result;
      }
      // 如果当前节点有子节点，将其子节点加入队列，同时更新路径
      if (current.getChildList() != null) {
        for (CategoryTreeResultVO category : current.getChildList()) {
          assert currentPath != null;
          List<String> newPath = new ArrayList<>(currentPath);
          assert currentName != null;
          List<String> newName = new ArrayList<>(currentName);
          // 去掉前缀
          String prefixCode = current.getPrefixCode();
          String code = category.getPrefixCode().replace(prefixCode, "");
          newPath.add(code);  // 将子节点的 code 加入路径
          newName.add(category.getName()); // 将子节点的 name 加入路径
          queue.add(category);
          pathQueue.add(newPath);
          nameQueue.add(newName);
        }
      }
    }
    // 如果没有找到目标 code，返回 null
    return null;
  }
}
