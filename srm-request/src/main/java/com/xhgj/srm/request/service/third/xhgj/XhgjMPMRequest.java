package com.xhgj.srm.request.service.third.xhgj;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.request.config.AbstractXhgjConfig;
import com.xhgj.srm.request.config.XhgjMPMConfig;
import com.xhgj.srm.request.dto.mpm.BrandParamWrapper;
import com.xhgj.srm.request.dto.mpm.BrandResult;
import com.xhgj.srm.request.dto.mpm.MpmBrandDTO;
import com.xhgj.srm.request.dto.mpm.ProductResultDTO;
import com.xhgj.srm.request.dto.mpm.ShiBeiProductInfoDTO;
import com.xhgj.srm.request.dto.mpm.UnitResult;
import com.xhiot.boot.core.common.util.StringUtils;
import com.xhiot.boot.mvc.base.ResultBean;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2023-06-04 14:58
 */
@Component
@Slf4j
public class XhgjMPMRequest implements BaseXhgjRequest {

  @Autowired
  private XhgjMPMConfig xhgjMPMConfig;

  @Override
  public AbstractXhgjConfig getConfig() {
    return xhgjMPMConfig;
  }


  @Override
  public Logger getLogger() {
    return log;
  }

  /** 根据code获取商品主图 */
  public final static String GET_PRODUCT_ADMIN_PIC_BY_CODE = "/product/getProductAdminPicByCode";

  /** mpm新增品牌 */
  public final static String MPM_ADD_BRAND = "/brand/doAddBrand";

  /** 获取mpm品牌详情 */
  public final static String MPM_GET_BRAND_DETAIL = "";

  /** 获取mpm品牌详情 */
  public final static String MPM_GET_BRAND_PAGE = "/brand/searchByName";
  /** 更新禁止推送状态 */
  public final static String UPDATE_NO_PUSH_PLATFORM = "/product/updateNoPushPlatform";
  /**
   * 获取物料描述字段
   */
  public final static String GET_PRODUCT_DESCRIPTION = "/product/getProductDescription";



  /** 获取mpm拾贝批次id **/
  public final static String MPM_GET_SHIBEI_BATCHID = "/product/shiBeiUrlHandel";
  /**
   * 获取mpm拾贝物料信息
   */
  public final static String MPM_GET_SHIBEI_PRODUCTINFO = "/product/getShiBeiProductInfo";
  /**
   * 查询计量单位
   */
  public final String MPM_FIND_UNIT = "/unit/findByCodeOrName";

  /**
   * 获取mpm物料详情
   */
  public final static String MPM_FIND_DETAIL = "/product/getProductDetailByCodeNew";

  /**
   * 根据code获取物料描述字段
   * @param code 商品 code
   * @return
   */
  public String getProductDescription(String code){
    if (StrUtil.isNotBlank(code)) {
      String result = get(GET_PRODUCT_DESCRIPTION,
          MapUtil.builder(new HashMap<String, Object>()).put("code", code).build());
      return resolveResult(result,new TypeReference<ResultBean<String>>(){}).orElse(StrUtil.EMPTY);
    }
    return StrUtil.EMPTY;
  }

  /**
   * 根据商品url 获取拾贝批次id
   * @param url
   * @return
   */
  public ShiBeiProductInfoDTO getShiBeiBatchId(String url){
    if (StrUtil.isNotBlank(url)) {
      String result = null;
      try {
        result = post(MPM_GET_SHIBEI_BATCHID,
            MapUtil.builder(new HashMap<String, Object>()).put("url", url).build());
      } catch (Exception e) {
        log.warn("调用mpm 拾贝 异常！url:{}",url);
        log.warn("调用mpm 拾贝获取批次id 异常！:{}",e.getMessage());
      }
      return resolveResult(result,new TypeReference<ResultBean<ShiBeiProductInfoDTO>>(){}).orElse(null);
    }
    return null;
  }

  public ShiBeiProductInfoDTO getShiBeiProductInfo(String batchId){
    if (StrUtil.isNotBlank(batchId)) {
      String result = null;
      try {
        result = get(MPM_GET_SHIBEI_PRODUCTINFO,
            MapUtil.builder(new HashMap<String, Object>()).put("batchId", batchId).build());
      } catch (Exception e) {
        log.warn("调用mpm 拾贝 异常！batchId:{}",batchId);
        log.warn("调用mpm 拾贝获取产品信息 异常！:{}",e.getMessage());
      }
      return resolveResult(result,new TypeReference<ResultBean<ShiBeiProductInfoDTO>>(){}).orElse(null);
    }
    return null;
  }

  /**
   * 根据code获取商品主图
   * @param code 商品 code
   * @return
   */
  public String getProductAdminPicByCode(String code){
    if (StrUtil.isNotBlank(code)) {
      String result = get(GET_PRODUCT_ADMIN_PIC_BY_CODE,
          MapUtil.builder(new HashMap<String, Object>()).put("code", code).build());
      return resolveResult(result,new TypeReference<ResultBean<String>>(){}).orElse(StrUtil.EMPTY);
    }
    return StrUtil.EMPTY;
  }

  /**
   * 根据code获取物料详情
   * @param code 商品 code
   */
  public ProductResultDTO getProductDetailByCode(String code) {
    code = StrUtil.emptyIfNull(code);
    String result = get(MPM_FIND_DETAIL,
        MapUtil.builder(new HashMap<String, Object>()).put("code", code).build());
    return resolveResult(result, new TypeReference<ResultBean<ProductResultDTO>>() {}).orElse(new ProductResultDTO());
  }

  /**
   * 查询单位，code和name任选一个，同时存在code优先级更高。
   * @param code 单位code
   * @param name 单位名称
   */
  public UnitResult findUnitByCodeOrName(String code, String name) {
    code = StrUtil.emptyIfNull(code);
    name = StrUtil.emptyIfNull(name);
    String result = get(MPM_FIND_UNIT,
        MapUtil.builder(new HashMap<String, Object>()).put("code", code).put("name", name).build());
    return resolveResult(result, new TypeReference<ResultBean<UnitResult>>() {}).orElse(new UnitResult());
  }

  /**
   * mpm品牌新增
   * @param brandParamWrapper
   * @return String
   */
  @Deprecated
  public String brandAdd(BrandParamWrapper brandParamWrapper){
    JSONObject resJson = null;
    String message = "";
    try {
      log.info("mpm品牌新增接口入参" + JSON.toJSONString(brandParamWrapper));
      String result =
          post(MPM_ADD_BRAND, JSON.parseObject(JSON.toJSONString(brandParamWrapper)));
      log.info("mpm品牌新增接口返参" + result);
      if (!StringUtils.isNullOrEmpty(result)) {
        resJson = JSONObject.parseObject(result);
        if (resJson != null
            && resJson.containsKey("code")
            && Objects.equals(resJson.getString("code"), "0")) {
        } else if (resJson != null && resJson.containsKey("msg")) {
          message = resJson.getString("msg");
        } else {
          message = "未知异常，联系管理员";
        }
      }
    } catch (Exception e) {
      log.error(e.toString());
    }
    return message;
  }

  /**
   * 根据mpm品牌id获取品牌信息
   * @param mpmBrandId
   * @return MpmBrandDTO
   */
  public MpmBrandDTO getBrandDetail(String mpmBrandId){
    if (StrUtil.isNotBlank(mpmBrandId)) {
      String result = get(MPM_GET_BRAND_DETAIL,
          MapUtil.builder(new HashMap<String, Object>()).put("mpmBrandId", mpmBrandId).build());
      return resolveResult(result,new TypeReference<ResultBean<MpmBrandDTO>>(){}).orElse(new MpmBrandDTO());
    }
    return new MpmBrandDTO();
  }

  /**
   * 根据条件查询mpm品牌列表
   * @param param
   * @return MpmBrandDTO
   */
  public List<BrandResult> getBrandPage(Map<String,Object> param){
      String result = get(MPM_GET_BRAND_PAGE, param);
      return resolveResult(result,new TypeReference<ResultBean<List<BrandResult>>>(){}).orElse(
          Collections.emptyList());
  }

  /**
   * 删除物料禁止推送标记
   * @param code  物料编码
   * @param platformCodes 平台编码，可以传多个。
   */
  public void updateNoPushPlatform(String code, List<String> platformCodes) {
    if (StrUtil.isBlank(code) || CollUtil.isEmpty(platformCodes)) {
      return;
    }
    HashMap<String, Object> param = new HashMap<>();
    param.put("code", code);
    param.put("platformCodes", platformCodes);
    String url = xhgjMPMConfig.getServiceUrl() + UPDATE_NO_PUSH_PLATFORM;
    Map<String, Object> resultMap = new HashMap<>();
    try {
      String result = HttpUtil.post(url, param);
      resultMap = JSON.parseObject(result, new TypeReference<Map<String, Object>>() {});
    } catch (Exception e) {
      log.error("调用mpm更新物料禁止推送标记接口异常，返回信息：{}", resultMap);
      e.printStackTrace();
      throw new RuntimeException("删除物料禁止推送标记出现未知异常");
    }
    if (Objects.equals(resultMap.get("code"), Constants.HTTP_RESPONSE_CODE_NO)) {
      log.error("调用mpm更新物料禁止推送标记接口响应失败：{}", resultMap);
      throw new RuntimeException("删除物料禁止推送标记失败");
    }
  }

}
