package com.xhgj.srm.request.service.third.hZero.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.dtflys.forest.http.ForestResponse;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.config.SrmConfig;
import com.xhgj.srm.common.enums.asmDisOrder.AsmDisOrderType;
import com.xhgj.srm.common.enums.entryregistration.EntryRegistrationDiscountTypeEnum;
import com.xhgj.srm.jpa.entity.AsmDisOrder;
import com.xhgj.srm.jpa.entity.AsmDisOrderItem;
import com.xhgj.srm.jpa.entity.EntryRegistrationDiscount;
import com.xhgj.srm.jpa.entity.EntryRegistrationLandingMerchant;
import com.xhgj.srm.jpa.entity.File;
import com.xhgj.srm.jpa.entity.InventoryLocation;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderToFormV2;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderV2;
import com.xhgj.srm.jpa.repository.FileRepository;
import com.xhgj.srm.jpa.repository.InventoryLocationRepository;
import com.xhgj.srm.registration.entity.EntryRegistrationEntity;
import com.xhgj.srm.request.ConstantHZero;
import com.xhgj.srm.request.config.HZeroProcessConfig;
import com.xhgj.srm.request.dto.hZero.process.AsmDisOrderProcessParam;
import com.xhgj.srm.request.dto.hZero.process.AsmDisOrderProcessParam.SubProduct;
import com.xhgj.srm.request.dto.hZero.process.EntryOrderProcessParam;
import com.xhgj.srm.request.dto.hZero.process.EntryOrderProcessParam.EntryLandingMerchant;
import com.xhgj.srm.request.dto.hZero.process.PaymentApplyProcessParam;
import com.xhgj.srm.request.dto.hZero.process.StartProcessParam;
import com.xhgj.srm.request.dto.hZero.process.StartProcessVo;
import com.xhgj.srm.request.dto.hZero.process.SupplierOrderReturnProcessForm;
import com.xhgj.srm.request.dto.hZero.process.SupplierOrderReturnProcessForm.ReturnDetail;
import com.xhgj.srm.request.dto.hZero.process.SupplierOrderWarehouseApplyForm;
import com.xhgj.srm.request.factory.MapStructFactory;
import com.xhgj.srm.request.service.third.api.FeiDaApi;
import com.xhgj.srm.request.service.third.hZero.HZeroService;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.DateUtils;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.StringJoiner;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @Author: fanghuanxu
 * @Date: 2025/2/25 11:35
 * @Description: h0服务实现
 */
@Service
@Slf4j
public class HZeroServiceImpl implements HZeroService {
  @Resource
  private FeiDaApi feiDaApi;
  @Resource
  private HZeroProcessConfig hZeroProcessConfig;
  @Resource
  private InventoryLocationRepository inventoryLocationRepository;
  @Resource
  private FileRepository fileRepository;

  private static final String template  = "履约金额达到{}元，比例{}%";
  private final String baseUrl;
  public HZeroServiceImpl(SrmConfig srmConfig) {
    this.baseUrl = srmConfig.getUploadUrl();
  }
  @Override
  public StartProcessVo startProcessWithoutFile(StartProcessParam param) {
    String description = param.getDescription();
    param.setDescription(null);
    ForestResponse<StartProcessVo> startProcessVoForestResponse = feiDaApi.startProcessWithoutFile(param, description);
    StartProcessVo result = startProcessVoForestResponse.getResult();
    if (result == null) {
      throw new CheckException("调用接口平台启动流程失败");
    }
    return result;
  }

  @Override
  public StartProcessVo startAsmDisOrderProcess(AsmDisOrder asmDisOrder, AsmDisOrderItem fpItem, List<AsmDisOrderItem> subItems) {
    AsmDisOrderProcessParam docJson =
        MapStructFactory.INSTANCE.toAsmDisOrderProcessParam(asmDisOrder);
    // 仓库
    Set<String> warehouseIds =
        subItems.stream().map(AsmDisOrderItem::getWarehouseId).filter(StrUtil::isNotBlank)
            .collect(Collectors.toSet());
    warehouseIds.add(fpItem.getWarehouseId());
    Map<String, String> inventoryId2Name =
        inventoryLocationRepository.findAllById(warehouseIds).stream().collect(
            Collectors.toMap(InventoryLocation::getId, InventoryLocation::getWarehouseName,
                (k1, k2) -> k1));
    // param赋值
    AsmDisOrderProcessParam.FinishedProduct finishedProduct =
        MapStructFactory.INSTANCE.toFinishedProduct(fpItem);
    finishedProduct.setWarehouseName(inventoryId2Name.get(fpItem.getWarehouseId()));
    docJson.setFinishedProductList(Collections.singletonList(finishedProduct));
    List<SubProduct> subProductList = subItems.stream().map(item -> {
      SubProduct subProduct = MapStructFactory.INSTANCE.toSubProduct(item);
      subProduct.setWarehouseName(inventoryId2Name.get(item.getWarehouseId()));
      return subProduct;
    }).collect(Collectors.toList());
    finishedProduct.setSubProductList(subProductList);

    String desc = StrUtil.format("{}提交的{}{}", asmDisOrder.getCreateManName(),
        AsmDisOrderType.getNameByCode(asmDisOrder.getType()),
        asmDisOrder.getCode());
    StartProcessParam processParam =
        StartProcessParam.builder().flowKey(hZeroProcessConfig.getAsmDisOrderFlowKey())
            .businessKey(desc).dimension(ConstantHZero.DIMENSION_ORG)
            .starter(asmDisOrder.getCreateManCode().toLowerCase())
            .description(desc)
            .variableMap(null).docJsonMap(JSON.parseObject(JSON.toJSONString(docJson))).build();
    return this.startProcessWithoutFile(processParam);
  }

  @Override
  public StartProcessVo startEntryOrderProcess(User createMan, EntryRegistrationEntity order) {
    // 基本属性
    EntryOrderProcessParam docJson = MapStructFactory.INSTANCE.toEntryOrderProcessParam(order);
    EntryRegistrationLandingMerchant merchant = order.getMerchant();
    EntryLandingMerchant landingMerchant =
        MapStructFactory.INSTANCE.toEntryLandingMerchant(merchant);
    // 额外赋值
    AtomicReference<String> initialRatio = new AtomicReference<>("");
    order.getDiscounts().stream().findFirst().ifPresent(discount -> {
      initialRatio.set(
          NumberUtil.null2Zero(discount.getDiscountRatio()).stripTrailingZeros().toPlainString()
              + "%");
      docJson.setDiscountRatio(
          NumberUtil.null2Zero(discount.getDiscountRatio()).stripTrailingZeros().toPlainString()
              + "%");
    });
    // 阶梯比例
    String stepText = handleAmountAndDiscount(order.getDiscounts());
    if (StrUtil.isAllNotBlank(docJson.getDiscountRatio(),stepText)) {
      docJson.setDiscountRatio(StrUtil.join("\n", docJson.getDiscountRatio(), stepText));
    }

    List<File> supplyFile = fileRepository.findAllByRelationIdAndRelationTypeAndState(order.getId(),
            Constants.FILE_TYPE_REGISTRATION_ORDER_SUPPLEMENT, Constants.STATE_OK)
        .orElse(Collections.emptyList());
    for (int i = 0; i < Math.min(5, supplyFile.size()); i++) {
      String methodName = "setSupplementaryAttachment" + (i + 1);
      ReflectUtil.invoke(docJson, methodName, getFileFullUrlFromList(supplyFile, i));
    }
    // 1张营业执照
    fileRepository.findFirstByRelationIdAndRelationTypeAndState(merchant.getId(),
        Constants.FILE_TYPE_LANDING_MERCHANT_LICENSE, Constants.STATE_OK).ifPresent(file -> {
      // 营业执照
      landingMerchant.setBusinessLicenseAttachment(getFullUrl(file.getUrl()));
    });
    // 身份证
    List<File> idCardFiles =
        fileRepository.findAllByRelationIdAndRelationTypeAndState(merchant.getId(),
                Constants.FILE_TYPE_LANDING_MERCHANT_ID_CARD_PHOTO, Constants.STATE_OK)
            .orElse(Collections.emptyList());
    for (int i = 0; i < Math.min(idCardFiles.size(), 2); i++) {
      String methodName = "setCorporateIdCardAttachment" + (i + 1);
      ReflectUtil.invoke(landingMerchant, methodName, getFileFullUrlFromList(idCardFiles, i));
    }
    // 产品资历书
    List<File> qualificationFiles =
        fileRepository.findAllByRelationIdAndRelationTypeAndState(merchant.getId(),
                Constants.FILE_TYPE_LANDING_MERCHANT_PRODUCT_QUALIFICATION, Constants.STATE_OK)
            .orElse(Collections.emptyList());
    for (int i = 0; i < Math.min(qualificationFiles.size(), 7); i++) {
      // 反射
      String methodName = "setProductQualificationAttachment" + (i + 1);
      ReflectUtil.invoke(landingMerchant, methodName,
          getFileFullUrlFromList(qualificationFiles, i));
    }
    docJson.setEntryLandingMerchant(Collections.singletonList(landingMerchant));
    // 电商供应商准入审批单：业务员，合作单位名称，合作初始比例，项目大类
    String desc = StrUtil.format("电商供应商准入审批单{}：{}，{}，{}，{}",
        order.getRegistrationNumber(),
        createMan.getRealName(),
        order.getPartnerName(),
        initialRatio.get(),
        order.getProjectCategory());
    String description = StrUtil.format("{}提交的电商供应商准入审批单{}", createMan.getRealName(),
        order.getRegistrationNumber());
    StartProcessParam processParam =
        StartProcessParam.builder().flowKey(hZeroProcessConfig.getEntryOrderFlowKey())
            .businessKey(desc).dimension(ConstantHZero.DIMENSION_ORG)
            .starter(createMan.getCode().toLowerCase()).description(description).variableMap(null)
            .docJsonMap(JSON.parseObject(JSON.toJSONString(docJson))).build();
    return this.startProcessWithoutFile(processParam);
  }

  @Override
  public StartProcessVo startSupplierOrderReturnProcess(SupplierOrderV2 supplierOrder,
      SupplierOrderToFormV2 returnOrderForm, List<ReturnDetail> details, User user) {
    SupplierOrderReturnProcessForm docJson = new SupplierOrderReturnProcessForm();
    docJson.setCode(supplierOrder.getCode());
    docJson.setSupplierName(supplierOrder.getSupplierName());
    docJson.setPurchaseMan(supplierOrder.getPurchaseMan());
    docJson.setPurchaseDept(supplierOrder.getPurchaseDept());
    docJson.setFormCode(returnOrderForm.getFormCode());
    docJson.setPostingDate(DateUtils.formatTimeStampToNormalDateTime(returnOrderForm.getPostingDate()));
    docJson.setReturnReason(returnOrderForm.getReturnReason());
    docJson.setWarehouseName(returnOrderForm.getWarehouseName());
    docJson.setLogisticsCompany(returnOrderForm.getLogisticsCompany());
    docJson.setTrackNum(returnOrderForm.getTrackNum());
    docJson.setConsignee(returnOrderForm.getConsignee());
    docJson.setReceiveAddress(returnOrderForm.getReceiveAddress());
    docJson.setNeedRedTicket(
        StrUtil.equals(Constants.YES, returnOrderForm.getNeedRedTicket()) ? "是" : "否");
    docJson.setDetails(details);

    String desc = StrUtil.format("{}提交的退库单{}", user.getRealName(),
        returnOrderForm.getFormCode());
    StartProcessParam processParam =
        StartProcessParam.builder().flowKey(hZeroProcessConfig.getPurchaseOrderReturnFlowKey())
            .businessKey(desc).dimension(ConstantHZero.DIMENSION_ORG)
            .starter(user.getCode().toLowerCase())
            .description(desc)
            .variableMap(null).docJsonMap(JSON.parseObject(JSON.toJSONString(docJson))).build();
    return this.startProcessWithoutFile(processParam);
  }

  @Override
  public StartProcessVo startPaymentApplyProcess(PaymentApplyProcessParam param) {
    String desc =
        StrUtil.format("{}提交的付款申请单{}", param.getPurchaseMan(), param.getPaymentApplyNo());
    StartProcessParam processParam =
        StartProcessParam.builder().flowKey(hZeroProcessConfig.getPaymentApplyFlowKey())
            .businessKey(desc).dimension(ConstantHZero.DIMENSION_ORG)
            .starter(param.getPurchaseCode().toLowerCase()).description(desc).variableMap(null)
            .docJsonMap(JSON.parseObject(JSON.toJSONString(param))).build();
    return this.startProcessWithoutFile(processParam);
  }

  @Override
  public void createWarehouseApplyQualityCheck(List<SupplierOrderWarehouseApplyForm> forms) {
    ForestResponse<String> stringForestResponse = feiDaApi.purchaseOrderWarehouseApply(forms);
  }

  private String getFileFullUrlFromList(List<File> idCardFiles,int i) {
    return Optional.ofNullable(CollUtil.get(idCardFiles, i)).map(File::getUrl).map(this::getFullUrl)
        .orElse(null);
  }

  private String getFullUrl(String url) {
    return StrUtil.addSuffixIfNot(baseUrl, "/") + StrUtil.removePrefix(url, "/");
  }
  private String handleAmountAndDiscount(List<EntryRegistrationDiscount> entryRegistrationDiscountList) {
    StringJoiner sj = new StringJoiner("\n");
    //有多少条数据，就有多少条履约金额和折扣比例（按照履约金额正序排列），最大5条，超过5条只取前5条
    if (CollUtil.isEmpty(entryRegistrationDiscountList)) {
      return null;
    }
    // 筛选类型为 STEP_DISCOUNT_RATIO 的折扣记录，并按 performanceAmount 升序排序（null 排在最前）
    List<EntryRegistrationDiscount> allRegistrationDiscountList =
        entryRegistrationDiscountList.stream()
            .filter(entryDiscount -> StrUtil.equals(
                EntryRegistrationDiscountTypeEnum.STEP_DISCOUNT_RATIO.getKey(),
                entryDiscount.getType()))
            .sorted(Comparator.comparing(EntryRegistrationDiscount::getPerformanceAmount,
                Comparator.nullsFirst(BigDecimal::compareTo)))
            .skip(1)  // 跳过第一条记录
            .limit(5)  // 只取最多5条记录（即原列表中的第2,3、4、5,6条）
            .collect(Collectors.toList());
    if (CollUtil.isEmpty(allRegistrationDiscountList)) {
      return null;
    }
    // 使用 Map 来存储 performanceAmount 和 discountRatio，键为索引（0, 1, 2,3,4）
    Map<Integer, EntryRegistrationDiscount> discountMap = IntStream.range(0, Math.min(5,
            allRegistrationDiscountList.size()))
        .boxed()
        .collect(Collectors.toMap(i -> i, allRegistrationDiscountList::get));

    // 提取每一条记录的履约金额
    BigDecimal performanceAmount1 = Optional.ofNullable(discountMap.get(0)).map(EntryRegistrationDiscount::getPerformanceAmount).orElse(BigDecimal.ZERO);
    BigDecimal performanceAmount2 = Optional.ofNullable(discountMap.get(1)).map(EntryRegistrationDiscount::getPerformanceAmount).orElse(BigDecimal.ZERO);
    BigDecimal performanceAmount3 = Optional.ofNullable(discountMap.get(2)).map(EntryRegistrationDiscount::getPerformanceAmount).orElse(BigDecimal.ZERO);
    BigDecimal performanceAmount4 = Optional.ofNullable(discountMap.get(3)).map(EntryRegistrationDiscount::getPerformanceAmount).orElse(BigDecimal.ZERO);
    BigDecimal performanceAmount5 = Optional.ofNullable(discountMap.get(4)).map(EntryRegistrationDiscount::getPerformanceAmount).orElse(BigDecimal.ZERO);

    // 根据实际存在的记录数赋值
    Optional.ofNullable(discountMap.get(0)).ifPresent(d -> {
      // 计算金额区间 ≥ ? 万
      sj.add(StrUtil.format(template,
          performanceAmount1.setScale(2, RoundingMode.HALF_UP).toString(),
          NumberUtil.toBigDecimal(d.getDiscountRatio()).setScale(2, RoundingMode.HALF_UP).toString())
      );
    });
    Optional.ofNullable(discountMap.get(1)).ifPresent(d -> {
      // 计算金额区间 > ?万、? ≤万
      sj.add(StrUtil.format(template,
          performanceAmount2.setScale(2, RoundingMode.HALF_UP).toString(),
          NumberUtil.toBigDecimal(d.getDiscountRatio()).setScale(2, RoundingMode.HALF_UP).toString())
      );
    });
    Optional.ofNullable(discountMap.get(2)).ifPresent(d -> {
      // 计算金额区间 > ?万、? ≤万
      sj.add(StrUtil.format(template,
          performanceAmount3.setScale(2, RoundingMode.HALF_UP).toString(),
          NumberUtil.toBigDecimal(d.getDiscountRatio()).setScale(2, RoundingMode.HALF_UP).toString())
      );
    });
    Optional.ofNullable(discountMap.get(3)).ifPresent(d -> {
      // 计算金额区间 > ?万、? ≤万
      sj.add(StrUtil.format(template,
          performanceAmount4.setScale(2, RoundingMode.HALF_UP).toString(),
          NumberUtil.toBigDecimal(d.getDiscountRatio()).setScale(2, RoundingMode.HALF_UP).toString())
      );
    });
    Optional.ofNullable(discountMap.get(4)).ifPresent(d -> {
      // 计算金额区间 > ? 万
      sj.add(StrUtil.format(template,
          performanceAmount5.setScale(2, RoundingMode.HALF_UP).toString(),
          NumberUtil.toBigDecimal(d.getDiscountRatio()).setScale(2, RoundingMode.HALF_UP).toString())
      );
    });

    return sj.toString();
  }
}
