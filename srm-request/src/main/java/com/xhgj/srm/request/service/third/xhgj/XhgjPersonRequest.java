package com.xhgj.srm.request.service.third.xhgj;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.xhgj.srm.common.dto.OAUserInfoDTO;
import com.xhgj.srm.common.dto.PersonDomain;
import com.xhgj.srm.common.dto.XhgjPersonInfoDTO;
import com.xhgj.srm.common.utils.RequestUtil;
import com.xhgj.srm.request.config.AbstractXhgjConfig;
import com.xhgj.srm.request.config.XhgjPersonConfig;
import com.xhgj.srm.request.dto.mdm.NameAndCodeDTO;
import com.xhgj.srm.request.dto.mdm.OrganizationStockDTO;
import com.xhgj.srm.request.dto.person.FindPersonPageParam;
import com.xhgj.srm.request.dto.person.FindPersonPageVO;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.mvc.base.PageResult;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

/**
 * Created by Geng Shy on 2023/8/31
 */
@Component
@Slf4j
public class XhgjPersonRequest implements BaseXhgjRequest {

  private final XhgjPersonConfig config;

  /**
   * 精确查找人员信息
   */
  private static final String FIND_PERSON_INFO = "/getPersonByParams";
  /**
   * 根据钉钉id精确查找人员信息
   */
  private static final String FIND_PERSON_INFO_BY_DING_TALK_ID = "/getPersonByDingTalkId";
  /**
   * 人员信息分页查询
   */
  private final String FIND_PERSON_PAGE = "/getPersonListByThird";
  /**
   * 查询 SAP 库存地址
   */
  private final String FIND_SAP_STOCK_ADDR_BY_CONDITION_URL = "/organization"
      + "/getSAPStockAddrByCondition";

  /**
   * 查询所有组织的库存信息
   */
  private final String FIND_PERSON_ALL_ORG_AND_STOCK = "/organization/getAllOrgAndStock";

  private static final String FIND_PERSON_BY_ID = "/getPersonById";

  public XhgjPersonRequest(XhgjPersonConfig config) {
    this.config = config;
  }

  @Override
  public AbstractXhgjConfig getConfig() {
    return config;
  }

  @Override
  public Logger getLogger() {
    return log;
  }

  /**
   * @param name 名称
   * @param code 编码
   * @param sapComponentCode SAP 公司代码
   * @param sapFactoryCode SAP 工厂代码
   */
  public List<NameAndCodeDTO> getSAPStockAddrByCondition(String name, String code,
      String sapComponentCode, String sapFactoryCode) {
    String url = config.getServiceUrl() + FIND_SAP_STOCK_ADDR_BY_CONDITION_URL;
    Map<String, Object> param =
        MapUtil.of(new Pair<String, Object>("name", name), new Pair<String, Object>("code", code),
            new Pair<String, Object>("sapComponentCode", sapComponentCode),
            new Pair<String, Object>("sapFactoryCode", sapFactoryCode));
    try {
      String result = HttpUtil.get(url, param);
      return RequestUtil.extractDataArray(JSON.parseObject(result), NameAndCodeDTO.class);
    } catch (RuntimeException e) {
      log.info("调用【查询 SAP 库存地址】入参：" + JSON.toJSONString(param));
      log.info("调用【查询 SAP 库存地址】异常：" + e);
      throw new RuntimeException("调用【查询 SAP 库存地址】异常", e);
    }
  }

  public XhgjPersonInfoDTO findPersonInfo(String name, String mobile) {
    name = StrUtil.emptyIfNull(name);
    mobile = StrUtil.emptyIfNull(mobile);
    String url = config.getServiceUrl() + FIND_PERSON_INFO ;
    Map<String,Object> params = new HashMap<>();
    params.put("name", name);
    params.put("mobile", mobile);
    params.put("excludeInvalidPersonStatus", true);
    XhgjPersonInfoDTO xhgjPersonInfoDTO = new XhgjPersonInfoDTO();
    try {
      String json = HttpUtil.get(url,params);
      xhgjPersonInfoDTO = JSON.parseObject(json, new TypeReference<XhgjPersonInfoDTO>() {});
    } catch (Exception e) {
      log.error("信息化人员信息服务异常");
    }
    return xhgjPersonInfoDTO;
  }

  public OAUserInfoDTO findPersonInfoByDingTalkId(String dingTalkId) {
    Assert.notBlank(dingTalkId);
    String url = config.getServiceUrl() + FIND_PERSON_INFO_BY_DING_TALK_ID + "?dingTalkId=" + dingTalkId;
    OAUserInfoDTO oaUserInfo = new OAUserInfoDTO();
    try {
      String json = HttpUtil.get(url);
      oaUserInfo = JSON.parseObject(json, new TypeReference<OAUserInfoDTO>() {});
    } catch (Exception e) {
      log.error("信息化人员信息服务异常");
    }
    return oaUserInfo;
  }
  /**
   * 分页查询咸亨国际人员信息（北森的数据）
   */
  public PageResult<FindPersonPageVO> getPersonUserPage(FindPersonPageParam param) {
    Assert.notNull(param);
    String url = config.getServiceUrl() + FIND_PERSON_PAGE;
    String json = null;
    try {
      json = HttpUtil.post(url, JSON.toJSONString(param));
      if (StrUtil.isBlank(json)) {
        throw new CheckException();
      }
    } catch (Exception e) {
      log.error("信息化人员信息服务异常");
      throw new CheckException("信息化人员信息服务异常", e);
    }
    return RequestUtil.extractData(JSON.parseObject(json),
        new TypeReference<PageResult<FindPersonPageVO>>() {});
  }

  public List<OrganizationStockDTO> getAllOrgAndStock() {
    String url = config.getServiceUrl() + FIND_PERSON_ALL_ORG_AND_STOCK;
    try {
      String result = HttpUtil.get(url);
      return RequestUtil.extractDataArray(JSON.parseObject(result), OrganizationStockDTO.class);
    } catch (RuntimeException e) {
      log.info("调用【mdm-查询所有组织的库存信息】异常：" + e);
      throw new RuntimeException("调用【mdm-查询所有组织的库存信息】异常：", e);
    }
  }
  /**
   * 根据人员id查询人员信息
   */
  public PersonDomain findPersonInfoById(String personId) {
    Assert.notBlank(personId);
    String url = config.getServiceUrl() + FIND_PERSON_BY_ID + "?id=" + personId;
    try {
      String json = HttpUtil.get(url);
      return RequestUtil.extractData(JSON.parseObject(json),
          new TypeReference<PersonDomain>() {});
    } catch (Exception e) {
      log.error("信息化人员信息服务异常",e);
      throw new CheckException("信息化人员信息服务异常", e);
    }
  }

}
