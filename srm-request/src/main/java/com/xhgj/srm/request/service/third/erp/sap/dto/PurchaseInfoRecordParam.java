package com.xhgj.srm.request.service.third.erp.sap.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/1/3 11:21
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseInfoRecordParam {

  /** 采购信息记录号 */
  private String INFNR;

  /** SRM 参考代码：传入参数无值时，则调用创建bapi，有值时则进行修改 */
  private String SRMID;

  /** 供应商科目编号（供应商主数据编码） */
  private String LIFNR;

  /** 物料编码 */
  private String MATNR;

  /** 采购组织 */
  private String EKORG;

  /** 工厂 */
  private String WERKS;

  /** 采购信息记录分类：标准-0 寄售-2*/
  private String ESOKZ;

  /** 计划交货时间（天） */
  private String APLFZ;

  /** 采购组（采购部门编码） */
  private String EKGRP;

  /** 销售/购买税代码（税率）：J1-17%、J2-16%、J3-13% */
  private String MWSKZ;

  /** 净价（含税单价），最多输入两位小数 */
  private String NETPR;

  /** 价格单位：默认传1 */
  private String PEINH;

  /** 订单价格单位（物料单位）：EA、PC与物料计量单位一致 */
  private String BPRME;

  /** 货币码：CNY-中国人民币、USD-美元 */
  private String WAERS;

  /** 订单价格单位转换为订单单位的分母 */
  private String BPUMN;

  /** 订单价格单位转换为订单单位的分子 */
  private String BPUMZ;

  /** 开始生效日期：yyyyMMdd */
  private String DATAB;

  /** 有效期至日期：yyyyMMdd */
  private String DATBI;

  /** 标准采购订单数量：默认传1 */
  private String NORBM;
}
