package com.xhgj.srm.request.service.third.xhgj;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.xhgj.srm.common.enums.ShortMessageEnum;
import com.xhgj.srm.request.config.AbstractXhgjConfig;
import com.xhgj.srm.request.config.XhgiOCRConfig;
import com.xhgj.srm.request.dto.ocr.AccurateDTO;
import com.xhgj.srm.request.dto.ocr.GetAccurateParams;
import com.xhgj.srm.request.dto.sms.SendSmsParams;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.mvc.base.ResultBean;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2023-04-17 14:06
 */
@Component
@Slf4j
public class XhgjOCRRequest implements BaseXhgjRequest{
  @Autowired
  private XhgiOCRConfig config;

  /**
   * 通用文字识别（高精度含位置版）
   */
  private static final String  GET_ACCURATE = "baidu/ocr/getAccurate";

  @Override
  public AbstractXhgjConfig getConfig() {
    return config;
  }

  @Override
  public Logger getLogger() {
    return log;
  }

  /**
   * 通用文字识别
   * @param params 参数
   */
  public AccurateDTO getAccurate(GetAccurateParams params) {
    String image = params.getImage();
    String url = params.getUrl();
    String pdf = params.getPdf();
    if (StrUtil.isAllBlank(image,url,pdf)) {
      throw new CheckException("没有需要识别的文件");
    }
    return resolveResult(
        post(GET_ACCURATE, JSON.parseObject(JSON.toJSONString(params))),
        new TypeReference<ResultBean<AccurateDTO>>() {})
        .orElse(null);
  }
}
