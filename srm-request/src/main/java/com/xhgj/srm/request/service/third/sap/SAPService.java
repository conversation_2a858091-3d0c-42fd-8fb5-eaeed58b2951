package com.xhgj.srm.request.service.third.sap;

import com.dtflys.forest.http.ForestResponse;
import com.xhgj.srm.request.dto.sap.SapInventoryQueryForm;
import com.xhgj.srm.request.service.third.erp.sap.dto.AdvanceApplyParam;
import com.xhgj.srm.request.service.third.erp.sap.dto.AdvanceApplyResult;
import com.xhgj.srm.request.service.third.erp.sap.dto.InvoicePostingParam;
import com.xhgj.srm.request.service.third.erp.sap.dto.InvoicePostingResult;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_075Param;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_075Result;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_076Param;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_076Result;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_077Param;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_077Result;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_078Param;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_078Result;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_079Param;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_079Result;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_084Param;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_084Result;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_086Param;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_086Result;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_087Param;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_087Result;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_088Param;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_088Result;
import com.xhgj.srm.request.service.third.erp.sap.dto.PurchaseApplyForOrderUpdateParam;
import com.xhgj.srm.request.service.third.erp.sap.dto.ReceiptOrReturnReversalParams;
import com.xhgj.srm.request.service.third.erp.sap.dto.ReceiptOrReturnReversalResult;
import com.xhgj.srm.request.service.third.erp.sap.dto.ReceiptVoucherSynchronizationParam;
import com.xhgj.srm.request.service.third.erp.sap.dto.ReceiptVoucherSynchronizationResult;
import com.xhgj.srm.request.service.third.erp.sap.dto.SapDrawApplyParam;
import com.xhgj.srm.request.service.third.erp.sap.dto.SapPayStateParam;
import com.xhgj.srm.request.service.third.erp.sap.dto.SapPayStateResult;
import com.xhgj.srm.request.service.third.erp.sap.dto.UpdatePurchaseOrderSapParam;
import com.xhgj.srm.request.service.third.erp.sap.dto.UpdatePurchaseOrderSapResult.UpdatePurchaseOrderRETURNDTO;
import com.xhgj.srm.request.vo.sap.SapInventoryVO;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <AUTHOR>
 * SAP相关服务
 */
public interface SAPService {

  /**
   * SAP付款申请（预付款、应付款、退款、冲销）接口 + 锁
   * MM034
   * @param advanceApplyParam 付款申请参数
   */
  AdvanceApplyResult sapAdvanceApplyWithLockGroup(AdvanceApplyParam advanceApplyParam);

  /**
   * SAP付款申请（预付款、应付款、退款、冲销）接口
   * MM034
   * @param advanceApplyParam
   * @return
   */
  AdvanceApplyResult sapAdvanceApply(AdvanceApplyParam advanceApplyParam);

  /**
   * SAP物料凭证新增接口 + 锁
   * MM_031
   */
  ReceiptVoucherSynchronizationResult sapMaterialVoucherWithLockGroup(ReceiptVoucherSynchronizationParam form);

  /**
   * SAP物料凭证新增接口 + 锁
   * MM_031
   */
  ReceiptVoucherSynchronizationResult sapMaterialVoucherWithLockGroup(ReceiptVoucherSynchronizationParam form, ReceiptVoucherSynchronizationResult tempResult);

  /**
   * SAP物料凭证新增接口
   * MM_031
   * @param form
   * @return
   */
  ReceiptVoucherSynchronizationResult sapMaterialVoucherWithError(ReceiptVoucherSynchronizationParam form, ReceiptVoucherSynchronizationResult tempResult);

  /**
   * SAP财务凭证修改申请接口
   * MM_066
   * @param form
   * @return
   */
  AdvanceApplyResult sapDraw(SapDrawApplyParam form);

  /**
   * SAP 采购订单创建/修改接口 MM_021,错误则发送群内报警
   */
  UpdatePurchaseOrderRETURNDTO sapPurchaseOrderWithAlarm(UpdatePurchaseOrderSapParam form, String orderNo);

  /**
   * SAP查询sap付款状态 ZFM_SRM_FI_018
   */
  SapPayStateResult sapPayState(SapPayStateParam param);

  /**
   * SAP直销库退货接口 MM_075
   */
  MM_075Result sapDirectReturnWithError(MM_075Param param);

  /**
   * SAP直销库退货接口 MM_075
   */
  MM_075Result sapDirectReturnWithError(MM_075Param param, MM_075Result tempResult);

  /**
   * 上传sap银行回单至oss
   */
  String uploadBankReceipt(String url);
  /**
   * 发票过账(失败抛异常且发送消息)
   * ZFM_FICO_008
   */
  InvoicePostingResult transferItemsWithAlarm(InvoicePostingParam form, String invoiceNums);


  /**
   * SAP MM080库存查询接口
   */
  List<SapInventoryVO> sapInventoryQuery(SapInventoryQueryForm form);

  /**
   * SAP MM076调拨凭证过账接口
   */
  MM_076Result sapTransferOrder(MM_076Param param);

  /**
   * SAP MM077组装拆卸单创建修改接口
   */
  MM_077Result sapAsmDisOrderUpdate(MM_077Param param);

  /**
   * SAP MM078组装拆卸单状态变更同步
   */
  MM_078Result sapAsmDisOrderChangeState(MM_078Param param);

  /**
   * SAP MM079组装拆卸信息过账接口
   */
  MM_079Result sapAsmDisOrderInfoPosting(MM_079Param param);

  /**
   * SAP MM053更新采购申请单接口
   */
  boolean sapUpdatePurchaseForOrder(List<PurchaseApplyForOrderUpdateParam> param);
  /**
   * SAP MM084 采购订单状态更新接口
   */
  MM_084Result sapPurchaseOrderStatusUpdate(MM_084Param param);

  /**
   * SAP MM086订单查询
   */
  MM_086Result sapOrderInquiries(MM_086Param param);

  /**
   * SAP MM087资料卡片查询
   */
  MM_087Result sapMaterialCardInquiries(MM_087Param param);
  /**
   * SAP MM088查询BOM清单
   */
  MM_088Result sapBomInventoryInquiries(MM_088Param param);

  /**
   * sap 032接口 入库单 退库单冲销
   */
  ReceiptOrReturnReversalResult sapReceiptOrReturnReversal(ReceiptOrReturnReversalParams param);
}
