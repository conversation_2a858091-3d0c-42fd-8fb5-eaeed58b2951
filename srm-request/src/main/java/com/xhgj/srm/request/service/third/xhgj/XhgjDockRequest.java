package com.xhgj.srm.request.service.third.xhgj;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.xhgj.cloud.dock.provider.support.dto.DockSupplier;
import com.xhgj.srm.request.config.AbstractXhgjConfig;
import com.xhgj.srm.request.config.XhgjDockConfig;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.mvc.base.ResultBean;
import java.util.Map;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2022/8/11 0:06
 */
@Component
@Slf4j
public class XhgjDockRequest implements BaseXhgjRequest {
  @Autowired private XhgjDockConfig config;
  /** 修改主数据 */
  private static final String POST_CREATE_SUPPLIER_ADD_TASK = "task/createSupplierTask";

  @Override
  public AbstractXhgjConfig getConfig() {
    return config;
  }

  @Override
  public Logger getLogger() {
    return log;
  }

  /** 创建供应商推送任务 */
  public Optional<String> createSupplierAddTask(DockSupplier dto) {
    if (StrUtil.isBlank(dto.getMdmCode())) {
      throw new CheckException("【" + dto.getName() + "】该供应商无 MDM 编码无法推送 ERP ，请联系管理员处理！");
    }
    Map<String, Object> params =
        JSON.parseObject(JSON.toJSONString(dto), new TypeReference<Map<String, Object>>() {});
    return resolveResult(
        post(POST_CREATE_SUPPLIER_ADD_TASK, params), new TypeReference<ResultBean<String>>() {});
  }
}
