package com.xhgj.srm.request.service.third.xhgj;

import com.xhgj.srm.common.dto.MDMBankInfoDTO;
import com.xhgj.srm.request.dto.edge.CountryDTO;
import com.xhgj.srm.request.dto.edge.CountryDomain;
import com.xhgj.srm.request.dto.edge.IndustryDTO;
import com.xhgj.srm.request.dto.edge.ProvinceCityDTO;
import java.util.List;
import java.util.Optional;

public interface XhgjEdgeService {

  /**
   * 获取所有省市信息
   * @return List<ProvinceCityDTO>
   */
  List<ProvinceCityDTO> getAllProvinceCity();

  /**
   * 获取所有国家信息
   * @return List<CountryDTO>
   */
  List<CountryDTO> getAllCountry();

  /**
   * 获取所有行业信息
   * @return List<IndustryDTO>
   */
  List<IndustryDTO> getAllIndustry();

  /**
   * 获取国家信息
   *
   * @param name 名称
   * @return Optional<CountryDomain>
   */
  Optional<CountryDomain>  getCountryByName(String name);

  /**
   * 获取银行信息
   *
   * @param bankName 银行名称
   * @param bankCode 银行编码
   * @param bankNameOrBankCode 银行名称或银行编码
   * @return MDMBankInfoDTO
   */
  MDMBankInfoDTO pageQueryBankBranchByEdge(
      String bankName, String bankCode,String bankNameOrBankCode
  );

  /**
   * 获取省市信息包含全国
   * @return List<ProvinceCityDTO>
   */
  Optional<List<ProvinceCityDTO>> getAllProvinceCityWithCountry();

}
