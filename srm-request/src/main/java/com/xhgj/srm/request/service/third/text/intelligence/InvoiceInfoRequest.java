package com.xhgj.srm.request.service.third.text.intelligence;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.xhgj.srm.common.dto.invoice.InvoiceOcrRecognitionResultDTO;
import com.xhgj.srm.common.dto.invoice.InvoiceOcrRecognitionResultDTO;
import com.xhgj.srm.common.dto.invoice.InvoiceVerificationInfo;
import com.xhgj.srm.common.dto.invoice.InvoiceVerificationParam;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Created by <PERSON><PERSON> on 2023/8/15
 */

@Slf4j
@Component
public class InvoiceInfoRequest {

  private final String HEAD_APP_ID = "3c9c1f61603aecc61e9c48777d5d7db0";
  private final String HEAD_SECRET_CODE = "74fec8754809ab2fd9615e462477cf55";
  /**
   * 验真url
   */
  private final String VERIFICATION_URL = "https://api.textin.com/robot/v1.0/api/verify_vat";
  /**
   * 增值税发票识别提取
   */
  private final String IDENTIFY_URL = "https://api.textin.com/robot/v1.0/api/vat_invoice";

  /**
   * 查询发票详情信息（验真）
   */
  public InvoiceVerificationInfo doQueryInfo(final InvoiceVerificationParam param) {
    if (param == null) {
      return null;
    }
    log.info("请求发票验真服务，入参：{}", JSONUtil.toJsonStr(param));
    HttpRequest request = HttpUtil.createPost(VERIFICATION_URL).header("x-ti-app-id", HEAD_APP_ID)
        .header("x-ti-secret-code", HEAD_SECRET_CODE)
        .body(com.alibaba.fastjson.JSON.toJSONString(param));
    InvoiceVerificationInfo invoiceVerificationInfo = null;
    try {
      HttpResponse response = request.execute();
      String result = response.body();
      invoiceVerificationInfo = com.alibaba.fastjson.JSON.parseObject(result,
          new TypeReference<InvoiceVerificationInfo>() {});
      log.info("请求发票验真服务响应结果：{}", JSONUtil.toJsonStr(result));
    } catch (Exception e) {
      log.error("请求发票验真服务出现异常，入参：{}", JSONUtil.toJsonStr(param));
    }
    return invoiceVerificationInfo;
  }

  /**
   * 校验发票状态是否正常
   */
  public boolean doVerification(final InvoiceVerificationInfo invoiceVerificationInfo) {
    if (invoiceVerificationInfo == null || invoiceVerificationInfo.getResult() == null) {
      return false;
    }
    String success_code = "001";
    String code = invoiceVerificationInfo.getResult().getCode();
    return Objects.equals(success_code, code);
  }

  /**
   * 发票提取服务
   *
   * @param bytes 发票文件
   * @return 提取后映射的信息对象
   */
  public InvoiceOcrRecognitionResultDTO invoiceIdentify(byte[] bytes) {
    InvoiceOcrRecognitionResultDTO InvoiceOcrRecognitionResultDTO = null;
    String body = null;
    try {
      body = HttpUtil.createPost(IDENTIFY_URL).header("x-ti-app-id", HEAD_APP_ID)
          .header("x-ti-secret-code", HEAD_SECRET_CODE)
          .header("Content-Type", "application/octet" + "-stream").charset("UTF-8").body(bytes)
          .execute().body();
      InvoiceOcrRecognitionResultDTO = JSON.parseObject(body, new TypeReference<InvoiceOcrRecognitionResultDTO>() {});
      log.info("合合信息发票识别服务响应：{}", body);
    } catch (Exception e) {
      log.info("请求发票提取服务出现异常！", e);
    }
    return InvoiceOcrRecognitionResultDTO;
  }
}
