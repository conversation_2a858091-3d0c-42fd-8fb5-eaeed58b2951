package com.xhgj.srm.request.service.third.oms;

import com.xhgj.srm.common.dto.OrderLargeTicketProjectNo;
import com.xhgj.srm.common.dto.PaymentStatus;
import com.xhgj.srm.common.dto.PlatformLargeTicketProjectDTO;
import com.xhgj.srm.jpa.entity.Order;
import com.xhgj.srm.request.dto.oms.OMSCustomerFilingSheetAddParam;
import com.xhgj.srm.request.dto.oms.OMSFilingSheetAddParam;
import com.xhgj.srm.request.dto.oms.PlatformLargeTicketParam;
import com.xhgj.srm.request.dto.supplierRate.SupplierRateBatchAddParam;
import com.xhgj.srm.request.vo.SupplierRateDetailVO;
import com.xhgj.srm.request.vo.oms.OmsOrderCustomerInfo;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface OMSService {

  /**
   * 报备单派单至履约
   */
  void filingSheetAdd(OMSFilingSheetAddParam form);

  /**
   * 客户订单号报备单派单
   */
  void customerFilingSheetAdd(OMSCustomerFilingSheetAddParam form);
  /**
   * 履约同步供应商折扣比例
   */
  void supplierRateAdd(SupplierRateBatchAddParam form);
  /**
   * 获取履约供应商折扣比例
   */
  String getSupplierRate(String supplierId, String price, String platform);
  /**
   * 获取履约供应商折扣比例详情
   */
  SupplierRateDetailVO getSupplierRateDetail(String supplierId, String platform);
  /**
   * 获取履约订单客户资料信息
   */
  OmsOrderCustomerInfo getOmsOrderCustomerInfo(String orderNo, String platformCode);

  /**
   * 获取并更新履约订单客户资料信息
   */
  void updateOmsOrderCustomerInfo(Order order);

  /**
   * 通过订单编号获取订单的大票
   */
  List<OrderLargeTicketProjectNo> getOrderLargeTicketProjectNo(String orderNo, String dockingOrderType);

  /**
   * 通过订单编号获取旧履约大票号码
   */
  String getOrderLargeTicketProjectNoOld(String orderNo, String dockingOrderType);

  /**
   * 从履约平台查询订单回款状态信息
   */
  List<PaymentStatus> getOrderCustomerReturnStatus(String orderNo, String platformCode, String supplierOrderId);


  List<PlatformLargeTicketProjectDTO>  getLargeTicketInfoByProjectNoList(
      PlatformLargeTicketParam param);
}
