package com.xhgj.srm.mobile.controller;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.constants.Constants_LockName;
import com.xhgj.srm.mobile.dto.OrderCountDTO;
import com.xhgj.srm.mobile.dto.order.SupplierOrderDeliveryProductDetailDTO;
import com.xhgj.srm.mobile.dto.supplierOrder.AddSupplierOrderDeliveryParam;
import com.xhgj.srm.mobile.dto.supplierOrder.OrderProgressTimeDTO;
import com.xhgj.srm.mobile.dto.supplierOrder.SingleBaseParam;
import com.xhgj.srm.mobile.dto.supplierOrder.SupplierOrderCancelDTO;
import com.xhgj.srm.mobile.dto.supplierOrder.SupplierOrderDetailDTO;
import com.xhgj.srm.mobile.dto.supplierOrder.SupplierOrderQueryDTO;
import com.xhgj.srm.mobile.dto.supplierOrder.SupplierOrderReturnDTO;
import com.xhgj.srm.mobile.dto.supplierOrder.SupplierOrderShipDTO;
import com.xhgj.srm.mobile.handler.SupplierAuditor;
import com.xhgj.srm.mobile.service.SupplierOrderService;
import com.xhgj.srm.service.SharePurchaseOrderService;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.StringUtils;
import com.xhiot.boot.mvc.base.PageResult;
import com.xhiot.boot.mvc.base.ResultBean;
import com.xhiot.boot.repeat.annotation.RepeatSubmit;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import java.net.URLEncoder;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2024/06/19 13:23
 */
@RestController
@RequestMapping("/supplierOrder")
@Api(tags = {"订单供应商接口"})
@Slf4j
@Validated
public class SupplierOrderController {
  @Autowired private SupplierOrderService service;
  @Resource private RedissonClient redissonClient;
  @Resource private SupplierAuditor supplierAuditor;
  @Resource
  SharePurchaseOrderService sharePurchaseOrderService;
  @ApiOperation(value = "新建发货单")
  @PostMapping(
      value = "supplierOrderDelivery",
      consumes = {MediaType.APPLICATION_JSON_VALUE})
  @RepeatSubmit
  public ResultBean<Boolean> supplierOrderDelivery(
      @RequestBody @Validated AddSupplierOrderDeliveryParam deliveryParam) {
    RLock lock = null;
    RLock shareLock = null;
    try {
      lock =
          redissonClient.getLock(
              Constants_LockName.SUPPLIER_ORDER_DELIVERY + deliveryParam.getSupplierId());
      shareLock = redissonClient.getLock(
          StrUtil.format(Constants_LockName.PURCHASE_ORDER_UPDATE_LOCK, deliveryParam.getId()));
      lock.lock();
      shareLock.lock();
      service.supplierOrderDelivery(deliveryParam);
    } catch (CheckException checkException) {
      throw checkException;
    } catch (Exception e) {
      log.error(ExceptionUtil.stacktraceToString(e, -1));
      throw new CheckException("未知异常，请联系管理员！");
    } finally {
      if (lock != null) {
        lock.unlock();
      }
      if (shareLock != null) {
        shareLock.unlock();
      }
    }
    return new ResultBean<>(true, "操作成功!");
  }

  @ApiOperation(value = "获取采购订单详情")
  @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "订单 id", required = true)})
  @GetMapping("getSupplierOrder")
  public ResultBean<SupplierOrderDetailDTO> getSupplierOrder(@RequestParam String id) {
    if (StringUtils.isNullOrEmpty(id)) {
      throw new CheckException("订单id为空");
    }
    return new ResultBean<>(service.getSupplierOrder(id));
  }

  @ApiOperation("拒单")
  @GetMapping("/refuseOrder")
  public ResultBean<Boolean> refuseOrder(String orderId, String refuseReason, String userId) {
    return new ResultBean<>(service.refuseOrder(orderId, refuseReason, userId));
  }

  @ApiOperation("确认订单")
  @ApiImplicitParams({
    @ApiImplicitParam(name = "id", value = "订单 id", required = true),
  })
  @PostMapping(
      value = "confirmOrder",
      consumes = {MediaType.APPLICATION_JSON_VALUE})
  public ResultBean<Boolean> confirmOrder(@RequestBody SingleBaseParam singleBaseParam) {
    String id = singleBaseParam.getId();
    if (StringUtils.isNullOrEmpty(id)) {
      throw new CheckException("订单id为空");
    }
    service.confirmOrder(id);
    return new ResultBean<>();
  }

  @ApiOperation(value = "供应商订单统计")
  @ApiImplicitParams({@ApiImplicitParam(name = "supplierId", value = "供应商 id", required = true)})
  @GetMapping(value = "/getSupplierOrderCount")
  public ResultBean<OrderCountDTO> getSupplierOrderCount(@Valid SupplierOrderQueryDTO queryDTO) {
    return new ResultBean<>(service.getSupplierOrderCount(queryDTO));
  }

  @ApiOperation("根据供应商订单 id 获得发货单")
  @ApiImplicitParams(@ApiImplicitParam(name = "id", value = "供应商订单 id"))
  @GetMapping("getSupplierOrderShipById")
  public ResultBean<List<SupplierOrderShipDTO>> getSupplierOrderShipById(String id) {
    if (StringUtils.isNullOrEmpty(id)) {
      throw new CheckException("订单id为空");
    }
    return new ResultBean<>(service.getSupplierOrderShipDTO(id));
  }

  @ApiOperation("根据供应商订单 id 获得退货单")
  @ApiImplicitParams(@ApiImplicitParam(name = "id", value = "供应商订单 id"))
  @GetMapping("getSupplierOrderReturnById")
  public ResultBean<List<SupplierOrderReturnDTO>> getSupplierOrderReturnById(String id) {
    if (StringUtils.isNullOrEmpty(id)) {
      throw new CheckException("订单id为空");
    }
    return new ResultBean<>(service.getSupplierOrderReturnById(id));
  }

  @ApiOperation("根据供应商订单 id 获得取消单")
  @ApiImplicitParams(@ApiImplicitParam(name = "id", value = "供应商订单 id"))
  @GetMapping("getSupplierOrderCancelById")
  public ResultBean<List<SupplierOrderCancelDTO>> getSupplierOrderCancleById(String id) {
    if (StringUtils.isNullOrEmpty(id)) {
      throw new CheckException("订单id为空");
    }
    return new ResultBean<>(service.getSupplierOrderCancelById(id,supplierAuditor.getCurrentAuditorId()));
  }

  @ApiOperation("获取订单进度时间")
  @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "供应商订单 id ")})
  @GetMapping("getOrderProgressTime")
  public ResultBean<OrderProgressTimeDTO> getOrderProgressTime(
      @RequestParam @NotBlank(message = "供应商订单 id 必传") String id) {
    return new ResultBean<>(service.getOrderProgressTime(id));
  }

  @ApiOperation(value = "获取发货清单")
  @GetMapping(value = "/getProductInfoPage")
  public ResultBean<PageResult<SupplierOrderDeliveryProductDetailDTO>> getProductInfoPage(
      @RequestParam String orderId,
      @RequestParam(required = false) String keyWord,
      @RequestParam Integer pageNo,
      @RequestParam Integer pageSize,
      @RequestParam(required = false) String ids) {
    return new ResultBean<>(service.getProductInfoPage(orderId, keyWord, pageNo, pageSize, ids));
  }

  @SneakyThrows
  @ApiOperation(value = "导出采购合同")
  @PostMapping(value = "downloadPurchaseOrderContract")
  public ResponseEntity<byte[]> downloadSendTickets(@RequestParam String orderId) {
    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
    headers.setContentDispositionFormData("attachment",
        URLEncoder.encode("purchaseOrderContract" + System.currentTimeMillis() + ".docx", "UTF-8"));
    byte[] bytes = sharePurchaseOrderService.downloadPurchaseOrderContract(orderId);
    return new ResponseEntity<>(bytes, headers, HttpStatus.CREATED);
  }
}
