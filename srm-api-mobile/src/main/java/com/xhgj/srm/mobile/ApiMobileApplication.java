package com.xhgj.srm.mobile;

import com.xhiot.boot.ding.auth.controller.LoginWithDingController;
import com.xhiot.boot.ding.auth.service.impl.BootDingLoginServiceImpl;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ComponentScan.Filter;
import org.springframework.context.annotation.FilterType;
import org.springframework.context.annotation.Import;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

/**
 * <AUTHOR>
 * @since 2024-06-04 8:45
 */
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@EnableJpaRepositories(basePackages = {"com.xhiot.boot", "com.xhgj.srm"})
@EntityScan(basePackages = {"com.xhiot.boot", "com.xhgj.srm"})
@ComponentScan(
    basePackages = {"com.xhiot.boot", "com.xhgj.srm"},
    excludeFilters =
    @Filter(
        type = FilterType.ASSIGNABLE_TYPE,
        classes = {LoginWithDingController.class, BootDingLoginServiceImpl.class}))
@EnableJpaAuditing
@Import(cn.hutool.extra.spring.SpringUtil.class)
public class ApiMobileApplication {

  public static void main(String[] args) {
    SpringApplication.run(ApiMobileApplication.class, args);
  }
}
