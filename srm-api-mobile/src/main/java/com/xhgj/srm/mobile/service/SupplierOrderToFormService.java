package com.xhgj.srm.mobile.service;

import cn.hutool.core.date.DateField;
import com.alibaba.fastjson.JSONArray;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormType;
import com.xhgj.srm.jpa.entity.SupplierOrderToForm;
import com.xhgj.srm.mobile.dto.supplierOrder.LogisticsInformationParam;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import java.util.List;

/**
 * <AUTHOR> @ClassName SupplierOrderToFormService
 */
public interface SupplierOrderToFormService extends BootBaseService<SupplierOrderToForm, String> {
  /**
   * 获得订单明细的表单信息
   *
   * @param supplierOrderId 供应商订单 id 必传
   */
  SupplierOrderToForm getDetailedBySupplierOrderId(String supplierOrderId);

  /**
   * 根据单子类型和供应商订单 id 获得对应的单据
   *
   * @param type 单据类型 必传
   * @param supplierOrderId 供应商订单 id 必传
   */
  List<SupplierOrderToForm> getByTypeAndSupplierOrderId(
      SupplierOrderFormType type, String supplierOrderId);

  /**
   * 根据单子类型和供应商订单 id 获得对应的 取消或退货 单据
   *
   * @param type 单据类型 必传
   * @param supplierOrderId 供应商订单 id 必传
   */
  List<SupplierOrderToForm> getReturnOrCancelFormByTypeAndAndStatusSupplierOrderId(
      SupplierOrderFormType type, String supplierOrderId, List<String> statusList);

  /**
   * 创建发货单 @Author: liuyq @Date: 2022/12/2 16:15
   *
   * @param supplierOrderId 履约订单id
   * @return void
   */
  SupplierOrderToForm createSupplierOrderForm(String supplierOrderId, SupplierOrderFormType type);
  /**
   * 根据单子类型和供应商订单 id 获得对应的单据
   *
   * @param type 单据类型 必传
   * @param status 单据状态 必传
   */
  List<SupplierOrderToForm> getAllOrderToFormByType(String type, String status);

  /** 更新订单签收状态 */
  void updateOrderSignState(DateField dateField, Integer offSetInt);

  /** 提醒采购收货 */
  void remindPurchaseReceipt(DateField dateField, Integer offSetInt);

  /** 获取物流信息 */
  JSONArray getLogisticsInformation(LogisticsInformationParam param);

  /**
   * @param supplierOrderId 订单id
   */
  List<SupplierOrderToForm> findBySupplierOrderId(String supplierOrderId);

  /**
   * 根据单子类型和供应商订单 id 获得对应的 取消或退货 单据
   *
   * @param type 单据类型 必传
   * @param supplierOrderId 供应商订单 id 必传
   */
  List<SupplierOrderToForm> getReturnOrCancelFormByTypeAndAndStateSupplierOrderId(
      SupplierOrderFormType type, String supplierOrderId);
}
