package com.xhgj.srm.mobile.controller;

import com.xhgj.srm.mobile.dto.product.ProductResultVO;
import com.xhgj.srm.mobile.service.ProductService;
import com.xhgj.srm.request.dto.edge.ProvinceCityDTO;
import com.xhgj.srm.request.service.third.xhgj.XhgjEdgeService;
import com.xhiot.boot.mvc.base.ResultBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("product")
@Api(tags = {"物料接口"})
@Slf4j
@Validated
public class ProductController {

  @Autowired ProductService productService;
  @Resource
  XhgjEdgeService xhgjEdgeService;

  @ApiOperation(value = "获取所有省市信息")
  @GetMapping("/getAllProvinceCity")
  public ResultBean<List<ProvinceCityDTO>> getAllProvinceCity() {
    return new ResultBean<>(xhgjEdgeService.getAllProvinceCity());
  }

  @ApiOperation(value = "获取商品信息详情", notes = "获取商品信息详情")
  @ApiImplicitParams({
    @ApiImplicitParam(name = "code", value = "编码", dataType = "String"),
  })
  @GetMapping(value = "/getNormalProductDetail")
  @ResponseBody
  public ResultBean<ProductResultVO> getNormalProductDetail(String code) {
    return new ResultBean<>(productService.getNormalProductDetailRef(code));
  }
}
