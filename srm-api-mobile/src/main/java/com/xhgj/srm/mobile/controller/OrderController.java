package com.xhgj.srm.mobile.controller;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.Constants_order;
import com.xhgj.srm.common.constants.Constants_LockName;
import com.xhgj.srm.common.dto.ExpressCompanyDTO;
import com.xhgj.srm.common.dto.OrderPlatformDTO;
import com.xhgj.srm.dto.order.AllowPaymentOrderDTO;
import com.xhgj.srm.dto.order.OrderDetailInvoiceDTO;
import com.xhgj.srm.dto.order.OrderWithdrawDTO;
import com.xhgj.srm.dto.order.param.AllowPaymentOrderQueryParam;
import com.xhgj.srm.dto.order.param.OrderWithdrawParam;
import com.xhgj.srm.dto.order.param.ProductPictureParam;
import com.xhgj.srm.dto.order.vo.ProductPictureVO;
import com.xhgj.srm.mobile.domain.SrmSupplierUserDetails;
import com.xhgj.srm.mobile.dto.FileDTO;
import com.xhgj.srm.mobile.dto.order.delivery.GetCountDTO;
import com.xhgj.srm.mobile.dto.order.BatchOrderInvoiceParams;
import com.xhgj.srm.mobile.dto.order.CustomerPaybackDTO;
import com.xhgj.srm.mobile.dto.order.CustomerPaybackParams;
import com.xhgj.srm.mobile.dto.order.DeliveryOrderDetailDTO;
import com.xhgj.srm.mobile.dto.order.DeliveryParamDTO;
import com.xhgj.srm.mobile.dto.order.InvoiceDTO;
import com.xhgj.srm.mobile.dto.order.MobileOrderTableDTO;
import com.xhgj.srm.mobile.dto.order.OrderInvoiceDTO;
import com.xhgj.srm.mobile.dto.order.OrderInvoiceInfoDTO;
import com.xhgj.srm.mobile.dto.order.OrderInvoicePageQuery;
import com.xhgj.srm.mobile.dto.order.OrderInvoiceParams;
import com.xhgj.srm.mobile.dto.order.OrderSomeStatusDTO;
import com.xhgj.srm.mobile.dto.order.SubmitOrderAcceptDTO;
import com.xhgj.srm.mobile.dto.order.param.OrderPageQueryParam;
import com.xhgj.srm.mobile.dto.order.vo.OrderCountVO;
import com.xhgj.srm.mobile.dto.order.vo.OrderDetailVO;
import com.xhgj.srm.mobile.dto.order.vo.OrderPageVO;
import com.xhgj.srm.mobile.dto.order.vo.OrderProductInfoVO;
import com.xhgj.srm.mobile.dto.scheme.SingleBaseParam;
import com.xhgj.srm.mobile.dto.supplier.ExportOrderListParam;
import com.xhgj.srm.mobile.handler.SupplierAuditor;
import com.xhgj.srm.mobile.service.FileService;
import com.xhgj.srm.mobile.service.OrderDetailService;
import com.xhgj.srm.mobile.service.OrderService;
import com.xhgj.srm.mobile.utils.SupplierSecurityUtil;
import com.xhgj.srm.request.vo.dock.FileByteAndType;
import com.xhgj.srm.service.ShareFileService;
import com.xhgj.srm.service.ShareOrderService;
import com.xhgj.srm.service.SharePlatformService;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.framework.web.dto.param.PageParam;
import com.xhiot.boot.mvc.base.PageResult;
import com.xhiot.boot.mvc.base.ResultBean;
import com.xhiot.boot.repeat.annotation.RepeatSubmit;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("order")
@Api(tags = {"订单接口"})
@Slf4j
@Validated
public class OrderController  {

  @Autowired private OrderService orderService;
  @Resource private OrderDetailService orderDetailService;
  @Autowired private FileService fileService;
  @Resource private SharePlatformService platformService;
  @Resource private RedissonClient redissonClient;
  @Resource private ShareOrderService shareOrderService;
  @Resource
  private SupplierAuditor supplierAuditor;
  @Resource private ShareFileService shareFileService;
  @Resource private SupplierSecurityUtil supplierSecurityUtil;


  @ApiOperation(value = "订单撤单")
  @ApiImplicitParams({
    @ApiImplicitParam(name = "orderNo", value = "订单号", dataType = "String", required = true),
    @ApiImplicitParam(name = "type", value = "下单平台", dataType = "String", required = true)
  })
  @PostMapping(value = "orderWithdraw")
  public ResultBean<List<OrderWithdrawDTO>> orderWithdraw(
      @RequestBody @Validated OrderWithdrawParam orderWithdrawParam) {
    log.error("接口已迁移至portal");
    throw new CheckException("接口迁移至portal");
  }

  @ApiOperation(value = "获取订单状态及其个数", notes = "获取订单状态及其个数")
  @ApiImplicitParams({
    @ApiImplicitParam(name = "supplierId", value = "供应商id", dataType = "String", required = true),
  })
  @GetMapping(value = "/getOrderStateAndCount")
  public ResultBean<OrderCountVO> getOrderStateAndCount(
      @RequestParam("supplierId") String supplierId) {
    return new ResultBean<>(orderService.getOrderStateAndCount(supplierId));
  }

  @ApiOperation(value = "分页获取可付款订单列表")
  @GetMapping(value = "/getAllowPaymentOrderPage")
  public ResultBean<PageResult<AllowPaymentOrderDTO>> getAllowPaymentOrderPage(
      @Valid AllowPaymentOrderQueryParam query, @Valid PageParam param) {
    return new ResultBean<>(orderService.getAllowPaymentOrderPage(query, param));
  }

  @ApiOperation(value = "分页获取订单信息", notes = "分页获取订单信息")
  @RequestMapping(value = "/getOrderPage", method = RequestMethod.GET)
  @ResponseBody
  public ResultBean<PageResult<OrderPageVO>> getOrderPage(@Validated OrderPageQueryParam param) {
    return new ResultBean<>(orderService.getOrderPage(param));
  }

  @ApiOperation(value = "获取订单详情", notes = "获取订单详情")
  @ApiImplicitParams({
    @ApiImplicitParam(name = "orderId", value = "订单id", dataType = "String", required = true),
  })
  @RequestMapping(value = "/getOrderDetail", method = RequestMethod.GET)
  @ResponseBody
  public ResultBean<OrderDetailVO> getOrderDetail(String orderId) {
    return new ResultBean<>(orderDetailService.getOrderDetail(orderId));
  }

  @ApiOperation(value = "获取订单商品、发货、退货详情", notes = "获取订单商品、发货、退货详情")
  @ApiImplicitParams({
    @ApiImplicitParam(name = "orderId", value = "订单id", dataType = "String", required = true),
  })
  @RequestMapping(value = "/getOrderProductInfo", method = RequestMethod.GET)
  @ResponseBody
  public ResultBean<OrderProductInfoVO> getOrderProductInfo(String orderId) {
    return new ResultBean<>(orderDetailService.getOrderProductInfo(orderId));
  }

  @ApiOperation(value = "批量获取订单商品、发货、退货详情", notes = "获取订单商品、发货、退货详情")
  @ApiImplicitParams({
    @ApiImplicitParam(
        name = "orderIds",
        value = "订单id集合，元素用逗号分割",
        dataType = "String",
        required = true),
  })
  @RequestMapping(value = "/batchGetOrderProductInfo", method = RequestMethod.GET)
  @ResponseBody
  public ResultBean<List<OrderProductInfoVO>> batchGetOrderProductInfo(@NotEmpty String orderIds) {
    String[] split = orderIds.split(",");
    List<String> arrayList = new ArrayList<>(split.length);
    Collections.addAll(arrayList, split);
    return new ResultBean<>(orderDetailService.batchGetOrderProductInfo(arrayList));
  }

  @ApiOperation("根据订单 id 获取详情发票信息")
  @ApiImplicitParams({
    @ApiImplicitParam(name = "orderId", value = "订单id", dataType = "String", required = true),
  })
  @GetMapping("getOrderDetailInvoiceByOrderId")
  public ResultBean<OrderDetailInvoiceDTO> getOrderDetailInvoiceByOrderId(
      @NotBlank(message = "订单 id 必传") @RequestParam String orderId) {
    return new ResultBean<>(orderService.getOrderDetailInvoiceByOrderId(orderId));
  }

  @ApiOperation(value = "批量获取订单详情", notes = "批量获取订单详情")
  @ApiImplicitParams({
    @ApiImplicitParam(name = "orderIds", value = "订单id集合", dataType = "String", required = true),
  })
  @RequestMapping(value = "/batchOrderDetail", method = RequestMethod.GET)
  public ResultBean<List<OrderDetailVO>> batchGetOrderProductById(
      @RequestParam("orderId") String orderIds) {
    String[] split = orderIds.split(",");
    List<String> arrayList = new ArrayList<>(split.length);
    Collections.addAll(arrayList, split);
    return new ResultBean<>(orderDetailService.batchGetOrderProductById(arrayList));
  }

  @ApiOperation(value = "新建发货单")
  @PostMapping(
      value = "orderDelivery",
      consumes = {MediaType.APPLICATION_JSON_VALUE})
  @RepeatSubmit(interval = 60000)
  public ResultBean<Boolean> orderDelivery(
      @RequestBody @Validated DeliveryParamDTO deliveryParamDTO) {
    orderService.orderDelivery(deliveryParamDTO);
    return new ResultBean<>(true, "操作成功!");
  }

  @ApiOperation(value = "变更发货单")
  @PostMapping(
      value = "orderDeliveryUpdate",
      consumes = {MediaType.APPLICATION_JSON_VALUE})
  public ResultBean<Boolean> orderDeliveryUpdate(
      @RequestBody @Validated DeliveryParamDTO deliveryParamDTO) {
    orderService.orderDeliveryUpdate(deliveryParamDTO);
    return new ResultBean<>(true, "操作成功!");
  }

  @ApiOperation(value = "获取发货单详情", notes = "获取发货单详情")
  @ApiImplicitParams({
    @ApiImplicitParam(
        name = "orderDeliveryId",
        value = "发货单id",
        dataType = "String",
        required = true),
  })
  @RequestMapping(value = "/getOrderDeliveryDetail", method = RequestMethod.GET)
  @ResponseBody
  public ResultBean<DeliveryOrderDetailDTO> getOrderDeliveryDetail(String orderDeliveryId) {
    return new ResultBean<>(orderService.getOrderDeliveryDetail(orderDeliveryId));
  }

  @SneakyThrows
  @ApiOperation(value = "验收单模板下载")
  @PostMapping(
      value = "downloadAcceptTemp",
      consumes = {MediaType.APPLICATION_JSON_VALUE})
  public ResponseEntity<byte[]> downloadAcceptTemp(
      @RequestBody @Valid SingleBaseParam singleBaseParam) {
    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
    FileByteAndType fileByteAndType = orderService.downloadAcceptTemp(singleBaseParam.getId());
    byte[] bytes = fileByteAndType.getFileByte();
    String defaultName = fileByteAndType.getFileName();
    if (fileByteAndType.isMultiFile()) {
      headers.setContentDispositionFormData(
          "attachment",
          URLEncoder.encode(defaultName + System.currentTimeMillis() + ".zip", "UTF-8"));
    } else {
      headers.setContentDispositionFormData(
          "attachment",
          URLEncoder.encode(defaultName + System.currentTimeMillis() + ".docx", "UTF-8"));
    }
    return new ResponseEntity<>(bytes, headers, HttpStatus.CREATED);
  }

  @ApiOperation(value = "获取物流公司列表", notes = "获取物流公司列表")
  @RequestMapping(value = "/getExpressCompanyList", method = RequestMethod.GET)
  @ApiImplicitParams({
    @ApiImplicitParam(name = "type", value = "下单平台", dataType = "String", required = true),
  })
  @ResponseBody
  public ResultBean<List<ExpressCompanyDTO>> getExpressCompanyList(
      @RequestParam("type") String type) {
    return new ResultBean<>(orderService.getExpressCompanyList(type));
  }

  @ApiOperation(value = "获取OMS承运商物流公司列表", notes = "获取OMS承运商物流公司列表")
  @GetMapping(value = "/getLogisticsCompany")
  public ResultBean<List<ExpressCompanyDTO>> getLogisticsCompanies() {
    return new ResultBean<>(orderService.getLogisticsCompanies());
  }

  @SneakyThrows
  @ApiOperation(value = "导出订单明细")
  @PostMapping(
      value = "exportOrderDetail",
      consumes = {MediaType.APPLICATION_JSON_VALUE})
  public ResponseEntity<byte[]> exportOrderDetail(
      @RequestBody @Valid ExportOrderListParam exportOrderListParam) {
    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
    headers.setContentDispositionFormData(
        "attachment", URLEncoder.encode("orderDetail.xlsx", "UTF-8"));
    byte[] bytes =
        orderService.exportOrderDetail(
            exportOrderListParam.getIds(),
            exportOrderListParam.getSupplierId(),
            exportOrderListParam.getState());
    return new ResponseEntity<>(bytes, headers, HttpStatus.CREATED);
  }

  @ApiOperation(value = "获取对接平台列表")
  @GetMapping(value = "getOrderPlatformList")
  public ResultBean<List<OrderPlatformDTO>> getOrderPlatformList() {
    return new ResultBean<>(platformService.findAll());
  }

  @ApiOperation(value = "提交开票申请")
  @PostMapping("submitOrderInvoice")
  @RepeatSubmit
  public ResultBean<Boolean> submitOrderInvoice(
      @Valid @RequestBody OrderInvoiceParams orderInvoiceParams) {
    RLock lock = null;
    try {
      lock =
          redissonClient.getLock(
              Constants_LockName.SUBMIT_ORDER_INVOICE + orderInvoiceParams.getSupplierId());
      lock.lock();
      orderService.submitOrderInvoice(orderInvoiceParams);
    } catch (CheckException checkException) {
      throw checkException;
    } catch (Exception e) {
      log.error(ExceptionUtil.stacktraceToString(e, -1));
      throw new CheckException("未知异常，请联系管理员！");
    } finally {
      if (Objects.nonNull(lock)) {
        lock.unlock();
      }
    }
    return new ResultBean<>(true, "操作成功");
  }

  @ApiOperation(value = "订单合并开票申请")
  @PostMapping("batchSubmitOrderInvoice")
  public ResultBean<Boolean> batchSubmitOrderInvoice(
      @Valid @RequestBody BatchOrderInvoiceParams batchOrderInvoiceParams,
      UsernamePasswordAuthenticationToken usernamePasswordAuthenticationToken) {
    RLock lock = null;
    try {
      lock =
          redissonClient.getLock(
              Constants_LockName.SUBMIT_ORDER_INVOICE + batchOrderInvoiceParams.getSupplierId());
      lock.lock();
      SrmSupplierUserDetails userDetails =
          (SrmSupplierUserDetails) usernamePasswordAuthenticationToken.getPrincipal();
      String userId = userDetails.supplierUser().getId();
      orderService.batchSubmitOrderInvoice(batchOrderInvoiceParams, userId);
    } catch (CheckException checkException) {
      throw checkException;
    } catch (Exception e) {
      log.error(ExceptionUtil.stacktraceToString(e, -1));
      throw new CheckException("未知异常，请联系管理员！");
    } finally {
      if (Objects.nonNull(lock)) {
        lock.unlock();
      }
    }

    return new ResultBean<>(true, "操作成功");
  }

  @ApiOperation(value = "获取发票申请详情")
  @GetMapping("orderInvoiceApplyInfo")
  public ResultBean<OrderInvoiceInfoDTO> getOrderInvoiceApplyInfo(
      @NotBlank(message = "开票申请单 id 必传") @RequestParam String orderInvoiceId) {
    return new ResultBean<>(orderService.getOrderInvoiceApplyInfo(orderInvoiceId));
  }

  @ApiOperation(value = "获取可开票订单")
  @GetMapping("getOrderInvoicablePage")
  public ResultBean<PageResult<OrderInvoiceDTO>> getOrderInvoicablePage(
      OrderInvoicePageQuery orderInvoicePageQuery,
      @NotBlank(message = "供应商 id 必传") @RequestParam String supplierId) {
    orderInvoicePageQuery.setInvoicingState(ListUtil.toList(Constants_order.INVOICE_STATE_UN));
    orderInvoicePageQuery.setOrderState(Constants_order.ORDER_STATE_ACCEPTED);
    return new ResultBean<>(
        orderService.getPermitOrderInvoicePage(
            OrderInvoiceDTO.class, orderInvoicePageQuery, false, supplierId));
  }

  @ApiOperation(value = "获取开票申请订单")
  @GetMapping("getOrderInvoicingRequisitionPage")
  public ResultBean<PageResult<InvoiceDTO>> getOrderInvoicingRequisitionPage(
      OrderInvoicePageQuery orderInvoicePageQuery,
      @NotBlank(message = "供应商 id 必传") @RequestParam String supplierId) {
    PageResult<InvoiceDTO> orderInvoicePage =
        orderService.getOrderInvoicePage(InvoiceDTO.class, orderInvoicePageQuery, true, supplierId);
    return new ResultBean<>(orderInvoicePage);
  }

  @ApiOperation(value = "提交签收信息", consumes = MediaType.APPLICATION_JSON_VALUE)
  @PostMapping("submitOrderAccept")
  @RepeatSubmit
  public ResultBean<Boolean> submitOrderAccept(
      @RequestBody @Valid SubmitOrderAcceptDTO submitOrderAcceptDTO) {
    orderService.submitOrderAccept(submitOrderAcceptDTO);
    return new ResultBean<>(true);
  }

  @ApiOperation("根据订单id下载发票")
  @PostMapping(value = "downloadInvoiceZipByOrderId")
  public ResponseEntity<byte[]> downloadInvoiceZipByOrderId(
      @RequestParam String orderId, HttpServletResponse response) throws IOException {
    response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
    response.setHeader(
        "Content-disposition", "attachment;filename=" + System.currentTimeMillis() + ".zip");
    response.flushBuffer();
    // !!!!! orderId实际为invoiceId
    String invoiceId = orderId;
    return ResponseEntity.ok()
        .body(fileService.downloadZipMoreFile(shareOrderService.getInvoiceFileIdListByOrderId(invoiceId)));
  }

  @ApiOperation("根据开票类型获取对应的数量")
  @GetMapping(value = "getInvoiceCountByType")
  public ResultBean<Long> getInvoiceCountByType(
      @NotBlank(message = "开票状态必传") @RequestParam String type,
      @NotBlank(message = "供应商 id") @RequestParam String supplierId) {
    return new ResultBean<>(orderService.getInvoiceCountByType(type, supplierId));
  }

  @ApiOperation(value = "根据开票申请单号获取关联订单详情", notes = "根据开票申请单号获取关联订单详情")
  @RequestMapping(value = "/orderDetailsByApplicationNum", method = RequestMethod.GET)
  public ResultBean<List<OrderDetailVO>> getOrderDetailsByApplicationNumber(
      String applicationNumber) {
    return new ResultBean<>(orderService.getOrderDetailsByApplicationNumber(applicationNumber));
  }

  @ApiOperation("根据订单id验证签收凭证、客户回款、供应商开票")
  @GetMapping("/validateByOrderIds")
  public ResultBean<List<OrderSomeStatusDTO>> validateByOrderIds(String orderIdList) {
    return new ResultBean<>(orderService.validateByOrderIds(orderIdList));
  }

  @ApiOperation("获取客户回款列表")
  @PostMapping("/getCustomerPaybackList")
  public ResultBean<List<CustomerPaybackDTO>> getCustomerPaybackList(
      @RequestBody CustomerPaybackParams params) {
    return new ResultBean<>(orderService.getCustomerPaybackList(params));
  }

  /** 开放接口，供履约调用 */
  @ApiOperation(value = "通过订单id查询验收单的接口", notes = "通过订单id查询验收单的接口")
  @ApiImplicitParams({
    @ApiImplicitParam(name = "type", value = "平台", dataType = "String", required = true),
    @ApiImplicitParam(name = "orderNo", value = "订单号", dataType = "String", required = true)
  })
  @GetMapping(value = "/getOrderAcceptedList")
  public ResultBean<List<FileDTO>> getOrderAcceptedList(
      @RequestParam("orderNo") String orderNo, @RequestParam("type") String type) {
    log.error("接口已迁移至portal");
    throw new CheckException("接口迁移至portal");
  }

  @ApiOperation(value = "批量获取物料图片", notes = "批量获取物料图片")
  @ApiImplicitParams({
    @ApiImplicitParam(
        name = "productCodes",
        value = "物料编码集合",
        dataType = "List<String>",
        required = true),
  })
  @RequestMapping(value = "/batchGetProductPicture", method = RequestMethod.POST)
  public ResultBean<List<ProductPictureVO>> batchGetProductPicture(
      @RequestBody ProductPictureParam param) {
    return new ResultBean<>(shareOrderService.getProductPicture(param.getProductCodes()));
  }

  @GetMapping("getPage")
  @ApiOperation("通过订单号搜索")
  public ResultBean<PageResult<MobileOrderTableDTO>> getPage(
      @RequestParam(required = false) String orderNo,
      @RequestParam(defaultValue = "1") Integer pageNo,
      @RequestParam(defaultValue = "20") Integer pageSize) {
    return new ResultBean<>(
        orderService.get(supplierAuditor.getCurrentAuditorId(), orderNo, pageNo, pageSize));
  }

  @GetMapping("getPendingNum")
  @ApiOperation("获取待处理数量")
  public ResultBean<GetCountDTO> getPendingNum() {
    return new ResultBean<>(orderService.getPendingNum(supplierAuditor.getCurrentAuditorId()));
  }

  /**
   * 落地商订单的采购合同上传
   */
  @PostMapping("/uploadPurchaseContract")
  public ResultBean<Boolean> uploadPurchaseContract(MultipartFile[] file, String orderId) {
    String userId = supplierSecurityUtil.getSupplierUserDetails().getId();
    shareFileService.uploadFile(file, orderId, "purchaseContract",
        Constants.FILE_TYPE_LANDING_PURCHASE_CONTRACT, userId);
    return new ResultBean<>(true);
  }

  /**
   * 落地商订单的采购合同导出
   * @param orderId
   * @return
   */
  @PostMapping("/downloadPurchaseOrderContract")
  public ResponseEntity<byte[]> downloadPurchaseOrderContract(@RequestParam String orderId) {
    byte[] bytes = shareOrderService.downloadLandingMerchantPurchaseOrderContract(orderId);
    HttpHeaders headers = new HttpHeaders();
    String fileName = "采购合同" + "-" + System.currentTimeMillis() + ".pdf";
    // 对文件名进行URL编码
    String encodedFileName;
    try {
      encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
    } catch (UnsupportedEncodingException e) {
      e.printStackTrace();
      encodedFileName = fileName;
    }
    headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
    headers.setContentDispositionFormData("attachment", encodedFileName);
    return ResponseEntity.ok().headers(headers).body(bytes);
  }
}
