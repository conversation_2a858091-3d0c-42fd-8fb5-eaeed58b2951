package com.xhgj.srm.mobile.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.xhgj.mall.express.support.dto.LogisticsInfoDTO;
import com.xhgj.srm.mobile.dto.order.delivery.OrderDeliveryCountDTO;
import com.xhgj.srm.mobile.dto.order.delivery.OrderDeliveryPageDTO;
import com.xhgj.srm.mobile.dto.order.delivery.OrderDeliveryProductDetailDTO;
import com.xhgj.srm.mobile.dto.order.delivery.SupplierOrderDeliveryDTO;
import com.xhgj.srm.mobile.dto.order.delivery.query.DeliveryOrderQueryParam;
import com.xhgj.srm.mobile.dto.order.delivery.query.SupplierOrderDeliveryQueryParam;
import com.xhgj.srm.mobile.dto.order.logistics.LogisticsInformationParam;
import com.xhgj.srm.mobile.factory.MapStructFactory;
import com.xhgj.srm.mobile.handler.SupplierAuditor;
import com.xhgj.srm.mobile.service.OrderDetailService;
import com.xhgj.srm.mobile.service.OrderService;
import com.xhgj.srm.mobile.service.SupplierOrderToFormService;
import com.xhiot.boot.mvc.base.PageResult;
import com.xhiot.boot.mvc.base.ResultBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import javax.annotation.Resource;
import javax.validation.Valid;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 接单发货
 *
 * <AUTHOR>
 * @date 2024/06/17 10:15
 */
@RestController
@Api(tags = {"订单发货管理"})
@RequestMapping("order/delivery")
@Slf4j
public class OrderDeliveryController {
  @Resource private OrderService orderService;
  @Resource private OrderDetailService orderDetailService;
  @Resource private SupplierAuditor supplierAuditor;
  @Autowired private SupplierOrderToFormService supplierOrderToFormService;

  @ApiOperation(value = "分页获取发货订单信息")
  @GetMapping(value = "/getDeliveryOrderPage")
  public ResultBean<PageResult<OrderDeliveryPageDTO>> getDeliveryOrderPage(
      @Validated DeliveryOrderQueryParam param) {
    return new ResultBean<>(
        orderService.getDeliveryOrderPage(param, supplierAuditor.getCurrentAuditorId()));
  }

  @ApiOperation(value = "获取发货清单")
  @GetMapping(value = "/getProductInfoPage")
  public ResultBean<PageResult<OrderDeliveryProductDetailDTO>> getProductInfoPage(
      @RequestParam String orderId,
      @RequestParam(required = false) String keyWord,
      @RequestParam Integer pageNo,
      @RequestParam Integer pageSize,
      @RequestParam(required = false) String ids) {
    return new ResultBean<>(
        orderDetailService.getProductInfoPage(orderId, keyWord, pageNo, pageSize, ids));
  }

  @ApiOperation(value = "分页获取供应商订单列表")
  @GetMapping("getSupplierOrderDeliveryPage")
  public ResultBean<PageResult<SupplierOrderDeliveryDTO>> getSupplierOrderDeliveryPage(
      @Valid SupplierOrderDeliveryQueryParam param) {
    return new ResultBean<>(
        orderService.getSupplierOrderDeliveryPage(param, supplierAuditor.getCurrentAuditorId()));
  }

  @GetMapping("getOrderDeliveryCount")
  @ApiOperation("获取接单发货数量")
  public ResultBean<OrderDeliveryCountDTO> getOrderDeliveryCount() {
    return new ResultBean<>(
        orderService.getOrderDeliveryCount(supplierAuditor.getCurrentAuditorId()));
  }

  @SneakyThrows
  @ApiOperation(value = "识别面单")
  @ApiImplicitParams({
    @ApiImplicitParam(name = "url", value = "图片地址"),
  })
  @GetMapping("/identifyingWaybill")
  public ResultBean<LogisticsInfoDTO> identifyingWaybill(@RequestParam String url) {
    return new ResultBean<>(orderService.identifyingWaybill(url));
  }

  @ApiOperation("获取物流信息")
  @GetMapping(value = "getLogisticsInformation")
  public ResultBean<JSONArray> getLogisticsInformation(LogisticsInformationParam param) {
    JSONArray logisticsInformation;
    if (StrUtil.isEmpty(param.getId())) {
      logisticsInformation = orderService.getLogisticsInformation(param);
    } else {
      com.xhgj.srm.mobile.dto.supplierOrder.LogisticsInformationParam param1 = MapStructFactory.INSTANCE.toLogisticsInformationParam(param);
      logisticsInformation = supplierOrderToFormService.getLogisticsInformation(param1);
    }
    return new ResultBean<>(logisticsInformation);
  }
}
