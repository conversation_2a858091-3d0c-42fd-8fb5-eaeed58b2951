package com.xhgj.srm.mobile.dto.supplierOrder;

import com.xhgj.srm.mobile.dto.FileDTO;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * <AUTHOR> @ClassName AddSupplierOrderDeliveryParam
 */
@Data
public class AddSupplierOrderDeliveryParam {
  @ApiModelProperty(value = "supplierId", required = true)
  @NotEmpty
  private String supplierId;

  @ApiModelProperty(value = "id", required = true)
  @NotEmpty(message = "订单id不能为空")
  private String id;

  @ApiModelProperty(value = "快递公司", required = true)
  @NotEmpty(message = "快递公司不能为空！")
  private String logisticsCompany;

  @ApiModelProperty("物流单号/送货人手机号(厂家配送时传入)")
  private String trackNum;

  @ApiModelProperty("物流编码")
  private String logisticsCode;

  @ApiModelProperty(value = "咸亨电商物流公司编码", required = true)
  private String code;

  @ApiModelProperty("附件")
  private List<FileDTO> fileList;

  @ApiModelProperty("件数")
  private String number;

  @ApiModelProperty(value = "发货物料明细")
  private List<ProductDetailParam> productDetailList;
}
