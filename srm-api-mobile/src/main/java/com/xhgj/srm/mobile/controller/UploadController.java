package com.xhgj.srm.mobile.controller;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.config.SrmConfig;
import com.xhgj.srm.common.utils.FileNameUtils;
import com.xhgj.srm.common.utils.HttpUtil;
import com.xhgj.srm.common.utils.oss.service.OssService;
import com.xhgj.srm.jpa.dao.FileDao;
import com.xhgj.srm.jpa.entity.File;
import com.xhgj.srm.jpa.repository.FileRepository;
import com.xhgj.srm.jpa.repository.OrderRepository;
import com.xhgj.srm.mobile.dto.UpFileDTO;
import com.xhgj.srm.mobile.dto.UploadReturnData;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.StringUtils;
import com.xhiot.boot.mvc.base.ResultBean;
import com.xhiot.boot.upload.service.BootUploadFileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * @ClassName UploadController Create by Liuyq on 2021/6/3 10:31
 */
@Slf4j
@RestController
@RequestMapping("/upload")
@Api(tags = {"上传接口"})
public class UploadController {
  @Autowired BootUploadFileService uploadFileService;
  @Autowired private OssService ossService;
  @Autowired private FileRepository fileRepository;

  // 上传文件的默认保存路径
  private final String configPath = "srm/upload";

  private final String baseUrl;

  public UploadController(SrmConfig config) {
    baseUrl = config.getUploadUrl();
  }

  @ApiOperation(value = "上传文件")
  @ApiImplicitParams({
    @ApiImplicitParam(name = "file", value = "文件", allowMultiple = true, dataType = "__file"),
    @ApiImplicitParam(name = "code", value = "供应商编码/商品编码/订单id"),
    @ApiImplicitParam(
        name = "type",
        value = "类别 上传品牌图片 type=brand/上传证件 type = supplier/商品 type = product"),
    @ApiImplicitParam(name = "fileType", value = "类别 1-主图,2-详情图")
  })
  @PostMapping("/uploadPic")
  public ResultBean<UploadReturnData> uploadPic(
      MultipartFile file, String code, String type, String fileType) {
    String filename = FileNameUtils.encodeKeepChinese(file.getOriginalFilename());
    String newFilePath = ""; // 回传文件存放路径
    String newFileName = ""; // 回传文件名称
    String osspath = "";
    code = !StringUtils.isNullOrEmpty(code) ? code : "";
    UploadReturnData uploadReturnData = new UploadReturnData();
    if (StrUtil.equalsAny(fileType, Constants.PICTURE_TYPE_ZT, Constants.PICTURE_TYPE_XQ)
        && StrUtil.isEmpty(code)) {
      throw new CheckException("编码参数缺失，路径异常，请联系管理员。");
    }
    if (!"".equals(filename.trim())) {
      if (Constants.PICTURE_TYPE_ZT.equals(fileType)) {
        newFileName = filename;
      } else if (Constants.PICTURE_TYPE_XQ.equals(fileType)) {
        if (filename.startsWith("xq")) {
          newFileName = filename;
        } else {
          throw new CheckException("文件名上传有误");
        }
      } else {
        newFileName = System.currentTimeMillis() + "." + FileUtil.getSuffix(filename); // 文件新名称
      }
    }
    if (!StringUtils.isNullOrEmpty(code)) {
      newFilePath = configPath + "/" + type + "/" + code + "/" + newFileName; // 文件存放路径
      osspath = configPath + "/" + type + "/" + code;
    } else {
      newFilePath = configPath + "/" + type + "/" + newFileName; // 文件存放路径
      osspath = configPath + "/" + type;
    }
    // 上传到OSS
    boolean result = ossService.addHeadImage(file, osspath, newFileName);
    if (!result) {
      log.error("文件上传oss失败:" + filename);
    }
    uploadReturnData.setFilename(filename);
    uploadReturnData.setNewFilePath(newFilePath);
    uploadReturnData.setNewFileName(newFileName);
    uploadReturnData.setBaseUrl(baseUrl);
    return new ResultBean<>(uploadReturnData);
  }

  @ApiOperation(value = "批量上传文件")
  @ApiImplicitParams({
    @ApiImplicitParam(name = "file", value = "文件", allowMultiple = true, dataType = "__file"),
    @ApiImplicitParam(name = "code", value = "订单id/对账单id"),
    @ApiImplicitParam(name = "uploadMan", value = "文件上传人"),
    @ApiImplicitParam(
        name = "type",
        value =
            "类别 验收单 type = account"
                + "/发票 type = invoice"
                + "/两单 type = twoOrder"
                + "/落地商发票 type = orderInvoice"
                + "/落地商附件 type = orderOtherFile"),
  })
  @PostMapping("/uploadFiles")
  public ResultBean<List<UpFileDTO>> uploadFiles(
      MultipartFile[] file, String code, String type, String uploadMan) {
    long currentTimeMillis = System.currentTimeMillis();
    List<UpFileDTO> upFileDTOS = new ArrayList<>();
    if (ArrayUtil.isNotEmpty(file)) {
      for (MultipartFile f : file) {
        String filename = FileNameUtils.encodeKeepChinese(f.getOriginalFilename());
        if (filename == null) {
          throw new CheckException("获取文件异常，请联系管理员。");
        }
        String newFilePath = ""; // 回传文件存放路径
        String ossPath = "";
        code = !StringUtils.isNullOrEmpty(code) ? code : "";
        UpFileDTO upFileDTO = new UpFileDTO();
        if (!"".equals(filename.trim())) {
          long time = System.currentTimeMillis();
          if (!StringUtils.isNullOrEmpty(code)) {
            newFilePath = configPath + "/" + type + "/" + code + "/" + time + "/" + filename; // 文件存放路径
            ossPath = configPath + "/" + type + "/" + code + "/" + time;
          } else {
            newFilePath = configPath + "/" + type + "/" + time + "/" + filename; // 文件存放路径
            ossPath = configPath + "/" + type + "/" + time;
          }
          // 上传到OSS
          boolean result = ossService.addHeadImage(f, ossPath, filename);
          if (!result) {
            log.error("文件上传oss失败:" + filename);
          }
          if ("account".equals(type)
              || "invoice".equals(type)
              || "twoOrder".equals(type)
              || "orderInvoice".equals(type)
              || "orderOtherFile".equals(type)) {
            File newFile = new File();
            if ("account".equals(type)) {
              if (StrUtil.isBlank(uploadMan)) {
                throw new CheckException("上传人名称必传，请联系管理员！");
              }
              newFile.setRelationType(Constants.FILE_TYPE_YANSHOU);
            } else if ("invoice".equals(type)) {
              newFile.setRelationType(Constants.FILE_TYPE_INVOICE);
            } else if ("twoOrder".equals(type)) {
              newFile.setRelationType(Constants.FILE_TYPE_TWO_ORDERS);
              if (StrUtil.isBlank(uploadMan)) {
                throw new CheckException("上传人名称必传，请联系管理员！");
              }
            } else if ("orderInvoice".equals(type)) {
              newFile.setRelationType(Constants.FILE_TYPE_ODER_INVOICE);
            } else if ("orderOtherFile".equals(type)) {
              newFile.setRelationType(Constants.FILE_TYPE_ODER_OTHER_FILE);
            }
            newFile.setUploadMan(uploadMan);
            newFile.setRelationId(code);
            newFile.setName(filename);
            newFile.setUrl(newFilePath);
            newFile.setDescription(filename);
            // 附件类型
            String fileLast = filename.substring(filename.lastIndexOf(".") + 1).toLowerCase();
            String fileType = Constants.FILE_TYPE_PICTURE;
            if ("doc".equalsIgnoreCase(fileLast)
                || "docx".equalsIgnoreCase(fileLast)
                || "pdf".equalsIgnoreCase(fileLast)
                || "ppt".equalsIgnoreCase(fileLast)
                || "excel".equalsIgnoreCase(fileLast)) {
              fileType = Constants.FILE_TYPE_WORD;
            }
            if ("jpg".equalsIgnoreCase(fileLast)
                || "png".equalsIgnoreCase(fileLast)
                || "bmp".equalsIgnoreCase(fileLast)
                || "gif".equalsIgnoreCase(fileLast)) {
              fileType = Constants.FILE_TYPE_PICTURE;
            }
            if ("rar".equalsIgnoreCase(fileLast) || "zip".equalsIgnoreCase(fileLast)) {
              fileType = Constants.FILE_TYPE_RARZIP;
            }
            newFile.setType(fileType);
            newFile.setState(Constants.STATE_OK);
            newFile.setCreateTime(currentTimeMillis);
            fileRepository.save(newFile);
            upFileDTO.setId(newFile.getId());
          }
          upFileDTO.setName(filename);
          upFileDTO.setUrl(newFilePath);
          upFileDTO.setBaseUrl(baseUrl);
          upFileDTOS.add(upFileDTO);
        }
      }
    }
    return new ResultBean<>(upFileDTOS);
  }

  @ApiOperation(value = "批量上传文件 - 不存表")
  @ApiImplicitParams({
    @ApiImplicitParam(name = "file", value = "文件", allowMultiple = true, dataType = "__file"),
    @ApiImplicitParam(name = "code", value = "与文件关联的id"),
    @ApiImplicitParam(name = "uploadMan", value = "文件上传人"),
    @ApiImplicitParam(
        name = "type",
        value =
            "类别 验收单 type = account"
                + "/发票 type = invoice"
                + "/两单 type = twoOrder"
                + "/落地商发票 type = orderInvoice"
                + "/落地商附件 type = orderOtherFile"
                + "/落地商合同 type = landingMerchantContract"
                + "/落地商合同补充协议 = landingMerchantContractSupplement"
                + "/物料主图 = mainPicture"
                + "/物料详情图 = detailsPicture"),
  })
  @PostMapping("/uploadFilesNoSave")
  public ResultBean<List<UpFileDTO>> uploadFilesNoSave(
      MultipartFile[] file, String code, String type) {
    List<UpFileDTO> upFileDTOS = new ArrayList<>();
    if (ArrayUtil.isNotEmpty(file)) {
      for (MultipartFile f : file) {
        String filename = FileNameUtils.encodeKeepChinese(f.getOriginalFilename());
        if (filename == null) {
          throw new CheckException("获取文件异常，请联系管理员。");
        }
        String newFilePath = ""; // 回传文件存放路径
        String ossPath = "";
        code = !StringUtils.isNullOrEmpty(code) ? code : "";
        UpFileDTO upFileDTO = new UpFileDTO();
        if (!"".equals(filename.trim())) {
          long time = System.currentTimeMillis();
          if (!StringUtils.isNullOrEmpty(code)) {
            newFilePath = configPath + "/" + type + "/" + code + "/" + time + "/" + filename; // 文件存放路径
            ossPath = configPath + "/" + type + "/" + code + "/" + time;
          } else {
            newFilePath = configPath + "/" + type + "/" + time + "/" + filename; // 文件存放路径
            ossPath = configPath + "/" + type + "/" + time;
          }
          // 上传到OSS
          boolean result = ossService.addHeadImage(f, ossPath, filename);
          if (!result) {
            log.error("文件上传oss失败:" + filename);
          }
          upFileDTO.setName(filename);
          upFileDTO.setUrl(newFilePath);
          upFileDTO.setBaseUrl(baseUrl);
          upFileDTOS.add(upFileDTO);
        }
      }
    }
    return new ResultBean<>(upFileDTOS);
  }
}
