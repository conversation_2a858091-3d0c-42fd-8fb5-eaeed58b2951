package com.xhgj.srm.mobile.service;

import com.xhgj.srm.jpa.entity.File;
import com.xhgj.srm.mobile.dto.FileDTO;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2021/3/1 18:46
 */
public interface FileService extends BootBaseService<File, String> {

  /**
   * 将供应商的协议文件复制到目标供应商副本
   *
   * @param supplierId 供应商 id
   * @param supplierFbId 目标供应商副本 id
   */
  void copySupplierAgreeFileToSupplierFb(String supplierId, String supplierFbId);

  /**
   * 将供应商的附件复制到目标供应商副本
   *
   * @param supplierId 供应商 id
   * @param supplierFbId 目标供应商副本 id
   */
  void copySupplierFileToSupplierFb(String supplierId, String supplierFbId);

  /**
   * 删除供应商的附件(除协议)
   *
   * @param supplierId 供应商 id
   * @since 16:51 2019/8/28
   */
  void deleteAllFileExxy(String supplierId);

  void updateSupplierFilesByFb(String supplierId, String fbid);

  void updateSupplierFbFilesByFb(String oldFbId, String fbid);

  void updateSupplierFbXyByFb(String oldFbId, String fbid);

  byte[] downloadZipMoreFile(List<String> fileIdList);

  /**
   * 添加文件（与oss服务器上的文件进行关联，此时该文件应该已经上传到oss上并且拿到url）
   *
   * @param contractFile
   * @param userId
   * @param relationId
   * @param relationType
   */
  void saveFile(FileDTO contractFile, String userId, String relationId, String relationType);

  /**
   * 更改文件关联id
   *
   * @param fileId
   * @param relationId
   * @return
   */
  boolean updateRelevanceId(String fileId, String relationId);

  /**
   * 根据关联 id 和关联类型获取附件列表
   *
   * @param relationId 关联 id
   * @param relationType 关联类型
   */
  List<File> getFileListByIdAndType(String relationId, String relationType);

  /**
   * 删除文件
   *
   * @param relationId 关联id
   * @param relationType 关联类型
   */
  void deleteFileByRelationIdAndType(String relationId, String relationType);

  /**
   * 查询第一个根据关联id和关联类型
   *
   * @param relationId 关联id
   * @param relationType 关联类型
   * @return Optional<File>
   */
  Optional<File> findFirstByRelationIdAndRelationType(String relationId, String relationType);
}
