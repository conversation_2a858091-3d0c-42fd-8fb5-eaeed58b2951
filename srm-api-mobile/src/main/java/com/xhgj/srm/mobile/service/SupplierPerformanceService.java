package com.xhgj.srm.mobile.service;

import com.xhgj.srm.jpa.entity.SupplierPerformance;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import java.util.List;
import java.util.Optional;

public interface SupplierPerformanceService extends BootBaseService<SupplierPerformance, String> {

  /**
   * 根据供应商 id 和平台编码获取履约信息
   *
   * @param supplierId 供应商 id 必传
   * @param platformCode 平台编码 必传
   * @return
   */
  SupplierPerformance getFirstBySupplierIdAndPlatformCode(String supplierId, String platformCode);

  /**
   * 查询采购手机号(落地商履约信息中配置的采购人）
   *
   * @param supplierId
   * @param platformCode
   * @return
   */
  String getPurchasePhoneNumber(String supplierId, String platformCode);

  /** 获取对接助理的OAId信息 */
  Optional<String> getDockingAssistant(String supplierId, String platformCode);

  /**
   * @param supplierId 供应商id
   * @param status 生效状态
   * @return 履约信息集合
   */
  List<SupplierPerformance> findBySupplierIdAndStatus(String supplierId, String status);

  /**
   * 查询供应商下所有平台，包含不生效的。
   *
   * @param supplierId 供应商id
   * @return List<String>
   */
  List<String> findPlatformCodeBySupplierId(String supplierId);
}
