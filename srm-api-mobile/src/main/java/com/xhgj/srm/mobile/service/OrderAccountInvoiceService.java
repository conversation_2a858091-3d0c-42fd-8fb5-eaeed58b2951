package com.xhgj.srm.mobile.service;

import com.xhgj.srm.jpa.entity.OrderSupplierInvoice;
import com.xhgj.srm.mobile.dto.account.OrderAccountInvoiceInfo;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/1/9 14:52
 */
public interface OrderAccountInvoiceService extends BootBaseService<OrderSupplierInvoice, String> {

  /**
   * 通过对账 id 获得发票信息
   *
   * @param id 对账 id 必传
   */
  List<OrderAccountInvoiceInfo> getByAccountId(String id, String url);

  /**
   * 创建对账单开票信息
   *
   * @param invoiceNum 发票号
   * @param invoiceCode 发票代码
   * @param invoiceTime 开票时间
   * @param price 金额
   * @param logisticsCompany 物流公司
   * @param logisticsNum 物流单号
   * @param orderAccountId 对账单 id
   */
  OrderSupplierInvoice createOrderAccountInvoice(
      String invoiceNum,
      String invoiceCode,
      Long invoiceTime,
      BigDecimal price,
      String logisticsCompany,
      String logisticsNum,
      String orderAccountId);
}
