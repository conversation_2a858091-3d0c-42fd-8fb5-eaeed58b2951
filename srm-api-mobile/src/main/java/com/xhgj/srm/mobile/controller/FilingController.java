package com.xhgj.srm.mobile.controller;

import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.constants.Constants_LockName;
import com.xhgj.srm.mobile.dto.filing.FilingDetailDTO;
import com.xhgj.srm.mobile.dto.filing.FilingOrderNoParamDTO;
import com.xhgj.srm.mobile.dto.filing.FilingPageDTO;
import com.xhgj.srm.mobile.dto.filing.FilingParamDTO;
import com.xhgj.srm.mobile.dto.filing.OrderNoFilingDetailDTO;
import com.xhgj.srm.mobile.service.OrderFilingService;
import com.xhiot.boot.mvc.base.PageResult;
import com.xhiot.boot.mvc.base.ResultBean;
import com.xhiot.boot.repeat.annotation.RepeatSubmit;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;

@RestController
@RequestMapping("filing")
@Api(tags = {"报备单接口"})
@Slf4j
public class FilingController {

  @Autowired private OrderFilingService orderFilingService;
  @Resource
  private RedissonClient redissonClient;

  @ApiOperation(value = "分页获取报备单信息", notes = "分页获取报备单信息")
  @ApiImplicitParams({
    @ApiImplicitParam(name = "supplierId", value = "供应商id", required = true, dataType = "String"),
    @ApiImplicitParam(
        name = "supplierUserId",
        value = "用户id",
        required = true,
        dataType = "String"),
    @ApiImplicitParam(name = "filingNo", value = "报备单号", dataType = "String"),
    @ApiImplicitParam(name = "platform", value = "平台", dataType = "String"),
    @ApiImplicitParam(name = "price", value = "报备金额", dataType = "String"),
    @ApiImplicitParam(name = "customer", value = "客户单位", dataType = "String"),
    @ApiImplicitParam(
        name = "fillingState",
        value = "报备单状态(1--报备中,2--已派单,3--已撤回)",
        dataType = "String"),
    @ApiImplicitParam(name = "filingStartTime", value = "报备日期(起始)", dataType = "String"),
    @ApiImplicitParam(name = "filingEndTime", value = "报备日期(截止)", dataType = "String"),
    @ApiImplicitParam(name = "filingArriveStartTime", value = "到期日(起始)", dataType = "String"),
    @ApiImplicitParam(name = "filingArriveEndTime", value = "到期日(截止)", dataType = "String"),
    @ApiImplicitParam(name = "orderNo", value = "客户订单号", dataType = "String"),
    @ApiImplicitParam(name = "filingType", value = "报备类型", dataType = "String"),
    @ApiImplicitParam(name = "dockingSalesName", value = "对接销售名称", dataType = "String"),
    @ApiImplicitParam(name = "schemeId", value = "方案id"),
    @ApiImplicitParam(name = "queryMobile", value = "小程序多条件查询"),
    @ApiImplicitParam(name = "pageNo", value = "当前页", dataType = "String"),
    @ApiImplicitParam(name = "pageSize", value = "每页展示数量", dataType = "String"),
  })
  @RequestMapping(value = "/getFilingPage", method = RequestMethod.GET)
  @ResponseBody
  public ResultBean<PageResult<FilingPageDTO>> getFilingPage(
      String supplierId,
      String supplierUserId,
      String filingNo,
      String platform,
      String price,
      String state,
      String customer,
      String filingStartTime,
      String filingEndTime,
      String filingArriveStartTime,
      String filingArriveEndTime,
      String orderNo,
      String filingType,
      String fillingState,
      String dockingSalesName,
      String schemeId,
      String queryMobile,
      Integer pageNo,
      Integer pageSize) {
    return new ResultBean<>(
        orderFilingService.getFilingPage(
            supplierId,
            supplierUserId,
            filingNo,
            platform,
            price,
            customer,
            state,
            filingStartTime,
            filingEndTime,
            filingArriveStartTime,
            filingArriveEndTime,
            orderNo,
            filingType,
            fillingState,
            dockingSalesName,
            schemeId,
            queryMobile,
            pageNo,
            pageSize));
  }

  @ApiOperation(value = "新建或修改商品报备单")
  @PostMapping(
      value = "saveOrUpdateFiling",
      consumes = {MediaType.APPLICATION_JSON_VALUE})
  @RepeatSubmit
  public ResultBean<Boolean> saveOrUpdateFiling(
      @RequestBody @Validated FilingParamDTO filingParamDTO) {
    if (StrUtil.isBlank(filingParamDTO.getId())) {
      RLock lock = redissonClient.getLock(Constants_LockName.FILING_NO_LOCK);
      try {
        lock.lock();
        orderFilingService.saveOrUpdateFiling(filingParamDTO);
      } finally {
        lock.unlock();
      }
    } else {
      orderFilingService.saveOrUpdateFiling(filingParamDTO);
    }
    return new ResultBean<>(true, "操作成功!");
  }

  @ApiOperation(value = "新建或修改客户订单报备单")
  @PostMapping(
      value = "saveOrUpdateOrderNoFiling",
      consumes = {MediaType.APPLICATION_JSON_VALUE})
  @RepeatSubmit
  public ResultBean<Boolean> saveOrUpdateOrderNoFiling(
      @RequestBody @Validated FilingOrderNoParamDTO param) {
    if (StrUtil.isBlank(param.getId())) {
      RLock lock = redissonClient.getLock(Constants_LockName.FILING_NO_LOCK);
      try {
        lock.lock();
        orderFilingService.saveOrUpdateOrderNoFiling(param);
      } finally {
        lock.unlock();
      }
    } else {
      orderFilingService.saveOrUpdateOrderNoFiling(param);
    }
    return new ResultBean<>(true, "操作成功!");
  }

  @ApiOperation(value = "获取商品报备单详情", notes = "获取商品报备单详情")
  @ApiImplicitParams({
    @ApiImplicitParam(name = "filingId", value = "报备单id", dataType = "String", required = true),
  })
  @RequestMapping(value = "/getFilingDetail", method = RequestMethod.GET)
  @ResponseBody
  public ResultBean<FilingDetailDTO> getFilingDetail(String filingId) {
    return new ResultBean<>(orderFilingService.getFilingDetail(filingId));
  }

  @ApiOperation(value = "获取订单报备单详情", notes = "获取订单报备单详情")
  @ApiImplicitParams({
    @ApiImplicitParam(name = "filingId", value = "报备单id", dataType = "String", required = true),
  })
  @RequestMapping(value = "/getOrderNoFilingDetail", method = RequestMethod.GET)
  @ResponseBody
  public ResultBean<OrderNoFilingDetailDTO> getOrderNoFilingDetail(String filingId) {
    return new ResultBean<>(orderFilingService.getOrderNoFilingDetail(filingId));
  }
}
