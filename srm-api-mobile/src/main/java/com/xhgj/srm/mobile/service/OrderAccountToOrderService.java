package com.xhgj.srm.mobile.service;

import com.xhgj.srm.jpa.entity.OrderAccountToOrder;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-02-23 21:12
 */
public interface OrderAccountToOrderService extends BootBaseService<OrderAccountToOrder, String> {
  /**
   * 通过对账 id 获取订单集合
   *
   * @param orderAccountId 对账 id
   */
  List<String> getOrderIdLIstByOrderAccountId(String orderAccountId);

  /**
   * 通过订单 id 获取对账关联订单信息
   *
   * @param orderId 订单 id 必传
   */
  OrderAccountToOrder getByOrderId(String orderId);

  /**
   * 通过订单 id 获取对账 id
   *
   * @param orderId 订单 id 必传
   */
  String getAccountIdByOrderId(String orderId);
}
