package com.xhgj.srm.mobile.controller;

import com.xhgj.srm.mobile.dto.SupplierUserLoginData;
import com.xhgj.srm.mobile.service.SupplierUserService;
import com.xhiot.boot.mvc.base.ResultBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @ClassName LoginController Create by Liuyq on 2021/6/8 10:28
 */
@RestController
@RequestMapping("/mobileUserLogin")
@Api(tags = {"前台登录接口"})
@Slf4j
public class LoginController {

  @Autowired SupplierUserService supplierUserService;

  @ApiOperation(value = "供应商登录", notes = "供应商登录")
  @ApiImplicitParams({
    @ApiImplicitParam(name = "name", value = "用户名", required = true),
    @ApiImplicitParam(name = "pwd", value = "密码", required = true),
    @ApiImplicitParam(name = "code", value = "验证码")
  })
  @PostMapping(value = "/mobileUserLogin")
  public ResultBean<SupplierUserLoginData> supplierUserLogin(
      HttpServletRequest request,
      HttpServletResponse response,
      String name,
      String pwd,
      String code) {
    return new ResultBean<>(
        supplierUserService.supplierUserLogin(request, response, name, pwd, code));
  }

  @ApiOperation(value = "获取验证码", notes = "获取验证码")
  @GetMapping(value = "/getCodeImg")
  public ResultBean<?> getCodeImg(HttpServletResponse response) {
    return new ResultBean<>(supplierUserService.getCodeImg(response));
  }
}
