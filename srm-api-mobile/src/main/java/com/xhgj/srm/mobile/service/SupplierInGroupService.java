package com.xhgj.srm.mobile.service;

import com.xhgj.srm.jpa.entity.SupplierInGroup;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2022/10/12 9:08
 */
public interface SupplierInGroupService extends BootBaseService<SupplierInGroup, String> {

  /**
   * 通过供应商 id 获得组织下供应商
   *
   * @param supplierId 供应商 id 必传
   */
  List<SupplierInGroup> getBySupplierId(String supplierId);

  /**
   * 通过供应商 id 获得组织下供应商
   *
   * @param supplierId 供应商 id 必传
   */
  Optional<SupplierInGroup> getFirstBySupplierIdAndGroupId(String supplierId, String groupId);
}
