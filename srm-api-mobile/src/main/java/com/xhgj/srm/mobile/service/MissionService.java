package com.xhgj.srm.mobile.service;

import com.xhgj.srm.jpa.entity.Mission;
import com.xhgj.srm.jpa.entity.SupplierUser;

public interface MissionService {

  /**
   * 创建任务中心任务
   *
   * @param supplierUser 用户 必传
   * @param type 任务类型 必传
   * @param source 信息来源 必传
   * @param link excel 链接
   * @param fileName 文件名
   */
  Mission createMission(
      SupplierUser supplierUser, String type, String source, String link, String fileName);
}
