package com.xhgj.srm.mobile.controller;

import com.xhgj.srm.common.dto.OrderPlatformDTO;
import com.xhgj.srm.mobile.service.SupplierService;
import com.xhiot.boot.mvc.base.ResultBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("supplier")
@Api(tags = {"供应商接口"})
@Slf4j
public class SupplierController {

  @Autowired SupplierService supplierService;

  @ApiOperation(value = "根据供应商id获取供应商的可接单平台 - 仅为后台供应商配置生效的平台")
  @GetMapping(value = "getExclusivePlatformListBySupplierId")
  public ResultBean<List<OrderPlatformDTO>> getExclusivePlatformListBySupplierId(
      String supplierId) {
    return new ResultBean<>(supplierService.getPlatformListBySupplierId(supplierId));
  }
}
