spring:
  application:
    name: srm-api-mobile
  profiles:
    active: env-test
    include: datasource,boot
  http:
    encoding:
      charset: utf-8
      enabled: true
      force: true
  servlet:
    multipart:
      max-file-size: 2048MB
      max-request-size: 2058MB
  mail:
    host: smtp.xianhengguoji.com
    username: <EMAIL>
    password: FnGbdbcgwbmJJ5gK
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
          ssl:
            enable: false
    port: 587
  rabbitmq:
    listener:
      simple:
        prefetch: 1
        acknowledge-mode: manual
      direct:
        acknowledge-mode: manual
    connection-timeout: 0s
    publisher-confirms: true
    publisher-returns: true
  cloud:
    nacos:
      config:
        file-extension: "yaml"
        shared-configs:
          - data-id: "srm-shared-configs.yaml"
            refresh: true
xhiot:
  boot:
    security:
      jwt:
        secret: srm-api-mobile
      custom:
        ignore-urls:
          - /test/test
          - /mobileUserLogin/mobileUserLogin
          - /mobileUserLogin/getCodeImg
          - /order/getOrderPage
          - /scheme/getMySchemeList
          - /order/getOrderStateAndCount
          - /input/invoice/relation/page
          - /input/invoice
          - /order/getOrderDetailInvoiceByOrderId
          - /order/getOrderDetail
          - /order/getOrderProductInfo
          - /order/submitOrderAccept
          - /upload/uploadFiles
          - /input/invoice/identify
          - /orderPayment/getOrderPaymentPage
          - /order/getAllowPaymentOrderPage
          - /input/invoice/verification
          - /orderPayment/submitPaymentOrder
          - /input/invoice/data-count
          - /input/invoice/order/page
          - /input/invoice/supplierOrder/page
          - /input/invoice/relation/details
          - /input/invoice/relation/supplier/invoice/details
          - /order/delivery/getProductInfoPage
          - /supplierOrder/getProductInfoPage
          - /filing/getFilingPage
          - /filing/saveOrUpdateFiling
          - /filing/saveOrUpdateOrderNoFiling
          - /filing/getFilingDetail
          - /filing/getOrderNoFilingDetail
          - /product/getAllProvinceCity
          - /oa-user/info
          - /product/getNormalProductDetail
    app-name: ${spring.application.name}
    sys:
      error:
        ding:
          config:
            access-token: "3c0c25fd94f37f3c7b19ab884d1a39148b4d5c5ed6dfe2a54e57b27baf2e651a"
            at-mobiles-or-user-ids: ["13197800218"]
    ding:
      client-id: "ding65euvxming81gc3c"
      client-secret: "3UDwpVNWYLYYqLjoeGL_g7DK-2W-MjQ_8DDEtTbHrbwjq0gMzEe_9m0lzn5vgGjw"
server:
  port: 9000
  servlet:
    context-path: /${spring.application.name}
  tomcat:
    uri-encoding: utf-8
logging:
  level:
    root: info
