package com.xhgj.srm.dto.order;

import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.jpa.entity.OrderPaymentCollection;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Optional;

@Data
public class OrderPaymentCollectionVO {
  private String id;

  private String erpPaymentNo;

  private Long createTime;

  private String returnPrice;

  private Long paymentTime;
  @ApiModelProperty("付款链接")
  private String url;

  @ApiModelProperty("凭证生成方式：（1 系统发起，2 填写单号）")
  private String createVoucherType ;

  /**
   * 支付状态
   * {@link com.xhgj.srm.common.enums.VoucherPaymentStateEnum}
   */
  @ApiModelProperty("支付状态")
  private String paymentState ;

  @ApiModelProperty("退款状态：1 审核中 2 通过 3 驳回")
  private String refundState;

  public static OrderPaymentCollectionVO of(OrderPaymentCollection orderPaymentCollection) {
    OrderPaymentCollectionVO vo = new OrderPaymentCollectionVO();
    if (orderPaymentCollection == null)
      return vo;
    vo.id = orderPaymentCollection.getId();
    vo.erpPaymentNo = orderPaymentCollection.getPaymentNo();
    vo.createTime = orderPaymentCollection.getBusinessCreateTime();
    vo.returnPrice =
        Optional.ofNullable(orderPaymentCollection.getAmount()).map(BigDecimal::stripTrailingZeros)
            .map(BigDecimal::toPlainString).orElse(StrUtil.EMPTY);
    vo.paymentTime = orderPaymentCollection.getPaymentTime();
    vo.url = StrUtil.emptyIfNull(orderPaymentCollection.getUrl());
    vo.createVoucherType = StrUtil.emptyIfNull(orderPaymentCollection.getCreateVoucherType());
    vo.paymentState = StrUtil.emptyIfNull(orderPaymentCollection.getPaymentState());
    return vo;
  }




}
