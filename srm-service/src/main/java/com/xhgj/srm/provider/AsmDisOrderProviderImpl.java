package com.xhgj.srm.provider;/**
 * @since 2025/4/22 16:17
 */

import com.xhgj.srm.dto.asmDisOrder.AsmDisOrderApplyLinkDTO;
import com.xhgj.srm.factory.MapStructFactory;
import com.xhgj.srm.service.AsmDisOrderService;
import com.xhgj.srm.v2.dto.AsmDisOrderApplyLinkV2DTO;
import com.xhgj.srm.v2.provider.AsmDisOrderProvider;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class AsmDisOrderProviderImpl implements AsmDisOrderProvider {

  @Resource
  AsmDisOrderService asmDisOrderService;

  @Override
  public List<AsmDisOrderApplyLinkV2DTO> getPurchaseApplyLink(List<String> purchaseApplyForOrderIds) {
    List<AsmDisOrderApplyLinkDTO> purchaseApplyLink =
        asmDisOrderService.getPurchaseApplyLink(purchaseApplyForOrderIds);
    return purchaseApplyLink.stream().map(MapStructFactory.INSTANCE::toAsmDisOrderApplyLinkV2DTO).collect(Collectors.toList());
  }

}

