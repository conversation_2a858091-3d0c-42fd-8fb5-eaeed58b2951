package com.xhgj.srm.provider;/**
 * @since 2025/5/7 9:57
 */

import com.xhgj.srm.service.TemplateFieldConfigService;
import com.xhgj.srm.v2.form.PurchaseOrderAddV2Form;
import com.xhgj.srm.v2.provider.TemplateFieldConfigProvider;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
public class TemplateFieldConfigProviderImpl implements TemplateFieldConfigProvider {

  @Resource
  private TemplateFieldConfigService templateFieldConfigService;

  @Override
  public boolean isFieldShow(String userGroup, String orderType, String entityFieldName) {
    return templateFieldConfigService.isFieldShow(userGroup, orderType, entityFieldName);
  }

  @Override
  public void checkFieldRequired(String userGroup, String orderType, PurchaseOrderAddV2Form form) {
    templateFieldConfigService.checkFieldRequired(userGroup, orderType, form);
  }
}
