package com.xhgj.srm.provider;/**
 * @since 2025/4/18 10:27
 */

import com.xhgj.srm.jpa.dto.permission.MergeUserPermission;
import com.xhgj.srm.jpa.dto.permission.OperatorPermission;
import com.xhgj.srm.jpa.dto.permission.SearchPermission;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.service.SharePermissionTypeService;
import com.xhgj.srm.v2.provider.PermissionTypeProvider;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class PermissionTypeProviderImpl implements PermissionTypeProvider {

  @Resource
  private SharePermissionTypeService sharePermissionTypeService;

  @Override
  public String getUserPermissionCodeByUserIdAndType(String userId, String type) {
    return sharePermissionTypeService.getUserPermissionCodeByUserIdAndType(userId, type);
  }

  @Override
  public SearchPermission getSearchPermission(User user, String currentGroupCode,
      String permissionType, Boolean excludeAdmin, Boolean excludeSuperAdmin,
      Boolean includeSupplierUser, Boolean includeAllUserGroups) {
    return sharePermissionTypeService.getSearchPermission(user, currentGroupCode, permissionType,
        excludeAdmin, excludeSuperAdmin, includeSupplierUser, includeAllUserGroups);
  }

  @Override
  public SearchPermission getSearchPermission(User user, String currentGroupCode,
      String permissionType, Boolean excludeAdmin, Boolean excludeSuperAdmin,
      Boolean includeSupplierUser) {
    return sharePermissionTypeService.getSearchPermission(user, currentGroupCode, permissionType,
        excludeAdmin, excludeSuperAdmin, includeSupplierUser);
  }

  @Override
  public SearchPermission getSearchPermission(User user, String currentGroupCode,
      String permissionType) {
    return sharePermissionTypeService.getSearchPermission(user, currentGroupCode, permissionType);
  }

  @Override
  public OperatorPermission getOperatorPermission(User user, String currentGroupCode,
      String permissionType, Boolean excludeAdmin, Boolean excludeSuperAdmin) {
    return sharePermissionTypeService.getOperatorPermission(user, currentGroupCode, permissionType,
        excludeAdmin, excludeSuperAdmin);
  }

  @Override
  public OperatorPermission getOperatorPermission(User user, String currentGroupCode,
      String permissionType) {
    return sharePermissionTypeService.getOperatorPermission(user, currentGroupCode, permissionType);
  }

  @Override
  public MergeUserPermission mergePermission(SearchPermission searchPermission,
      OperatorPermission operatorPermission) {
    return sharePermissionTypeService.mergePermission(searchPermission, operatorPermission);
  }

  @Override
  public List<String> getConcatNumUserNameList(String userId, String type, List<String> role) {
    return sharePermissionTypeService.getConcatNumUserNameList(userId, type, role);
  }
}
