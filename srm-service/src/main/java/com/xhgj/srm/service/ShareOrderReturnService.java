package com.xhgj.srm.service;

import com.xhgj.srm.dto.order.OrderReturnOMSDTO;
import com.xhgj.srm.dto.returned.OrderReturnAddParam;
import com.xhgj.srm.jpa.entity.Order;
import com.xhgj.srm.jpa.entity.OrderReturn;
import com.xhgj.srm.request.dto.OrderReturnDetailInfo;
import com.xhgj.srm.request.dto.OrderReturnInfo;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_075Param;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_075Result;
import com.xhgj.srm.request.vo.sap.SyncOrderReturnRes;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface ShareOrderReturnService {

  /**
   * 新建取消单、退货单
   * @param orderReturnAddParams
   */
  List<OrderReturnOMSDTO> addCancelAndReturnOrder(List<OrderReturnAddParam> orderReturnAddParams);

  /**
   * 新建退货单
   * @param orderReturnAddParams
   */
  OrderReturnInfo addOrderRefund(List<OrderReturnAddParam> orderReturnAddParams, boolean addOrderReturn, boolean sync2Sap);

  /**
   * 同步取消单至SAP
   * @param orderCancelId
   */
  void syncOrderCancel(String orderCancelId);

    /**
   * sap同步退货单，且钉钉报异常
   */
  MM_075Result syncSapOrderReturn(MM_075Param param, String orderNo);

  /**
   * 订单履约 退货单同步 + 钉钉报异常
   * @param order
   * @param orderReturn
   * @param returnList
   * @param async 异步
   * @return
   */
  SyncOrderReturnRes syncOrderReturn(Order order, OrderReturn orderReturn, List<OrderReturnDetailInfo> returnList, boolean async);
}
