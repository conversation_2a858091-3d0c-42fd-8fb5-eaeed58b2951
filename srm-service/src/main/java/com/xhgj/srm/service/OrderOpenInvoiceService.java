package com.xhgj.srm.service;

import com.xhgj.srm.jpa.entity.OrderOpenInvoice;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-02-03 10:51
 */
public interface OrderOpenInvoiceService extends BootBaseService<OrderOpenInvoice,String> {

  /**
   * 通过开票申请信息获得开票信息
   * @param orderInvoiceId 发票申请信息 id 必传
   * @return
   */
  List<OrderOpenInvoice> getByInvoiceId(String orderInvoiceId);
}
