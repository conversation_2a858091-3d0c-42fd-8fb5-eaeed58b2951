package com.xhgj.srm.service;/**
 * @since 2025/3/13 11:12
 */

import org.springframework.web.multipart.MultipartFile;

/**
 *<AUTHOR>
 *@date 2025/3/13 11:12:06
 *@description 期初采购订单
 */
public interface InitialPurchaseOrderService {

  /**
   * 期初采购订单导入
   */
  void importOrder(MultipartFile file, String userId);

  /**
   * 期初采购订单入库单导入
   * @param file
   * @param id
   */
  void inboundDelivery(MultipartFile file, String id);
}
