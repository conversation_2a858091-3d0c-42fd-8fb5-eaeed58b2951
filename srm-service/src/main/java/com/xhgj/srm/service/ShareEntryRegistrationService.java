package com.xhgj.srm.service;
import com.xhgj.srm.jpa.entity.EntryRegistrationOrder;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.registration.dto.entryregistration.EntryRegistrationDiscountDTO;
import com.xhgj.srm.registration.entity.EntryRegistrationEntity;
import java.util.List;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * <AUTHOR>
 */
public interface ShareEntryRegistrationService {

  /**
   * 同步履约信息至OMS
   */
  void batchUpdateDiscountToOms(EntryRegistrationEntity order);

  /**
   * 同步履约信息至OMS
   * @param supplierName
   * @param supplierId
   * @param platformCodeList
   * @param discounts
   */
  void batchUpdateDiscountToOms(String supplierName, String supplierId, List<String> platformCodeList, List<EntryRegistrationDiscountDTO> discounts);

  /**
   * 同步履约信息至OMS(清空该入驻报备相关的履约平台信息)
   */
  void batchUpdateDiscountToOmsClear(EntryRegistrationEntity order);

  /**
   * 同步履约信息至OMS(清空原有供应商履约信息)
   */
  void batchUpdateDiscountToOmsClear(Supplier supplier, List<String> platformCodes);

  /**
   * 批量获取供应商信息
   */
  void batchGetEntryRegistrationInfo(List<EntryRegistrationOrder> list, EntryRegistrationBatchGetHandler handler);
}
