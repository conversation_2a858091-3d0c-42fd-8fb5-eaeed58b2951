package com.xhgj.srm.service;

import com.xhgj.srm.jpa.dto.permission.MergeUserPermission;
import com.xhgj.srm.jpa.dto.permission.OperatorPermission;
import com.xhgj.srm.jpa.dto.permission.SearchPermission;
import com.xhgj.srm.jpa.entity.PermissionType;
import com.xhgj.srm.jpa.entity.User;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import java.util.List;

public interface SharePermissionTypeService extends BootBaseService<PermissionType, String> {
  /**
   * @Auhor: liuyq @Date: 2022/8/17 17:32 根据用户和数据范围/操作范围类型获取权限编码
   *
   * @param userId 用户id，必传
   * @param type 数据范围/操作范围类型，必传
   * @return java.lang.String
   */
  String getUserPermissionCodeByUserIdAndType(String userId, String type);
  /**
   * Author: liuyq @Date: 2022/8/17 17:58 根据用户id 和权限编码获取权限内 用户id集合
   *
   * @param userId 用户id，必传
   * @param code 权限编码，必传
   * @return java.util.List<java.lang.String>
   */
  List<String> getUserIdList(String code, String userId, List<String> role);

  List<String> getConcatNumUserNameList(String userId, String type, List<String> role);

  /**
   * 设置用户 查询数据范围 -- 特殊处理excludeAdmin, excludeSuperAdmin, includeSupplierUser
   * @param user 用户
   * @param currentGroupCode 组织code
   * @param permissionType 权限类型
   * @param excludeAdmin 是否排除管理员
   * @param excludeSuperAdmin 是否排除超级管理员
   * @param includeSupplierUser 是否包含供应商用户
   * @param includeAllUserGroups 是否包含所有用户的组织
   * @return
   */
  SearchPermission getSearchPermission(User user, String currentGroupCode, String permissionType,
      Boolean excludeAdmin, Boolean excludeSuperAdmin, Boolean includeSupplierUser,
      Boolean includeAllUserGroups);

  /**
   * 设置用户 查询数据范围 -- 特殊处理excludeAdmin, excludeSuperAdmin, includeSupplierUser
   * @param user
   * @param currentGroupCode 组织code
   * @param permissionType 权限类型
   * @param excludeAdmin 是否排除管理员
   * @param excludeSuperAdmin 是否排除超级管理员
   * @param includeSupplierUser 是否包含供应商用户
   * @return
   */
  SearchPermission getSearchPermission(User user, String currentGroupCode, String permissionType,
      Boolean excludeAdmin, Boolean excludeSuperAdmin, Boolean includeSupplierUser);

  /**
   * 设置用户 查询数据范围
   * @param user
   * @param currentGroupCode 组织code
   * @param permissionType 权限类型
   * @return
   */
  SearchPermission getSearchPermission(User user, String currentGroupCode, String permissionType);

  /**
   * 设置用户 操作权限范围 -- 特殊处理excludeAdmin, excludeSuperAdmin
   * @param user
   * @param currentGroupCode 组织code
   * @param permissionType 权限类型
   * @param excludeAdmin 是否排除管理员
   * @param excludeSuperAdmin 是否排除超级管理员
   * @return
   */
  OperatorPermission getOperatorPermission(User user, String currentGroupCode, String permissionType,
      Boolean excludeAdmin, Boolean excludeSuperAdmin);

  /**
   * 设置用户 操作权限范围
   * @param user
   * @param currentGroupCode 组织code
   * @param permissionType 权限类型
   * @return
   */
  OperatorPermission getOperatorPermission(User user, String currentGroupCode, String permissionType);

  /**
   * 合并权限
   * @param searchPermission
   * @param operatorPermission
   * @return
   */
  MergeUserPermission mergePermission(SearchPermission searchPermission, OperatorPermission operatorPermission);
}
