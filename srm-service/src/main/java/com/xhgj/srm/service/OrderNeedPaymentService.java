package com.xhgj.srm.service;/**
 * @since 2025/3/17 18:39
 */

import com.xhgj.srm.common.vo.order.OrderNeedPaymentListVO;
import com.xhgj.srm.common.vo.order.OrderNeedPaymentStatistics;
import com.xhgj.srm.dto.order.OrderNeedPaymentListQuery;
import com.xhgj.srm.dto.order.needPayment.NeedPaymentPreCheckForm;
import com.xhgj.srm.jpa.entity.Order;
import com.xhgj.srm.jpa.entity.OrderNeedPayment;
import com.xhiot.boot.mvc.base.PageResult;
import java.util.List;

/**
 *<AUTHOR>
 *@date 2025/3/17 18:39:15
 *@description 可付款履约单服务
 */
public interface OrderNeedPaymentService {

  /**
   * 分页查询可付款单列表
   */
  PageResult<OrderNeedPaymentListVO> getPage(OrderNeedPaymentListQuery query);

  /**
   * 可付款单统计
   */
  OrderNeedPaymentStatistics getCount(OrderNeedPaymentListQuery param);

  /**
   * 可付款列表导出
   */
  void export(Object form);

  /**
   * 生成付款单预校验
   * @param form
   */
  void preCheck(NeedPaymentPreCheckForm form);

  /**
   * 生成付款单预校验
   * @param orderIds 订单ids
   */
  void preCheck(List<String> orderIds);


  /**
   *  保存、更新可付款单
   *  todo 可能需要处理前台特殊的情况
   *  为背靠背，除了B记录其他需要生成 -- 有回款记录在调用此接口
   *  非背靠背，每次刷新关联id和相关冗余字段 -- 首次发货后就调用此接口
   * @param orderReceiptId 非背靠背首次发货此id可能为空
   * @param order notNull
   */
  void save(String orderReceiptId, Order order);

  /**
   * 保存
   * @param order
   */
  OrderNeedPayment saveOld(Order order);

  /**
   * 根据ids查询可付款单列表
   * todo 付款单详情页/列表页 需要查询关联
   */
  List<OrderNeedPaymentListVO> getListByIds(List<String> ids);

  /**
   * 根据订单id对于需付款单进行锁定操作
   * todo 部分付款需要调用清除
   */
  void lockPartPayByOrderId(String orderId);
}
