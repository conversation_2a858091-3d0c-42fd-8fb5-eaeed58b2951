package com.xhgj.srm.service;

import com.xhgj.srm.jpa.entity.OrderReturnDetail;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-06-04 18:23
 */
public interface OrderReturnDetailTempService extends BootBaseService<OrderReturnDetail,String> {

  /**
   * 根据退货 id 获取退货单详情
   * @param returnId 退货单 id 必传
   * @return
   */
  List<OrderReturnDetail> getReturnDetailByReturnId(String returnId);
}
