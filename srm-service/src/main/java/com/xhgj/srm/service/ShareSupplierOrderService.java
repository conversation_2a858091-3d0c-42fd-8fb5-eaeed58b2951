package com.xhgj.srm.service;

import com.xhgj.srm.OutBoundDeliveryPrams;
import com.xhgj.srm.dto.PurchaseOrderReturnDTO;
import com.xhgj.srm.dto.WarehouseWarrantDTO;
import com.xhgj.srm.dto.WarehouseWarrantParams;
import com.xhgj.srm.jpa.entity.SupplierOrder;
import com.xhgj.srm.jpa.entity.User;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import java.util.List;

public interface ShareSupplierOrderService extends BootBaseService<SupplierOrder, String> {

  /**
   * 获取所有的订单 id
   *
   * @param purchaseId 采购 id
   * @param supplierId 供应商 id
   * @return
   */
  List<String> getAllIds(String purchaseId, String supplierId);

  /**
   * 根据编码获取采购订单
   *
   * @param code 采购编号，必传
   */
  SupplierOrder getByCode(String code);
}
