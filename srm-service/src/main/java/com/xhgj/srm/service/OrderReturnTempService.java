package com.xhgj.srm.service;

import com.xhgj.srm.common.vo.order.OrderCancelVO;
import com.xhgj.srm.dto.order.ReturnInfoDTO;
import com.xhgj.srm.jpa.entity.OrderReturn;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-06-04 17:46
 */
public interface OrderReturnTempService extends BootBaseService<OrderReturn,String> {

  /**
   * 根据订单 id 获取退货单
   * @param orderId 订单 id 必传
   * @return
   */
  List<OrderReturn> getOrderReturnByOrderId(String orderId);

  /**
   * 根据订单 id 获取退货单VO对象
   * @param orderId 订单 id 必传
   * @return
   */
  List<ReturnInfoDTO> getOrderReturnVOByOrderId(String orderId);

  /**
   * 根据订单 id 获取退货单明细
   * @param orderId
   * @return
   */
    List<OrderCancelVO> getOrderCancelDetailByOrderId(String orderId);
}
