package com.xhgj.srm.service;

import com.xhgj.srm.jpa.entity.OrderAccount;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import java.math.BigDecimal;
import java.util.List;


/**
 * <AUTHOR>
 */
public interface TempOrderAccountService extends BootBaseService<OrderAccount,String> {

   /***
    *  获取最终结算金额
    * @param orderIdList
    * <AUTHOR>
    * @date 2023/5/10 15:39
    */
   BigDecimal getFinalAccountPrice(List<String> orderIdList);
}
