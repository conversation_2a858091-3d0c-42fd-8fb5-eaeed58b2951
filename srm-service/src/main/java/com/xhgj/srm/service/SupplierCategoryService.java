package com.xhgj.srm.service;/**
 * @since 2024/11/28 11:35
 */

import com.xhgj.srm.jpa.dto.supplierCategory.SupplierCategorySaveForm;
import com.xhgj.srm.jpa.entity.SupplierCategory;
import java.util.List;

/**
 *<AUTHOR>
 *@date 2024/11/28 11:35:27
 *@description 供应商经营类目
 */
public interface SupplierCategoryService {

  /**
   * 新增 保存供应商经营类目
   */
  void patchUpdate(List<SupplierCategorySaveForm> saveForm, String supplierId, String supplierInGroupId, String userId);

  /**
   * 新增 保存供应商经营类目
   */
  void patchUpdate(List<SupplierCategorySaveForm> saveForm, String supplierId,
      String supplierInGroupId, String userId, String originSupplierInGroupId);

  /**
   * 批量获取组织内供应商经营类目
   */
  List<SupplierCategory> getSupplierCategory(List<String> supplierInGroupIds);

  /**
   * 批量获取组织内供应商经营类目,实时刷新名称
   */
  List<SupplierCategory> getSupplierCategory(List<String> supplierInGroupIds, boolean refresh);

  /**
   * 删除供应商经营类目
   */
  void deleteBySupplierInGroupId(String supplierInGroupId, String userId);

  /**
   * 删除供应商经营类目
   */
  void deleteBySupplierId(String supplierId, String userId);
}
