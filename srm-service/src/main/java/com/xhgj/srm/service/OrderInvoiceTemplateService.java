package com.xhgj.srm.service;

import com.xhgj.srm.dto.order.OrderDetailInvoiceDTO;
import com.xhgj.srm.jpa.entity.OrderInvoiceTemplate;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2023-06-04 16:03
 */
public interface OrderInvoiceTemplateService extends BootBaseService<OrderInvoiceTemplate,String> {

  Optional<OrderInvoiceTemplate> getByOrderId(String orderId);

  /**
   * 保存订单发票信息模板
   * @param orderId 订单 id 必传
   * @param orderDetailInvoiceDTO 保存信息
   */
  void saveOrderInvoiceTemplate(String orderId, OrderDetailInvoiceDTO orderDetailInvoiceDTO);
}
