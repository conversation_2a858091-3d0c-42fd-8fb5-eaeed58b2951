package com.xhgj.srm.service.impl;/**
 * @since 2025/2/25 11:39
 */

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.Constants_Excel;
import com.xhgj.srm.common.constants.Constants_Sap;
import com.xhgj.srm.common.dto.ApprovalResult;
import com.xhgj.srm.common.enums.transferOrder.TransferOrderStatus;
import com.xhgj.srm.common.utils.ExportUtil;
import com.xhgj.srm.common.utils.transferOrder.TransferOrderCleanerAndGenerator;
import com.xhgj.srm.common.vo.transferOrder.TransferOrderListVO;
import com.xhgj.srm.dto.transferOrder.TransferOrderFillDTO;
import com.xhgj.srm.dto.transferOrder.TransferOrderSearchForm;
import com.xhgj.srm.factory.TransferOrderFactory;
import com.xhgj.srm.factory.WmsFactory;
import com.xhgj.srm.jpa.annotations.DefaultSearchScheme;
import com.xhgj.srm.jpa.dao.TransferOrderDao;
import com.xhgj.srm.jpa.dto.permission.MergeUserPermission;
import com.xhgj.srm.jpa.dto.permission.OperatorPermission;
import com.xhgj.srm.jpa.dto.permission.SearchPermission;
import com.xhgj.srm.jpa.entity.InventoryLocation;
import com.xhgj.srm.jpa.entity.TransferOrder;
import com.xhgj.srm.jpa.entity.TransferOrderItem;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.jpa.repository.InventoryLocationRepository;
import com.xhgj.srm.jpa.repository.TransferOrderItemRepository;
import com.xhgj.srm.jpa.repository.TransferOrderRepository;
import com.xhgj.srm.jpa.repository.UserRepository;
import com.xhgj.srm.request.ConstantHZero;
import com.xhgj.srm.request.config.HZeroProcessConfig;
import com.xhgj.srm.request.dto.hZero.process.StartProcessParam;
import com.xhgj.srm.request.dto.hZero.process.TransferProcessParam;
import com.xhgj.srm.request.dto.hZero.process.TransferProcessParam.Detail;
import com.xhgj.srm.factory.SapFactory;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_076Param;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_076Result;
import com.xhgj.srm.request.service.third.erp.sap.dto.WMSTransferOrderParam;
import com.xhgj.srm.request.service.third.erp.sap.dto.WMSTransferOrderParam.DATA;
import com.xhgj.srm.request.service.third.erp.sap.dto.WMSTransferOrderParam.DATA.Item;
import com.xhgj.srm.request.service.third.erp.sap.dto.WMSTransferOrderResult;
import com.xhgj.srm.request.service.third.erp.sap.dto.WMSTransferOrderResult.DATADTO.ITEM;
import com.xhgj.srm.request.service.third.erp.sap.dto.WMSTransferOrderReturnResult;
import com.xhgj.srm.request.service.third.erp.sap.dto.WMSTransferOrderReturnResult.RETURNDTO;
import com.xhgj.srm.request.service.third.hZero.HZeroService;
import com.xhgj.srm.request.service.third.sap.SAPService;
import com.xhgj.srm.request.service.third.wms.WMSService;
import com.xhgj.srm.service.SharePermissionTypeService;
import com.xhgj.srm.service.TransferOrderService;
import com.xhgj.srm.dto.transferOrder.TransferOrderSaveForm;
import com.xhgj.srm.vo.transferOrder.TransferOrderItemVO;
import com.xhgj.srm.vo.transferOrder.TransferOrderVO;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.mvc.base.PageResult;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import java.util.Comparator;

/**
 * <AUTHOR>
 * 调拨单业务方法
 */
@Service
@Slf4j
public class TransferOrderServiceImpl implements TransferOrderService {

  @Resource
  private TransferOrderRepository transferOrderRepository;
  @Resource
  private TransferOrderItemRepository transferOrderItemRepository;
  @Resource
  private TransferOrderDao transferOrderDao;
  @Resource
  private SharePermissionTypeService sharePermissionTypeService;
  @Resource
  private TransferOrderFactory transferOrderFactory;
  @Resource
  private ExportUtil exportUtil;
  @Resource
  private HZeroService hZeroService;
  @Resource
  private HZeroProcessConfig hZeroProcessConfig;
  @Resource
  private InventoryLocationRepository inventoryLocationRepository;
  @Resource
  private SAPService sapService;
  @Resource
  private SapFactory sapFactory;
  @Resource
  private WMSService wmsService;
  @Resource
  private UserRepository userRepository;
  @Resource
  private WmsFactory wmsFactory;

  @Override
  @Transactional(rollbackFor = Exception.class)
  public String save(TransferOrderSaveForm saveForm) {
    TransferOrder transferOrder;
    try {
      // ------------------- 校验 -------------------
      // #check 寄售调拨单时，供应商id是否填写
      transferOrderFactory.checkSupplier(saveForm);
      // #check 校验调拨单物料中库位id每行是否一致
      transferOrderFactory.checkItemsWarehouse(saveForm);
      // ------------------- 保存 -------------------
      transferOrder = transferOrderFactory.createTransferOrder(saveForm);
      transferOrderRepository.saveAndFlush(transferOrder);
      List<TransferOrderItem> transferOrderItems =
          transferOrderFactory.createTransferOrderItems(saveForm, transferOrder);
      transferOrderItemRepository.saveAll(transferOrderItems);
      transferOrderItemRepository.flush();
      // transferOrderItems 过滤删除的
      transferOrderItems =
          transferOrderItems.stream().filter(item -> !Constants.STATE_DELETE.equals(item.getState()))
              .collect(Collectors.toList());
      // ------------------- 调用飞搭 -------------------
      if (TransferOrderStatus.AUDITING.getCode().equals(transferOrder.getStatus())) {
        StartProcessParam processParam = buildProcessParam(transferOrder,transferOrderItems);
        transferOrder.setReviewId(hZeroService.startProcessWithoutFile(processParam).getInstanceId());
        transferOrderRepository.saveAndFlush(transferOrder);
      }
    } catch (Exception e) {
      log.error("调拨单保存失败", e);
      TransferOrderCleanerAndGenerator.INSTANCE.rollbackOrderNumber();
      throw e;
    }finally {
      TransferOrderCleanerAndGenerator.clear();
    }
    return transferOrder.getId();
  }

  /**
   * 构建流程参数
   */
  private StartProcessParam buildProcessParam(TransferOrder transferOrder,
      List<TransferOrderItem> transferOrderItems) {
    TransferProcessParam docJson = new TransferProcessParam(transferOrder);
    AtomicReference<String> export = new AtomicReference<>();
    AtomicReference<String> fold = new AtomicReference<>();
    transferOrderItems.stream().findFirst().ifPresent(item->{
      fold.set(inventoryLocationRepository.findById(item.getWarehouseIn())
          .map(InventoryLocation::getWarehouseName).orElse(null));
      export.set(inventoryLocationRepository.findById(item.getWarehouseOut())
          .map(InventoryLocation::getWarehouseName).orElse(null));
    });
    docJson.setDetailList(
        transferOrderItems.stream().map(item -> new Detail(item, export.get(), fold.get()))
            .collect(Collectors.toList()));
    String desc = StrUtil.format("{}提交的调拨单{}", transferOrder.getCreateManName(),
        transferOrder.getCode());
    return StartProcessParam.builder().flowKey(hZeroProcessConfig.getTransferOrderFlowKey())
            .businessKey(desc)
            .dimension(ConstantHZero.DIMENSION_ORG)
            .starter(transferOrder.getCreateManCode().toLowerCase())
            .description(desc)
            .variableMap(null)
            .docJsonMap(
                JSON.parseObject(JSON.toJSONString(docJson)))
            .build();
  }

  @Override
  @DefaultSearchScheme(searchType = Constants.SEARCH_TYPE_TRANSFER_ORDER)
  public PageResult<TransferOrderListVO> getPage(TransferOrderSearchForm form) {
    MergeUserPermission mergeUserPermission =
        sharePermissionTypeService.mergePermission(new SearchPermission(),
            new OperatorPermission());
    Map<String, Object> queryMap = form.toQueryMap(mergeUserPermission);
    return transferOrderDao.getPage(queryMap);
  }

  @Override
  public TransferOrderVO getDetail(String id) {
    TransferOrder transferOrder =
        transferOrderRepository.findById(id).orElseThrow(() -> new CheckException("调拨单不存在"));
    if (Constants.STATE_DELETE.equals(transferOrder.getState())) {
      throw new CheckException("调拨单已删除");
    }
    List<TransferOrderItem> transferOrderItems = transferOrderItemRepository.findAllByTransferOrderIdAndState(id, Constants.STATE_OK);
    // transferOrderItems 根据创建时间排序
    transferOrderItems.sort(Comparator.comparing(TransferOrderItem::getCreateTime));
    return transferOrderFactory.buildVO(transferOrder, transferOrderItems);
  }

  @Override
  public void delete(String id) {
    if (StrUtil.isBlank(id)) {
      return;
    }
    TransferOrder transferOrder =
        transferOrderRepository.findById(id).orElseThrow(() -> new CheckException("调拨单不存在"));
    // 判断只有暂存和驳回状态的才能删除
    if (!TransferOrderStatus.TEMPORARY.getCode().equals(transferOrder.getStatus())
        && !TransferOrderStatus.REJECT.getCode().equals(transferOrder.getStatus())) {
      throw new CheckException("只有暂存和驳回状态的才能删除");
    }
    transferOrder.setState(Constants.STATE_DELETE);
    transferOrder.setUpdateTime(System.currentTimeMillis());
    transferOrderRepository.saveAndFlush(transferOrder);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void auditCallBack(ApprovalResult approvalResult, byte status) {
    String instanceId = approvalResult.getProcessInstanceId();
    String jobNumber = approvalResult.getStaffId();
    String remark = approvalResult.getRemark();
    long finishTime = approvalResult.getFinishTime();
    String reviewerName = userRepository.findFirstByCodeAndState(jobNumber, Constants.STATE_OK)
        .map(User::getRealName)
        .orElse(StrUtil.EMPTY);

    TransferOrder transferOrder = transferOrderRepository.findByReviewId(instanceId)
        .orElseThrow(() -> CheckException.noFindException(TransferOrder.class, instanceId));
    if (Boolean.TRUE.equals(transferOrder.hasMovement()) && TransferOrderStatus.FINISHED.getCode() == status) {
      // 如果设计货物移动且审核通过，则状态修改为待仓库执行
      status = TransferOrderStatus.WAITING_WAREHOUSE.getCode();
    }
    transferOrder.setReviewer(reviewerName);
    transferOrder.setReviewerCode(jobNumber);
    transferOrder.setReviewTime(finishTime);
    transferOrder.setReviewReason(remark);
    transferOrder.setStatus(status);
    transferOrder.setUpdateTime(System.currentTimeMillis());
    transferOrderRepository.saveAndFlush(transferOrder);
    if (TransferOrderStatus.WAITING_WAREHOUSE.getCode() == status) {
      status = TransferOrderStatus.WAITING_WAREHOUSE.getCode();
      TransferOrderVO transferOrderVO = this.getDetail(transferOrder.getId());
      wmsService.wmsTransferOrderParam(wmsFactory.buildWMSTransferOrderParam(transferOrderVO));
    }
    if (TransferOrderStatus.FINISHED.getCode() == status) {
      // SAP处理结果校验
      MM_076Result mm076Result = sapProcess(transferOrder.getId());
      if (StrUtil.equals(mm076Result.getType(), Constants_Sap.SUCCESS_TYPE)) {
        transferOrder.setProductVoucher(mm076Result.getBelnr());
        transferOrder.setProductVoucherYear(mm076Result.getGjahr());
      } else {
        throw new CheckException("调用SAP调拨凭证过账接口失败:" + mm076Result.getMessage());
      }
    }
  }

  @Override
  public WMSTransferOrderReturnResult wmsCallback(WMSTransferOrderResult form) {
    WMSTransferOrderReturnResult wmsReturn = new WMSTransferOrderReturnResult();
    try {
      if (form.getData() == null || CollUtil.isEmpty(form.getData().getItem())) {
        return buildWMSResponse(wmsReturn, Constants_Sap.ERROR_TYPE,"请求数据或Item为空");
      }
      ITEM item = form.getData().getItem().get(0);
      String code = item.getZdbdh();
      TransferOrder transferOrder =
          transferOrderRepository.findFirstByCodeAndState(code, Constants.STATE_OK)
              .orElseThrow(() -> new CheckException("调拨单不存在"));
      // SAP处理结果校验
      MM_076Result mm076Result = sapProcess(transferOrder.getId());
      if (mm076Result == null) {
        return buildWMSResponse(wmsReturn,Constants_Sap.ERROR_TYPE, "调用SAP调拨凭证过账处理结果为空");
      }
      if (!StrUtil.equals(mm076Result.getType(), Constants_Sap.SUCCESS_TYPE)) {
        return buildWMSResponse(wmsReturn, Constants_Sap.ERROR_TYPE, mm076Result.getMessage());
      }
      updateTransferOrderForBack(transferOrder, item, mm076Result);
      //成功响应构建
      return buildWMSResponse(wmsReturn,Constants_Sap.SUCCESS_TYPE, mm076Result.getMessage());

    } catch (Exception e) {
      log.error("处理WMS回调异常:", e);
      return buildWMSResponse(wmsReturn,Constants_Sap.ERROR_TYPE, StrUtil.maxLength(ExceptionUtil.getSimpleMessage(e),500));
    }

  }

  private void updateTransferOrderForBack(TransferOrder transferOrder, ITEM item,
      MM_076Result result) {
    long currentTime = System.currentTimeMillis();
    transferOrder.setUpdateTime(currentTime);
    transferOrder.setWarehouseTime(currentTime);
    transferOrder.setWarehouseOperator(item.getUsnam());
    transferOrder.setTrackNum(item.getZwldh1());
    transferOrder.setLogisticsCompany(item.getZwlbm1());
    transferOrder.setStatus(TransferOrderStatus.FINISHED.getCode());
    transferOrder.setProductVoucher(result.getBelnr());
    transferOrder.setProductVoucherYear(result.getGjahr());
    transferOrderRepository.saveAndFlush(transferOrder);
  }

  private WMSTransferOrderReturnResult buildWMSResponse(WMSTransferOrderReturnResult result, String type, String message) {
    RETURNDTO dto = new RETURNDTO();
    dto.setType(type);
    dto.setMsg(message);
    result.setReturnDTO(dto);
    return result;
  }

  @Override
  public MM_076Result sapProcess(String id) {
    MM_076Result mm076Result = new MM_076Result();
    try {
      TransferOrderVO transferOrderVO = this.getDetail(id);
      return sapService.sapTransferOrder(sapFactory.createMM076Param(transferOrderVO));
    }catch (Exception e){
      log.error("调拨单调用sap调拨凭证过账接口执行失败", e);
      mm076Result.setMessage(StrUtil.maxLength(ExceptionUtil.getSimpleMessage(e),500));
      mm076Result.setType(Constants_Sap.ERROR_TYPE);
      return mm076Result;
    }
  }

  @SneakyThrows
  @Override
  public byte[] downloadDetail(List<TransferOrderFillDTO> fillDTOList) {
    XSSFWorkbook workbook = new XSSFWorkbook();
    List<Integer> widths = new ArrayList<>();
    int size = Constants_Excel.DOWNLOAD_TRANSFER_ORDER_INFO.size();
    for (int i = 0; i < size; i++) {
      widths.add(30);
    }
    Sheet sheet = exportUtil.createSheet(workbook, "product", widths);
    CellStyle titleStyle = exportUtil.getTitleStyle(workbook);
    CellStyle baseStyle = exportUtil.getBaseStyle(workbook);
    Row titleRow = sheet.createRow(0);
    exportUtil.createTitle(Constants_Excel.DOWNLOAD_TRANSFER_ORDER_INFO, titleStyle,titleRow);
    fillDTOList = CollUtil.emptyIfNull(fillDTOList);
    for (int i = 0; i < fillDTOList.size(); i++) {
      Row row = sheet.createRow(i + 1);
      TransferOrderFillDTO dto = fillDTOList.get(i);
      int index = 0;
      exportUtil.createCell(row, index++, dto.getRowId(), baseStyle);
      exportUtil.createCell(row, index++, dto.getProductCode(), baseStyle);
      exportUtil.createCell(row, index++, dto.getBrand(), baseStyle);
      exportUtil.createCell(row, index++, dto.getProductName(), baseStyle);
      exportUtil.createCell(row, index++, dto.getDesc(), baseStyle);
      exportUtil.createCell(row, index++, dto.getModel(), baseStyle);
      exportUtil.createCell(row, index++, dto.getUnit(), baseStyle);
      // 调拨数量 保留三位小数
      BigDecimal num = dto.getNum();
      String numStr = num == null ? "" : num.setScale(3, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
      exportUtil.createCell(row, index++, numStr, baseStyle);
      exportUtil.createCell(row, index++, dto.getBatchNo(), baseStyle);
      exportUtil.createCell(row, index++, dto.getRemark(), baseStyle);
    }
    ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
    workbook.write(byteArrayOutputStream);
    return byteArrayOutputStream.toByteArray();
  }

  @Override
  public List<TransferOrderFillDTO> fill(MultipartFile file) throws IOException {
    if (file == null || file.isEmpty()) {
      throw new CheckException("文件不能为空");
    }
    List<TransferOrderFillDTO> res = new ArrayList<>();
    try (Workbook workbook = exportUtil.buildByFile(StrUtil.emptyIfNull(file.getOriginalFilename()),
        file.getInputStream());) {
      Sheet sheet = workbook.getSheetAt(0);
      if (!exportUtil.validateExcel(sheet, 0, Constants_Excel.DOWNLOAD_TRANSFER_ORDER_INFO)) {
        throw new CheckException("文件异常，请导入指定模板。");
      }
      int rowNumCount = sheet.getPhysicalNumberOfRows();
      if (rowNumCount > 1) {
        for (int i = 0; i < rowNumCount; i++) {
          Row row = sheet.getRow(i + 1);
          if (exportUtil.isEmptyRow(row)) {
            continue;
          }
          boolean fail = false;
          TransferOrderFillDTO dto = new TransferOrderFillDTO();
          int index = 0;
          try {
            String rowId = exportUtil.getCellStringValue(row.getCell(index++));
            String productCode = exportUtil.getCellStringValue(row.getCell(index++));
            String brand = exportUtil.getCellStringValue(row.getCell(index++));
            String productName = exportUtil.getCellStringValue(row.getCell(index++));
            String desc = exportUtil.getCellStringValue(row.getCell(index++));
            String model = exportUtil.getCellStringValue(row.getCell(index++));
            String unit = exportUtil.getCellStringValue(row.getCell(index++));
            String numStr = exportUtil.getCellStringValue(row.getCell(index++));
            String batchNo = exportUtil.getCellStringValue(row.getCell(index++));
            String remark = exportUtil.getCellStringValue(row.getCell(index++));
            // 报错文案：导入完成！【序号】【物料编码】报错文案}、【序号】【物料编码】报错文案}、【序号】【物料编码】报错文案}
            StringBuilder errorMsg = new StringBuilder();
            errorMsg.append("【").append(rowId).append("】")
                .append("【").append(productCode).append("】");
            // 退货数量最大填写三位小数
            BigDecimal num = Convert.toBigDecimal(numStr);
            if (num != null && num.scale() > 3) {
              errorMsg.append(", 调拨数量最大填写三位小数");
              fail = true;
            }
            // 备注最大只支持50字
            if (StrUtil.isNotBlank(remark) && remark.length() > 50) {
              errorMsg.append(", 备注最大只支持50字");
              fail = true;
            }
            if (fail) {
              dto.setErrorMsg(errorMsg.toString());
            }
            dto.setRowId(rowId);
            dto.setProductCode(productCode);
            dto.setBrand(brand);
            dto.setProductName(productName);
            dto.setDesc(desc);
            dto.setModel(model);
            dto.setUnit(unit);
            dto.setNum(num);
            dto.setBatchNo(batchNo);
            dto.setRemark(remark);
            res.add(dto);
          } catch (Exception e) {
            log.error("第" + (i + 1) + "行数据异常", e);
            throw new CheckException("第" + (i + 1) + "行数据异常：" + e.getMessage());
          }
        }
      }
      return res;
    }
  }
}

