package com.xhgj.srm.service;

import com.xhgj.srm.jpa.entity.SearchScheme;
import com.xhiot.boot.framework.jpa.service.BootBaseService;

/** <AUTHOR> @ClassName SearchSchemeService */
public interface TempSearchSchemeService extends BootBaseService<SearchScheme, String> {
  /**
   * 获得默认的查询方案
   *
   * @param useId 用户 id
   * @param searchTypeSupplier 查询方案的类型
   */
  SearchScheme getDefaultSearchScheme(String useId, String searchTypeSupplier);
}
