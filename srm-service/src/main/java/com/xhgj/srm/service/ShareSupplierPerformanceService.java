package com.xhgj.srm.service;

import com.xhgj.srm.common.dto.OrderPlatformDTO;
import com.xhgj.srm.domain.SupplierWithPerformanceDomain;
import com.xhgj.srm.jpa.entity.LandingMerchantContract;
import com.xhgj.srm.jpa.entity.SupplierPerformance;
import com.xhgj.srm.jpa.entity.User;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import java.util.List;

/**
 * Created by Geng Shy on 2023/10/27
 */
public interface ShareSupplierPerformanceService extends
    BootBaseService<SupplierPerformance, String> {

  /**
   * 关联合同
   *
   * @param supplierId 供应商id
   * @param contractId 合同id
   * @param platformCode 平台对象
   */
  void relatedContract(String supplierId, String contractId, String platformCode, String userId);

  /**
   * 关联合同
   *
   * @param supplierId 供应商id
   * @param contractId 合同id
   * @param platformCode 平台对象
   */
  void relatedContract(String supplierId, String contractId, String platformCode, String userId, boolean forceOpenStatus);

  /**
   * 根据合同删除所有的供应商履约信息
   * @param landingContractId 合同 id
   */
  void deleteByLandingContractId(String landingContractId);

  /**
   * 删除关联的合同
   * @param supplierId
   * @param platformCode
   */
  void deleteRelatedContract(String supplierId, List<String> platformCode);

  /**
   * 获取供应商履约信息
   *
   * @param enterpriseName 供应商名称，模糊
   * @param platformCode 平台编码，必传
   */
  List<SupplierWithPerformanceDomain> getSupplierWithPerformanceByPlatformCode(String enterpriseName, String platformCode);
}

