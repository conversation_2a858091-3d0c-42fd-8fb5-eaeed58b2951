package com.xhgj.srm.service;

import com.xhgj.srm.jpa.entity.PaymentApplyDetail;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface SharePaymentApplyDetailService {

  /**
   * 根据申请详情和申请记录类型查询出相关金额
   * @param paymentApplyDetails
   * @param applyType
   * @return
   */
  BigDecimal getApplyAmount(List<PaymentApplyDetail> paymentApplyDetails, String applyType);

  /**
   * 更新付款申请中预付申请的supplierName
   */
  void updateAdvanceSupplierName(String supplierOrderNo, String newSupplierName);
}
