package com.xhgj.srm.service.impl;

import com.xhgj.srm.common.dto.ApprovalResult;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormReviewStatus;
import com.xhgj.srm.service.DingTalkApprovalCallback;
import com.xhgj.srm.v2.service.purchaseOrder.PurchaseOrderV2ReturnService;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * @Author: fanghuanxu
 * @Date: 2025/2/25 14:29
 * @Description: 采购订单退库单审批结果事件处理
 */
@Service
public class SupplierOrderReturnCallbackImpl implements DingTalkApprovalCallback {

  @Resource
  PurchaseOrderV2ReturnService purchaseOrderV2ReturnService;


  @Override
  public void doPassHandle(ApprovalResult approvalResult) {
    purchaseOrderV2ReturnService.auditCallBack(approvalResult, SupplierOrderFormReviewStatus.NORMAL);
  }

  @Override
  public void doRejectHandle(ApprovalResult approvalResult) {
    purchaseOrderV2ReturnService.auditCallBack(approvalResult, SupplierOrderFormReviewStatus.FEI_DA_REJECT);
  }
}
