package com.xhgj.srm.service;

import com.xhgj.srm.jpa.entity.EntryRegistrationLandingMerchant;
import com.xhgj.srm.jpa.entity.Platform;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface EntryRegistrationBatchGetHandler {

  /**
   * 返回入驻报备相关信息
   */
  void handleEntryRegistrationBatchGet(
      Map<String, EntryRegistrationLandingMerchant> entryId2Merchant,
      Map<String, Platform> id2Platform);
}
