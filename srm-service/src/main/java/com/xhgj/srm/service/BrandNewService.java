package com.xhgj.srm.service;

import com.xhgj.srm.common.enums.BrandRelationTypeEnum;
import com.xhgj.srm.dto.BrandInfoDTO;
import com.xhgj.srm.jpa.entity.Brand;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import java.util.List;

public interface BrandNewService extends BootBaseService<Brand, String> {


  /**
   * 获取关联品牌列表
   *
   * @param relationId 关联 id
   * @param relationType 关联类型
   */
  List<Brand> getBrandsByRelationIdAndType(String relationId, BrandRelationTypeEnum relationType);

  /**
   * 根据 mpmBrandCode 获取关联品牌信息
   * @param mpmBrandCode mpmBrandCode
   * @return List<BrandInfoDTO>
   */
  List<BrandInfoDTO> getBrandInfoDTOListByMpmBrandCode(String mpmBrandCode);
}
