package com.xhgj.srm.service;

import com.xhgj.srm.dto.order.DeliveryDetailDTO;
import com.xhgj.srm.jpa.entity.OrderDelivery;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-06-06 13:35
 */
public interface OrderDeliveryService extends BootBaseService<OrderDelivery,String> {

  /**
   * 根据订单id获取订单发货详情视图对象
   * @param orderId 订单id
   */
  List<DeliveryDetailDTO> getDeliveryDetailVOByOrderId(String orderId);

  /**
   * 订单是否发货
   * @param orderId 订单id
   * @return 是否存在
   */
  boolean existByOrderId(String orderId);

  /**
   * @param orderId 订单id
   * @return 发货单集合
   */
  List<OrderDelivery> findAllByOrderId(String orderId);
}
