package com.xhgj.srm.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.Constants_order;
import com.xhgj.srm.common.vo.order.OrderCancelDetailVO;
import com.xhgj.srm.common.vo.order.OrderCancelVO;
import com.xhgj.srm.dto.order.ReturnInfoDTO;
import com.xhgj.srm.factory.MapStructFactory;
import com.xhgj.srm.jpa.dao.OrderReturnDao;
import com.xhgj.srm.jpa.entity.OrderCancel;
import com.xhgj.srm.jpa.entity.OrderCancelDetail;
import com.xhgj.srm.jpa.entity.OrderReturn;
import com.xhgj.srm.jpa.repository.OrderCancelDetailRepository;
import com.xhgj.srm.jpa.repository.OrderCancelRepository;
import com.xhgj.srm.jpa.repository.OrderReturnRepository;
import com.xhgj.srm.service.OrderReturnDetailTempService;
import com.xhgj.srm.service.OrderReturnTempService;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2023-06-04 17:46
 */
@Service
public class OrderReturnTempServiceImpl implements OrderReturnTempService {

  @Autowired
  private OrderReturnRepository repository;
  @Autowired
  private OrderReturnDao dao;
  @Autowired
  private OrderReturnDetailTempService orderReturnDetailTempService;
  @Resource
  private OrderCancelDetailRepository orderCancelDetailRepository;
  @Resource
  private OrderCancelRepository orderCancelRepository;

  @Override
  public BootBaseRepository<OrderReturn, String> getRepository() {
    return repository;
  }

  @Override
  public List<OrderReturn> getOrderReturnByOrderId(String orderId) {
    Assert.notBlank(orderId);
    return dao.getByOrderIdAndType(orderId, Constants_order.ORDER_RETURN_TYPE);
  }

  @Override
  public List<ReturnInfoDTO> getOrderReturnVOByOrderId(String orderId) {
    return getOrderReturnByOrderId(orderId).stream().map(orderReturn -> {
      ReturnInfoDTO returnInfoDTO = new ReturnInfoDTO();
      returnInfoDTO.setReturnNo(orderReturn.getReturnNo());
      returnInfoDTO.setReturnId(orderReturn.getId());
      returnInfoDTO.setReturnTime(orderReturn.getCreateTime());
      returnInfoDTO.setReturnPrice(orderReturn.getPrice());
      returnInfoDTO.setErpNo(orderReturn.getErpNo());
      returnInfoDTO.setPurchaseOrderNo(orderReturn.getPurchaseOrderNo());
      returnInfoDTO.setState(orderReturn.getReturnState());
      returnInfoDTO.setStateStr(StrUtil.isNotBlank(orderReturn.getReturnState())
          ? Constants_order.ORDER_RETURN_STATE_MAP.get(orderReturn.getReturnState()) : "-");
      returnInfoDTO.setReturnDetailList(CollUtil.emptyIfNull(
              orderReturnDetailTempService.getReturnDetailByReturnId(orderReturn.getId())).stream()
          .map(com.xhgj.srm.dto.order.ReturnDetailInfoDTO::new).collect(Collectors.toList()));
      return returnInfoDTO;
    }).collect(Collectors.toList());
  }

  @Override
  public List<OrderCancelVO> getOrderCancelDetailByOrderId(String orderId) {
    List<OrderCancel> orderCancelList =
        orderCancelRepository.findAllByOrderIdAndState(orderId, Constants.STATE_OK);
    if (CollUtil.isEmpty(orderCancelList)) {
      return Collections.emptyList();
    }
    List<String> cancelIds =
        orderCancelList.stream().map(OrderCancel::getId).collect(Collectors.toList());
    List<OrderCancelDetail> orderCancelDetailList =
        orderCancelDetailRepository.findAllByCancelIdInAndState(cancelIds, Constants.STATE_OK);
    Map<String, List<OrderCancelDetail>> orderCancelDetailMap =
        orderCancelDetailList.stream().collect(Collectors.groupingBy(OrderCancelDetail::getCancelId));
    List<OrderCancelVO> res = orderCancelList.stream().map(item -> {
      OrderCancelVO orderCancelVO = MapStructFactory.INSTANCE.toOrderCancelVO(item);
      List<OrderCancelDetail> details =
          orderCancelDetailMap.getOrDefault(orderCancelVO.getId(), new ArrayList<>());
      List<OrderCancelDetailVO> detailsVos =
          details.stream().map(MapStructFactory.INSTANCE::toOrderCancelDetailVO)
              .collect(Collectors.toList());
      orderCancelVO.setDetails(detailsVos);
      return orderCancelVO;
    }).collect(Collectors.toList());
    return res;
  }
}
