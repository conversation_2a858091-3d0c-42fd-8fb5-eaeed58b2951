package com.xhgj.srm.service.impl;/**
 * @since 2025/4/22 10:54
 */

import com.xhgj.srm.common.dto.ApprovalResult;
import com.xhgj.srm.common.enums.purchaseApplyForOrder.PurchaseApplyRecordStatus;
import com.xhgj.srm.service.DingTalkApprovalCallback;
import com.xhgj.srm.v2.service.PurchaseApplyForOrderV2Service;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

/**
 *<AUTHOR>
 *@date 2025/4/22 10:54:18
 *@description
 */
@Service
public class PurchaseApplyForOrderCallBackImpl implements DingTalkApprovalCallback {

  @Resource
  private PurchaseApplyForOrderV2Service purchaseApplyForOrderV2Service;


  @Override
  public void doPassHandle(ApprovalResult approvalResult) {
    purchaseApplyForOrderV2Service.audit(approvalResult, PurchaseApplyRecordStatus.APPROVED);
  }

  @Override
  public void doRejectHandle(ApprovalResult approvalResult) {
    purchaseApplyForOrderV2Service.audit(approvalResult, PurchaseApplyRecordStatus.REJECTED);
  }
}
