package com.xhgj.srm.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.xhgj.srm.common.enums.entryregistration.EntryRegistrationDiscountTypeEnum;
import com.xhgj.srm.jpa.entity.EntryRegistrationLandingMerchant;
import com.xhgj.srm.jpa.entity.EntryRegistrationOrder;
import com.xhgj.srm.request.dto.supplierRate.SupplierRateAddDto;
import com.xhgj.srm.request.dto.supplierRate.SupplierRateBatchAddParam;
import com.xhgj.srm.request.dto.supplierRate.SupplierRateDetailParam;
import com.xhgj.srm.jpa.entity.Platform;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.registration.dto.entryregistration.EntryRegistrationDiscountDTO;
import com.xhgj.srm.registration.entity.EntryRegistrationEntity;
import com.xhgj.srm.registration.repository.EntryRegistrationRepository;
import com.xhgj.srm.request.service.third.oms.OMSService;
import com.xhgj.srm.service.EntryRegistrationBatchGetHandler;
import com.xhgj.srm.service.ShareEntryRegistrationService;
import com.xhiot.boot.core.common.exception.CheckException;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class ShareEntryRegistrationServiceImpl implements ShareEntryRegistrationService {

  @Resource
  OMSService omsService;
  @Resource
  EntryRegistrationRepository registrationRepository;

  @Override
  public void batchUpdateDiscountToOms(EntryRegistrationEntity order) {
    // 获取供应商
    Supplier supplier =
        registrationRepository.getSupplierById(order.getMerchant().getSupplierId());
    if (supplier == null) {
      throw new CheckException("供应商不存在");
    }
    List<EntryRegistrationDiscountDTO> discounts =
        Optional.ofNullable(order.getDiscounts()).orElse(new ArrayList<>()).stream().map(item -> {
          EntryRegistrationDiscountDTO dto = new EntryRegistrationDiscountDTO();
          dto.setId(item.getId());
          dto.setType(item.getType());
          dto.setPerformanceAmount(item.getPerformanceAmount());
          dto.setDiscountRatio(item.getDiscountRatio());
          dto.setBrandId(item.getBrandId());
          dto.setBrandName(item.getBrandName());
          return dto;
        }).collect(Collectors.toList());

    List<String> platformCodeList =
        order.getPlatformList().stream().map(Platform::getCode).collect(Collectors.toList());
    this.batchUpdateDiscountToOms(supplier.getEnterpriseName(), supplier.getId(), platformCodeList, discounts);
  }

  @Override
  public void batchUpdateDiscountToOms(String supplierName, String supplierId, List<String> platformCodeList, List<EntryRegistrationDiscountDTO> discounts) {
    if (CollUtil.isEmpty(discounts)) {
      return;
    }
    List<EntryRegistrationDiscountDTO> stepDiscountList =
        discounts.stream().filter(item -> item.getType()
                .equals(EntryRegistrationDiscountTypeEnum.STEP_DISCOUNT_RATIO.getKey()))
            .collect(Collectors.toList());
    List<EntryRegistrationDiscountDTO> brandDiscountList =
        discounts.stream().filter(item -> item.getType()
                .equals(EntryRegistrationDiscountTypeEnum.BRAND_DISCOUNT.getKey()))
            .collect(Collectors.toList());
    List<SupplierRateDetailParam> supplierRateDetailList = stepDiscountList.stream().map(
            item -> new SupplierRateDetailParam(item.getPerformanceAmount(), item.getDiscountRatio()))
        .collect(Collectors.toList());
    List<SupplierRateDetailParam> brandRateDetailList = brandDiscountList.stream().map(
        item -> new SupplierRateDetailParam(item.getBrandName(), item.getBrandId(),
            item.getDiscountRatio())).collect(Collectors.toList());
    //保存到oms
    List<SupplierRateAddDto> supplierRateAddList = new ArrayList<>();
    for (String platformCode : platformCodeList) {
      SupplierRateBatchAddParam supplierRateBatchAddParam = new SupplierRateBatchAddParam();
      SupplierRateAddDto supplierRateAddParam = new SupplierRateAddDto();
      supplierRateAddParam.setSupplierId(supplierId);
      supplierRateAddParam.setSupplierName(supplierName);
      supplierRateAddParam.setPlatform(platformCode);
      supplierRateAddParam.setSupplierRateDetailParams(supplierRateDetailList);
      supplierRateAddParam.setOrderRateBrandDetailParams(brandRateDetailList);
      supplierRateAddList.add(supplierRateAddParam);
      supplierRateBatchAddParam.setSupplierRateAddList(supplierRateAddList);
    }
    if (CollUtil.isEmpty(supplierRateAddList)) {
      return;
    }
    SupplierRateBatchAddParam supplierRateBatchAddParam = new SupplierRateBatchAddParam();
    supplierRateBatchAddParam.setSupplierRateAddList(supplierRateAddList);
    //设置折扣比例
    omsService.supplierRateAdd(supplierRateBatchAddParam);
  }

  @Override
  public void batchUpdateDiscountToOmsClear(EntryRegistrationEntity order) {
    List<String> platformCodeList =
        order.getPlatformList().stream().map(Platform::getCode).collect(Collectors.toList());
    // 获取供应商
    Supplier supplier =
        registrationRepository.getSupplierById(order.getMerchant().getSupplierId());
    if (supplier == null) {
      throw new CheckException("供应商不存在");
    }
    List<SupplierRateAddDto> supplierRateAddList = new ArrayList<>();
    //保存到oms
    for (String platformCode : platformCodeList) {
      SupplierRateAddDto supplierRateAddParam = new SupplierRateAddDto();
      supplierRateAddParam.setSupplierId(supplier.getId());
      supplierRateAddParam.setSupplierName(supplier.getEnterpriseName());
      supplierRateAddParam.setPlatform(platformCode);
      supplierRateAddParam.setSupplierRateDetailParams(new ArrayList<>());
      supplierRateAddParam.setOrderRateBrandDetailParams(new ArrayList<>());
      supplierRateAddList.add(supplierRateAddParam);
    }
    if (CollUtil.isEmpty(supplierRateAddList)) {
      return;
    }
    SupplierRateBatchAddParam supplierRateBatchAddParam = new SupplierRateBatchAddParam();
    supplierRateBatchAddParam.setSupplierRateAddList(supplierRateAddList);
    //设置折扣比例
    omsService.supplierRateAdd(supplierRateBatchAddParam);
  }

  @Override
  public void batchUpdateDiscountToOmsClear(Supplier supplier, List<String> platformCodes) {
    if (supplier == null) {
      throw new CheckException("供应商不存在");
    }
    List<SupplierRateAddDto> supplierRateAddList = new ArrayList<>();
    //保存到oms
    for (String platformCode : platformCodes) {
      SupplierRateAddDto supplierRateAddParam = new SupplierRateAddDto();
      supplierRateAddParam.setSupplierId(supplier.getId());
      supplierRateAddParam.setSupplierName(supplier.getEnterpriseName());
      supplierRateAddParam.setPlatform(platformCode);
      supplierRateAddParam.setSupplierRateDetailParams(new ArrayList<>());
      supplierRateAddParam.setOrderRateBrandDetailParams(new ArrayList<>());
      supplierRateAddList.add(supplierRateAddParam);
    }
    if (CollUtil.isEmpty(supplierRateAddList)) {
      return;
    }
    SupplierRateBatchAddParam supplierRateBatchAddParam = new SupplierRateBatchAddParam();
    supplierRateBatchAddParam.setSupplierRateAddList(supplierRateAddList);
    //设置折扣比例
    omsService.supplierRateAdd(supplierRateBatchAddParam);
  }

  @Override
  public void batchGetEntryRegistrationInfo(List<EntryRegistrationOrder> list, EntryRegistrationBatchGetHandler handler) {
    if (CollUtil.isEmpty(list)) {
      return;
    }
    // 批量获取落地商信息
    List<String> entryRegistrationOrderIds =
        list.stream().map(EntryRegistrationOrder::getId).collect(Collectors.toList());
    registrationRepository.getMerchantListByOrderIds(entryRegistrationOrderIds);
    Map<String, EntryRegistrationLandingMerchant> id2EntryRegistrationLandingMerchant =
        registrationRepository.getMerchantListByOrderIds(entryRegistrationOrderIds).stream()
            .collect(Collectors.toMap(EntryRegistrationLandingMerchant::getEntryRegistrationOrderId,
                Function.identity()));

    // 批量获取平台Codes
    String platformCodes = list.stream().map(EntryRegistrationOrder::getOriginPlatformList).flatMap(
            Collection::stream).filter(Objects::nonNull).distinct()
        .collect(Collectors.joining(","));
    if (platformCodes.isEmpty()) {
      platformCodes = "-1";
    }
    Map<String, Platform> id2Platform =
        registrationRepository.getPlatformListByPlatformCodes(platformCodes).stream()
            .collect(Collectors.toMap(Platform::getCode, Function.identity(), (k1, k2) -> k2));
    handler.handleEntryRegistrationBatchGet(id2EntryRegistrationLandingMerchant, id2Platform);
  }
}
