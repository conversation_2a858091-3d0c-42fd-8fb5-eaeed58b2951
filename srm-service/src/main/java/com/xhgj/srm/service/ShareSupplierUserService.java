package com.xhgj.srm.service;

import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierUser;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import java.util.List;
import java.util.Optional;

/**
 * Created by Geng Shy on 2023/10/13
 */
public interface ShareSupplierUserService extends BootBaseService<SupplierUser, String> {

  /**
   * 根据供应商id查询供应商用户
   *
   * @param supplierId 供应商id
   * @return 供应商用户集合
   */
  Optional<List<SupplierUser>> getSupplierUserListBySupplierIdAsc(String supplierId);

  boolean createIfNotExist(Supplier supplier, String name, String mobile, String emailAddress, String pwd, String createMan);
}
