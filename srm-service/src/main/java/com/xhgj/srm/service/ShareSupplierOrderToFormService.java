package com.xhgj.srm.service;

import com.xhgj.srm.common.enums.LogicalOperatorsEnums;
import com.xhgj.srm.jpa.dto.RetreatWarehousePageDTO;
import com.xhgj.srm.jpa.dto.WarehousingDTO;
import com.xhgj.srm.jpa.entity.SupplierOrderToForm;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import java.math.BigDecimal;
import java.util.List;

public interface ShareSupplierOrderToFormService extends BootBaseService<SupplierOrderToForm,
    String> {

  /**
   * 获得订单明细的表单信息
   *
   * @param supplierOrderId 供应商订单 id 必传
   */
  SupplierOrderToForm getDetailedBySupplierOrderId(String supplierOrderId);
}
