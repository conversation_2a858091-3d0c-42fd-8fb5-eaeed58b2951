package com.xhgj.srm.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.jpa.entity.SupplierInvoiceToDetail;
import com.xhgj.srm.jpa.entity.SupplierOrder;
import com.xhgj.srm.jpa.entity.SupplierOrderDetail;
import com.xhgj.srm.jpa.repository.SupplierInvoiceToDetailRepository;
import com.xhgj.srm.jpa.repository.SupplierOrderDetailRepository;
import com.xhgj.srm.jpa.repository.SupplierOrderRepository;
import com.xhgj.srm.service.SupplierInvoiceToDetailService;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.TreeSet;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
public class SupplierInvoiceToDetailServiceImpl implements SupplierInvoiceToDetailService {

  @Resource
  private SupplierInvoiceToDetailRepository repository;
  @Resource
  private SupplierOrderDetailRepository supplierOrderDetailRepository;
  @Resource
  private SupplierOrderRepository supplierOrderRepository;
  @Override
  public BootBaseRepository<SupplierInvoiceToDetail, String> getRepository() {
    return repository;
  }


  @Override
  @Deprecated
  public Map<SupplierOrder, List<SupplierInvoiceToDetail>> getInvoiceToDetailsGroupedByOrder(String inputInvoiceOrderId) {
    // todo 2024年11月21日19:42:27明天看看逻辑
    Map<String, SupplierInvoiceToDetail> supplierInvoiceToDetailMap =
        repository.findAllByInputInvoiceOrderId(inputInvoiceOrderId).stream().collect(Collectors.toMap(SupplierInvoiceToDetail::getDetailId,
        Function.identity(), (v1, v2) -> v1));

    Collection<SupplierInvoiceToDetail> supplierInvoiceToDetails = supplierInvoiceToDetailMap.values();

    if (CollUtil.isEmpty(supplierInvoiceToDetails)) {
      return Collections.emptyMap();
    }
    List<SupplierOrderDetail> supplierOrderDetails =
        supplierInvoiceToDetails.stream().map(supplierInvoiceToDetail -> supplierOrderDetailRepository.findById(supplierInvoiceToDetail.getDetailId())
            .orElseThrow(() -> CheckException.noFindException(SupplierOrderDetail.class,
                supplierInvoiceToDetail.getDetailId()))).collect(Collectors.toList());

    ArrayList<SupplierOrderDetail> supplierOrderDetailsDeduplication = supplierOrderDetails.stream().collect(
        Collectors.collectingAndThen(Collectors.toCollection(
                () -> new TreeSet<>(Comparator.comparing(SupplierOrderDetail::getPurchaseOrderId))),
            ArrayList::new));
    List<SupplierOrder> supplierOrders = supplierOrderDetailsDeduplication.stream()
        .map(supplierOrderDetail -> supplierOrderRepository.findById(supplierOrderDetail.getPurchaseOrderId())
            .orElseThrow(() -> CheckException.noFindException(SupplierOrder.class, supplierOrderDetail.getPurchaseOrderId())))
        .collect(Collectors.toList());
    HashMap<SupplierOrder, List<SupplierInvoiceToDetail>> result =
        new HashMap<>();
    for (SupplierOrder supplierOrder : supplierOrders) {
      List<SupplierOrderDetail> details =
          supplierOrderDetails.stream().filter(supplierOrderDetail1 -> {
            String purchaseOrderId = supplierOrderDetail1.getPurchaseOrderId();
            return Objects.equals(purchaseOrderId, supplierOrder.getId());
          }).collect(Collectors.toList());
      ArrayList<SupplierInvoiceToDetail> supplierInvoiceToDetailsResult = new ArrayList<>();
      for (SupplierOrderDetail detail : details) {
        SupplierInvoiceToDetail supplierInvoiceToDetail =
            supplierInvoiceToDetailMap.get(detail.getId());
        supplierInvoiceToDetailsResult.add(supplierInvoiceToDetail);
      }
      result.put(supplierOrder, supplierInvoiceToDetailsResult);
    }
    return result;
  }

  @Override
  public List<SupplierInvoiceToDetail> getAllByInputInvoiceOrderId(String inputInvoiceOrderId) {
    return repository.findAllByInputInvoiceOrderId(inputInvoiceOrderId);
  }

  @Override
  public void deleteByInputOrderId(String inputInvoiceOrderId) {
    repository.deleteByInputInvoiceOrderId(inputInvoiceOrderId);
  }
}
