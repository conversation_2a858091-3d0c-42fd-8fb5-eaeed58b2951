package com.xhgj.srm.service;

import com.xhgj.srm.common.dto.OrderPlatformDTO;
import com.xhgj.srm.jpa.entity.Platform;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import java.util.Collection;
import java.util.List;

/**
 * Created by Geng Shy on 2023/10/18
 */
public interface SharePlatformService extends BootBaseService<Platform, String> {

  /**
   * @return 查询全部平台
   * 未查询到结果时，返回空集合
   */
  List<OrderPlatformDTO> findAll();

  /**
   * @param code 编码
   * @return 名称
   * 为查询到结果时，返回null
   */
  String findNameByCode(String code);

  /**
   * @param name 名称
   * @return 编码
   * 未查询到结果时，返回null
   */
  String findCodeByName(String name);

  /**
   * @param code 编码
   * @return 下单平台
   * 未查询到结果时，返回null
   */
  OrderPlatformDTO findByCode(String code);

  /**
   * @param code 编码
   * @return Platform
   * 未查询到结果时，返回null
   */
  Platform getEntityByCode(String code);

  /**
   * @param name 名称
   * @return 下单平台
   * 未查询到结果时，返回null
   */
  OrderPlatformDTO findByName(String name);

  /**
   * 根据平台编码批量查询
   * @param codes 编码
   * @return 平台DTO对象集合，若查询不到则返回空集合。
   */
  List<OrderPlatformDTO> batchFindByCode(Collection<String> codes);

  /**
   * 根据平台名称批量查询
   * @param platformNames
   * @return
   */
  List<Platform> findByNameIn(List<String> platformNames);

  String findDefaultPurchaseByCode(String code);
}
