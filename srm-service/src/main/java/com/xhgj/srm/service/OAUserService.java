package com.xhgj.srm.service;

import com.xhgj.srm.common.dto.OAUserInfoDTO;
import com.xhgj.srm.common.dto.XhgjPersonInfoDTO;
import com.xhgj.srm.dto.oa.OAUserPageDTO;
import com.xhgj.srm.request.dto.person.FindPersonPageParam;
import com.xhgj.srm.request.dto.person.FindPersonPageVO;
import com.xhiot.boot.mvc.base.PageResult;
import java.util.List;

/**
 * <AUTHOR> <PERSON> @date 2023/6/21
 */
public interface OAUserService {

  /**
   * 条件查询oa用户
   * @param mobile
   * @param name
   * @param pageNo
   * @param pageSize
   * @return
   */
  PageResult<OAUserPageDTO> getOAUserPage(String mobile, String name, boolean encrypted, int pageNo,
      int pageSize);

  /**
   * 根据手机号和姓名查询，如查询出多条视为异常情况
   * @param mobile
   * @param name
   * @return
   */
  OAUserPageDTO getOAUserByMobileAndName(final String mobile, final String name) throws RuntimeException;

  /**
   * 根据oaId获取名字
   * @param oaId
   * @return
   */
  String getOaUserName(String oaId);

  /**
   * 根据oaId获取手机号
   * @param oaId
   * @return
   */
  String getOaUserMobile(String oaId);

  /**
   * 根据条件精确查找
   * @param name 姓名
   * @param mobile 手机号
   */
  XhgjPersonInfoDTO getOAUserInfo(String name, String mobile);

  /**
   * 根据钉钉id查询
   * @param dingTalkId 钉钉id
   */
  OAUserInfoDTO getOAUserInfoByDingTalkId(String dingTalkId);

  /**
   * 分页查询咸亨国际人员信息（北森的数据）
   */
  PageResult<FindPersonPageVO> getMdmPersonUserPage(FindPersonPageParam param);

  /**
   * 根据工号查询咸亨国际人员信息（北森的数据）
   * @param param
   */
  List<FindPersonPageVO> getMdmPersonUserPageByJobNumber(FindPersonPageParam param);
}
