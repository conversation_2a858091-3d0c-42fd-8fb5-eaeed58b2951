package com.xhgj.srm.service.impl;

import com.xhgj.srm.jpa.entity.SupplierUser;
import com.xhgj.srm.jpa.repository.SupplierUserRepository;
import com.xhgj.srm.service.TempSupplierUserService;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/** @ClassName SupplierUserServiceImpl Create by Liuyq on 2021/6/8 19:03 */
@Service
@Slf4j
public class TempSupplierUserServiceImpl implements TempSupplierUserService {

  @Autowired private SupplierUserRepository repository;

  @Override
  public BootBaseRepository<SupplierUser, String> getRepository() {
    return repository;
  }
}
