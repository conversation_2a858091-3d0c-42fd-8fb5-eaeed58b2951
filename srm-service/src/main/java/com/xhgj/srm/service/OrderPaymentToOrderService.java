package com.xhgj.srm.service;

import com.xhgj.srm.jpa.entity.OrderPayment;
import com.xhgj.srm.jpa.entity.OrderPaymentToOrder;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-03-16 15:38
 */
public interface OrderPaymentToOrderService extends BootBaseService<OrderPaymentToOrder,String> {

  /**
   * 创建付款的的中间信息
   * @param orderPaymentId 付款单 id 必传
   * @param relationId 关联 id
   * @param type 类型
   * @param createTime 创建时间
   * @param content 内容
   * @param applyPrice 付款金额
   */
  OrderPaymentToOrder createOrderPaymentToOrder(String orderPaymentId, String relationId,
      String type, long createTime, String content, BigDecimal applyPrice);
  /**
   * 通过关联 id 和类型、数据状态获取数量
   * @param relationId 关联 id
   * @param type 类型
   */
  long getCountByOrderPaymentIdAndTypeAndState(String relationId, String type);

  /**
   * 通过付款 id 获取订单id Author: liuyq @Date: 2023/3/17 14:54
   *
   * @param paymentId 付款 id
   * @return java.util.List<java.lang.String>
   */
  List<String> getOrderIdListByPaymentId(String paymentId);
  /***
   *  获取erp的付款单号
   * @param paymentId 付款单id 必填
   * <AUTHOR>
   * @date 2023/3/21 9:13
   * @return java.util.List<java.lang.String>
   */
  List<OrderPaymentToOrder> getErpPaymentNoListByPaymentId(String paymentId);


  /***
   *  通过订单Id获取付款单Id
   * @param orderId 必填
   * <AUTHOR>
   * @date 2023/3/23 9:26
   * @return String
   */
  String getAllByRelationIdAndStateAndType(String orderId);

  /**
   * 创建付款单信息
   *
   * @param paymentStatus 付款单状态
   * @param applyPrice 申请金额
   * @param submitMan 提交人
   * @param submitId 提交人 id
   * @param remark 供应商 必传
   * @param remark 供应商 Id 必传
   */
  OrderPayment createOrderPayment(String paymentStatus, BigDecimal applyPrice, String submitMan,
      String submitId, String remark, String supplierId);
}
