package com.xhgj.srm.service.impl;/**
 * @since 2024/11/28 11:36
 */

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.dto.MdmBrand;
import com.xhgj.srm.common.enums.landingContract.BundleType;
import com.xhgj.srm.dto.ProvinceFindDto;
import com.xhgj.srm.dto.bundle.SupplierBundleMatchQueryForm;
import com.xhgj.srm.factory.MapStructFactory;
import com.xhgj.srm.handle.LandingContractBundleBatchGetHandler;
import com.xhgj.srm.jpa.dao.LandingContractBundleDao;
import com.xhgj.srm.jpa.dto.landingContract.LandingContractBundleSaveForm;
import com.xhgj.srm.jpa.entity.LandingContractBundle;
import com.xhgj.srm.jpa.entity.LandingMerchantContract;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierPerformance;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.jpa.repository.LandingContractBundleRepository;
import com.xhgj.srm.jpa.repository.LandingMerchantContractRepository;
import com.xhgj.srm.jpa.repository.SupplierPerformanceRepository;
import com.xhgj.srm.jpa.repository.SupplierRepository;
import com.xhgj.srm.jpa.repository.UserRepository;
import com.xhgj.srm.request.dto.edge.ProvinceCityDTO;
import com.xhgj.srm.request.dto.partner.PartnerDTO;
import com.xhgj.srm.request.service.third.mpm.MPMService;
import com.xhgj.srm.request.service.third.partner.PartnerService;
import com.xhgj.srm.request.service.third.xhgj.XhgjEdgeService;
import com.xhgj.srm.service.LandingContractBundleService;
import com.xhgj.srm.vo.bundle.PlatformSupplierBundle;
import com.xhgj.srm.vo.bundle.SupplierBundleMatchBatchVO;
import com.xhgj.srm.vo.bundle.SupplierBundleMatchVO;
import com.xhiot.boot.core.common.exception.CheckException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Queue;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 *<AUTHOR>
 *@date 2024/11/28 11:36:02
 *@description
 */
@Service
@Slf4j
public class LandingContractBundleServiceImpl implements LandingContractBundleService {

  @Resource
  LandingContractBundleRepository landingContractBundleRepository;
  @Resource
  MPMService mpmService;
  @Resource
  XhgjEdgeService xhgjEdgeService;
  @Resource
  PartnerService partnerService;
  @Resource
  LandingContractBundleDao landingContractBundleDao;
  @Resource
  LandingMerchantContractRepository landingMerchantContractRepository;
  @Resource
  SupplierRepository supplierRepository;
  @Resource
  SupplierPerformanceRepository supplierPerformanceRepository;
  @Resource
  UserRepository userRepository;
  @Resource
  ApplicationContext applicationContext;

  private List<ProvinceCityDTO> getProvince() {
    // 获取所有省份 外层包裹全国
    try {
      return xhgjEdgeService.getAllProvinceCityWithCountry().get();
    } catch (Exception e) {
      log.error("获取所有省份数据失败:{}", e.getMessage(), e);
      throw new CheckException("获取所有省份数据失败: " + e.getMessage());
    }
  }

  /**
   * 根据区域编码查询信息
   * 同一个类下请勿直接调用，请使用 #bfsFindByCodeProxy
   * @param areaCode
   * @return
   */
  @Cacheable(value = "srm.provinceFindDto", key = "#areaCode")
  public ProvinceFindDto bfsFindByCode(String areaCode) {
    List<ProvinceCityDTO> provinces = getProvince();
    if (CollUtil.isEmpty(provinces)) {
      throw new CheckException("获取所有省份数据失败");
    }
    Queue<ProvinceCityDTO> queue = new LinkedList<>();
    Queue<List<String>> pathQueue = new LinkedList<>();  // 用来保存每个节点的路径
    // 把所有省级数据放入队列，并初始化路径
    for (ProvinceCityDTO province : provinces) {
      queue.add(province);
      pathQueue.add(Collections.singletonList(province.getCode()));  // 初始路径为当前节点的 code
    }

    // 广度优先遍历
    while (!queue.isEmpty()) {
      ProvinceCityDTO current = queue.poll();
      List<String> currentPath = pathQueue.poll();  // 获取当前节点的路径

      // 如果当前节点的 code 和目标 code 匹配，返回该节点和路径
      if (current.getCode().equals(areaCode)) {
        ProvinceFindDto result = new ProvinceFindDto();
        result.setCode(current.getCode());
        result.setName(current.getName());
        result.setPath(currentPath);
        return result;
      }
      // 如果当前节点有子节点，将其子节点加入队列，同时更新路径
      if (current.getChildren() != null) {
        for (ProvinceCityDTO child : current.getChildren()) {
          assert currentPath != null;
          List<String> newPath = new ArrayList<>(currentPath);
          newPath.add(child.getCode());  // 将子节点的 code 加入路径
          queue.add(child);
          pathQueue.add(newPath);
        }
      }
    }
    // 如果没有找到目标 code，返回 null
    return null;
  }

  /**
   * 同一个类下调用缓存方法
   * @param areaCode
   * @return
   */
  public ProvinceFindDto bfsFindByCodeProxy(String areaCode) {
    return applicationContext.getBean(LandingContractBundleServiceImpl.class).bfsFindByCode(areaCode);
  }

  /**
   * 每隔10天清除一次缓存(首次启动延迟10天)
   */
  @Scheduled(fixedRate = 864000000L, initialDelay = 864000000L)
  @CacheEvict(value = "srm.provinceFindDto", allEntries = true)
  public void clearCache() {
    log.info("清除区域路径查询缓存");
  }

  @Override
  public void patchUpdate(List<LandingContractBundleSaveForm> saveForm, String landingContractId, String userId) {

    if (StrUtil.isBlank(landingContractId)) {
      return;
    }
    // saveForm如果传null，不做处理
    if (saveForm == null) {
      return;
    }
    // 校验数据合理性
    this.checkContract(saveForm, landingContractId);
    // patch更新合同绑品信息
    // 查询合同绑品信息
    List<LandingContractBundle> origin =
        landingContractBundleRepository.findAllByLandingContractIdAndState(landingContractId,
            Constants.STATE_OK);
    // 筛选出新增的
    List<LandingContractBundleSaveForm> newOnes =
        saveForm.stream().filter(item -> StrUtil.isBlank(item.getId()))
            .collect(Collectors.toList());
    // 筛选出更新的
    List<LandingContractBundleSaveForm> updateOnes =
        saveForm.stream().filter(item -> StrUtil.isNotBlank(item.getId()))
            .collect(Collectors.toList());
    // 筛选出删除的 origin中存在，updateOnes中不存在的
    List<LandingContractBundle> deleteOnes = origin.stream().filter(
            item -> updateOnes.stream().noneMatch(update -> update.getId().equals(item.getId())))
        .collect(Collectors.toList());
    // 删除
    if (CollUtil.isNotEmpty(deleteOnes)) {
      deleteOnes.forEach(item -> {
        item.setUpdateTime(System.currentTimeMillis());
        item.setUpdateUser(userId);
        item.setState(Constants.STATE_DELETE);
      });
      landingContractBundleRepository.saveAll(deleteOnes);
      landingContractBundleRepository.flush();
    }
    // 新增
    if (CollUtil.isNotEmpty(newOnes)) {
      List<LandingContractBundle> newBatch = newOnes.stream().map(item -> {
        LandingContractBundle landingContractBundle = MapStructFactory.INSTANCE.toLandingContractBundle(item);
        landingContractBundle.setId(null);
        landingContractBundle.setLandingContractId(landingContractId);
        landingContractBundle.setCreateTime(System.currentTimeMillis());
        landingContractBundle.setUpdateTime(System.currentTimeMillis());
        landingContractBundle.setCreateUser(userId);
        landingContractBundle.setUpdateUser(userId);
        landingContractBundle.setState(Constants.STATE_OK);
        return landingContractBundle;
      }).collect(Collectors.toList());
      landingContractBundleRepository.saveAll(newBatch);
    }
    // 更新
    if (CollUtil.isNotEmpty(updateOnes)) {
      Map<String, LandingContractBundle> originMap =
          origin.stream().collect(Collectors.toMap(LandingContractBundle::getId, item -> item));
      List<LandingContractBundle> updateBatch = updateOnes.stream().map(item -> {
        LandingContractBundle landingContractBundle = originMap.get(item.getId());
        MapStructFactory.INSTANCE.updateLandingContractBundle(item, landingContractBundle);
        landingContractBundle.setUpdateTime(System.currentTimeMillis());
        landingContractBundle.setUpdateUser(userId);
        return landingContractBundle;
      }).collect(Collectors.toList());
      landingContractBundleRepository.saveAll(updateBatch);
    }
  }

  /**
   * 校验合同里不同平台的绑品信息
   */
  private void checkContract(List<LandingContractBundleSaveForm> saveForm, String landingContractId) {
    List<SupplierPerformance> supplierPerformanceList = supplierPerformanceRepository.findAllByLandingContractIdAndState(landingContractId, Constants.STATE_OK);
    if (CollUtil.isEmpty(supplierPerformanceList)) {
      return;
    }
    try {
      // 创建一个包含所有校验任务的CompletableFuture列表
      List<CompletableFuture<Void>> futures = supplierPerformanceList.stream()
          .map(sp -> CompletableFuture.runAsync(
                  () -> this.checkPlatFormBundle(saveForm, landingContractId, sp.getPlatformCode()))
              // 捕获异常并返回该future本身，以便稍后处理
              .exceptionally(ex -> { throw new RuntimeException(ex); })
          )
          .collect(Collectors.toList());

      // 等待第一个完成的future，不管是正常结束还是异常结束
      CompletableFuture<Void> anyOfFuture = CompletableFuture.anyOf(futures.toArray(new CompletableFuture[0]))
          .thenRun(() -> {}); // 转换为Void类型

      // 尝试等待所有任务完成或直到其中一个抛出异常
      try {
        anyOfFuture.join();
      } catch (CompletionException e) {
        // 循环获取cause，如果是checkException则抛出
        //当cause不为null
        Throwable cause = e.getCause();
        while (cause.getCause() != null) {
          cause = cause.getCause();
        }
        // 如果case是checkException
        if (cause instanceof CheckException) {
          throw (CheckException) cause;
        }
        log.info("校验绑品信息失败: {}", cause.getMessage(), e);
        throw new CheckException("校验绑品信息失败: " + cause.getMessage());
      }
      // 如果没有异常，确保所有任务都成功完成
      CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    } finally {

    }
  }

  /**
   * 校验同一个平台下绑品合理性
   * @param saveForm
   */
  private void checkPlatFormBundle(List<LandingContractBundleSaveForm> saveForm,String landingContractId, String platformCode) {
    // 本次保存的平台 --- 根据bundleType分类进行校验
    Map<Byte, List<LandingContractBundleSaveForm>> saveMap =
        saveForm.stream().collect(Collectors.groupingBy(LandingContractBundleSaveForm::getBundleType));
    // 平台的绑品信息 --- 需要过滤掉自己的
    List<LandingContractBundle> platformLandingContractBundle =
        this.getLandingContractBundleByPlatformCode(platformCode).stream().filter(item -> !landingContractId.equals(item.getLandingContractId())).collect(Collectors.toList());
    Map<Byte, List<LandingContractBundle>> platformMap =
        platformLandingContractBundle.stream().collect(Collectors.groupingBy(LandingContractBundle::getBundleType));
    for (Byte bundleType : saveMap.keySet()) {
      List<LandingContractBundleSaveForm> bundleList = saveMap.get(bundleType);
      List<LandingContractBundle> platformBundleList = platformMap.getOrDefault(bundleType, new ArrayList<>());
      // 客户单位绑定
      if (BundleType.CUSTOMER_ORG.getCode().equals(bundleType)) {
        Set<String> customerSet = new HashSet<>();
        // 校验此次提交的内容
        bundleList.forEach(item -> {
          // 校验bundleCustomer是否为空
          if (StrUtil.isBlank(item.getBundleCustomer())) {
            throw new CheckException("绑品客户单位不能为空");
          }
          // 校验bundleCustomer是否重复
          if (!customerSet.add(item.getBundleCustomer())) {
            throw new CheckException("同一个平台下的客户单位绑品不能重复或冲突，请核实此合同内有无重复，或核实此平台下有无其他供应商已设置此客户绑品!");
          }
        });
        // 校验平台的绑品信息
        platformBundleList.forEach(item -> {
          if (customerSet.contains(item.getBundleCustomer())) {
            throw new CheckException("同一个平台下的客户单位绑品不能重复或冲突，请核实此合同内有无重复，或核实此平台下有无其他供应商已设置此客户绑品!");
          }
        });
      }
      // 品牌绑定
      if (BundleType.BRAND.getCode().equals(bundleType)) {
        // 继续根据bundleBrand分类进行校验
        Map<String, List<LandingContractBundleSaveForm>> brandMap =
            bundleList.stream().collect(Collectors.groupingBy(LandingContractBundleSaveForm::getBundleBrand));
        Map<String, List<LandingContractBundle>> platFormbrandMap =
            platformBundleList.stream().collect(Collectors.groupingBy(LandingContractBundle::getBundleBrand));
        for (String bundleBrand : brandMap.keySet()) {
          List<LandingContractBundleSaveForm> brandList = brandMap.get(bundleBrand);
          List<LandingContractBundle> platFormBrandList =
              platFormbrandMap.getOrDefault(bundleBrand, new ArrayList<>());
          // 如果选择了全国，则不能有其他区域
          Set<String> areaSet = new HashSet<>();
          // 校验bundleBrand是否为空
          brandList.forEach(item -> {
            if (StrUtil.isBlank(item.getBundleBrand())) {
              throw new CheckException("绑品品牌不能为空");
            }
            ProvinceFindDto provinceFindDto = this.bfsFindByCodeProxy(item.getBundleArea());
            List<String> pathList = Optional.ofNullable(provinceFindDto).map(ProvinceFindDto::getPath)
                .orElse(new ArrayList<>());
            String path = Optional.of(String.join("/", pathList)).orElse(item.getBundleArea());
            item.setBundleAreaPath(path);
            // path 校验
            areaSet.forEach(area -> {
              if (area.startsWith(path + "/") || path.startsWith(area + "/")) {
                throw new CheckException("同一个平台下的品牌绑品不能重复或冲突，请核实此合同内有无重复，或核实此平台下有无其他供应商已设置此区域的品牌绑品!");
              }
            });
            // 校验区域是否重复
            if (!areaSet.add(path)) {
              throw new CheckException("同一个平台下的品牌绑品不能重复或冲突，请核实此合同内有无重复，或核实此平台下有无其他供应商已设置此区域的品牌绑品!");
            }
          });
          // 校验平台的绑品信息
          platFormBrandList.forEach(item -> {
            ProvinceFindDto provinceFindDto = this.bfsFindByCodeProxy(item.getBundleArea());
            List<String> pathList = Optional.ofNullable(provinceFindDto).map(ProvinceFindDto::getPath)
                .orElse(new ArrayList<>());
            String path = Optional.of(String.join("/", pathList)).orElse(item.getBundleArea());
            // path 校验
            areaSet.forEach(area -> {
              if (area.startsWith(path + "/") || path.startsWith(area + "/")) {
                throw new CheckException("同一个平台下的品牌绑品不能重复或冲突，请核实此合同内有无重复，或核实此平台下有无其他供应商已设置此区域的品牌绑品!");
              }
            });
            // 校验区域是否重复
            if (!areaSet.add(path)) {
              throw new CheckException("同一个平台下的品牌绑品不能重复或冲突，请核实此合同内有无重复，或核实此平台下有无其他供应商已设置此区域的品牌绑品!");
            }
          });
        }
      }
      // 区域绑定
      if (BundleType.AREA.getCode().equals(bundleType)) {
        Set<String> areaSet = new HashSet<>();
        // 如果选择了全国，则不能有其他区域
        bundleList.forEach(item -> {
          if (StrUtil.isBlank(item.getBundleArea())) {
            throw new CheckException("绑品区域不能为空");
          }
          ProvinceFindDto provinceFindDto = this.bfsFindByCodeProxy(item.getBundleArea());
          List<String> pathList = Optional.ofNullable(provinceFindDto).map(ProvinceFindDto::getPath)
              .orElse(new ArrayList<>());
          String path =
              Optional.of(String.join("/", pathList)).orElse(item.getBundleArea());
          item.setBundleAreaPath(path);
          // path 校验
          areaSet.forEach(area -> {
            if (area.startsWith(path + "/") || path.startsWith(area + "/")) {
              throw new CheckException("同一个平台下的区域绑品不能重复或冲突，请核实此合同内有无重复，或核实此平台下有无其他供应商已设置此区域绑品!");
            }
          });
          // 校验bundleArea是否重复
          if (!areaSet.add(path)) {
            throw new CheckException("同一个平台下的区域绑品不能重复或冲突，请核实此合同内有无重复，或核实此平台下有无其他供应商已设置此区域绑品!");
          }
        });
        // 校验平台的绑品信息
        platformBundleList.forEach(item -> {
          ProvinceFindDto provinceFindDto = this.bfsFindByCodeProxy(item.getBundleArea());
          List<String> pathList = Optional.ofNullable(provinceFindDto).map(ProvinceFindDto::getPath)
              .orElse(new ArrayList<>());
          String path =
              Optional.of(String.join("/", pathList)).orElse(item.getBundleArea());
          // path 校验
          areaSet.forEach(area -> {
            if (area.startsWith(path + "/") || path.startsWith(area + "/")) {
              throw new CheckException("同一个平台下的区域绑品不能重复或冲突，请核实此合同内有无重复，或核实此平台下有无其他供应商已设置此区域绑品!");
            }
          });
          // 校验bundleArea是否重复
          if (!areaSet.add(path)) {
            throw new CheckException("同一个平台下的区域绑品不能重复或冲突，请核实此合同内有无重复，或核实此平台下有无其他供应商已设置此区域绑品!");
          }
        });
      }
    }
  }

  @Override
  public List<LandingContractBundle> getLandingContractBundle(List<String> landingContractIds) {
    return this.getLandingContractBundle(landingContractIds, false);
  }

  @Override
  public List<LandingContractBundle> getLandingContractBundle(List<String> landingContractIds, boolean refresh) {
    if (CollUtil.isEmpty(landingContractIds)) {
      return new ArrayList<>();
    }
    List<LandingContractBundle> landingContractBundles =
        landingContractBundleRepository.findAllByLandingContractIdInAndState(landingContractIds, Constants.STATE_OK);
    if (CollUtil.isEmpty(landingContractBundles)) {
      return new ArrayList<>();
    }
    if (!refresh) {
      return landingContractBundles;
    }
    // 过滤出有效的 品牌
    List<String> bundleBrand =
        landingContractBundles.stream()
            .filter(item -> StrUtil.isNotBlank(item.getBundleBrand()))
            .map(LandingContractBundle::getBundleBrand)
            .distinct()
            .collect(Collectors.toList());
    // 筛选出有效的 客户单位
    List<String> bundleCustomer =
        landingContractBundles.stream()
            .filter(item -> StrUtil.isNotBlank(item.getBundleCustomer()))
            .map(LandingContractBundle::getBundleCustomer)
            .distinct()
            .collect(Collectors.toList());
    // 实时调用MPM品牌接口刷新
    if (CollUtil.isNotEmpty(bundleBrand)) {
      try {
        List<CompletableFuture<MdmBrand>> futures = new ArrayList<>();
        // 异步调用
        for (String brandCode : bundleBrand) {
          CompletableFuture<MdmBrand> future = CompletableFuture.supplyAsync(() -> {
            try {
              return mpmService.getMPMBrand(brandCode);
            } catch (Exception e) {
              log.warn("调用MPM品牌接口失败，品牌编码：{}，错误信息：{}", brandCode, e.getMessage());
              return null;
            }
          });
          futures.add(future);
        }
        // 等待所有异步完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        // 获取结果
        List<MdmBrand> mdmBrands =
            futures.stream().map(CompletableFuture::join)
                .filter(Objects::nonNull).collect(Collectors.toList());
        Map<String, MdmBrand> code2Brand =
            mdmBrands.stream().collect(Collectors.toMap(MdmBrand::getCode,
                item -> item));
        // 更新品牌名称
        landingContractBundles.forEach(item -> {
          MdmBrand mdmBrand = code2Brand.get(item.getBundleBrand());
          if (mdmBrand != null) {
            item.setBundleBrandName(mdmBrand.getCnName() + "/" + mdmBrand.getEnName());
          }
        });
      } catch (Exception e) {
        log.info("实时调用MPM品牌接口刷新失败:{}", e.getMessage(), e);
      }
    }
    // 实时调用区域接口刷新
    try {
      // 更新区域名称
      landingContractBundles.forEach(item -> {
        ProvinceFindDto provinceFindDto = bfsFindByCodeProxy(item.getBundleArea());
        if (provinceFindDto != null) {
          item.setBundleAreaName(provinceFindDto.getName());
        }
      });
    } catch (Exception e) {
      log.info("实时调用区域接口刷新失败:{}", e.getMessage(), e);
    }
    // 实时调用客户单位接口刷新
    try {
      List<CompletableFuture<PartnerDTO>> futures = new ArrayList<>();
      // 异步调用
      for (String customerCode : bundleCustomer) {
        CompletableFuture<PartnerDTO> future = CompletableFuture.supplyAsync(() -> {
          try {
            return partnerService.getPartnerByCode(customerCode);
          } catch (Exception e) {
            log.warn("调用客户单位接口失败，客户单位编码：{}，错误信息：{}", customerCode, e.getMessage());
            return null;
          }
        });
        futures.add(future);
      }
      // 等待所有异步完成
      CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
      // 获取结果
      List<PartnerDTO> partnerDTOS =
          futures.stream().map(CompletableFuture::join)
              .filter(Objects::nonNull).collect(Collectors.toList());
      Map<String, PartnerDTO> code2Partner =
          partnerDTOS.stream().collect(Collectors.toMap(PartnerDTO::getMdmCode, item -> item));
      // 更新客户单位名称
      landingContractBundles.forEach(item -> {
        PartnerDTO partnerDTO = code2Partner.get(item.getBundleCustomer());
        if (partnerDTO != null) {
          item.setBundleCustomerName(partnerDTO.getPartnerName());
        }
      });
    } catch (Exception e) {
      log.info("实时调用客户单位接口刷新失败:{}", e.getMessage(), e);
    }
    return landingContractBundles;
  }

  @Override
  public List<LandingContractBundle> getLandingContractBundleByPlatformCode(String platformCode, boolean refresh) {
    // 查询平台下的关联合同
    List<SupplierPerformance> supplierPerformances =
        supplierPerformanceRepository.findAllByPlatformCodeAndState(platformCode,
            Constants.STATE_OK);
    // 筛选出合同id
    List<String> contractIds =
        supplierPerformances.stream().map(SupplierPerformance::getLandingContractId)
            .filter(StrUtil::isNotBlank)
            .distinct().collect(Collectors.toList());
    return this.getLandingContractBundle(contractIds, refresh);
  }

  @Override
  public List<LandingContractBundle> getLandingContractBundleByPlatformCode(String platformCode) {
    return this.getLandingContractBundleByPlatformCode(platformCode, false);
  }

  @Override
  public List<PlatformSupplierBundle> getPlatformSupplierBundle(String platformCode) {
    // 查询平台下的关联合同
    List<SupplierPerformance> supplierPerformances =
        supplierPerformanceRepository.findAllByPlatformCodeAndState(platformCode,
            Constants.STATE_OK);
    // 筛选出合同id
    List<String> contractIds =
        supplierPerformances.stream().map(SupplierPerformance::getLandingContractId)
            .filter(StrUtil::isNotBlank)
            .distinct().collect(Collectors.toList());
    AtomicReference<List<PlatformSupplierBundle>> result = new AtomicReference<>(new ArrayList<>());
    this.batchGetInfo(contractIds, (id2Contract, id2Supplier) -> {
      List<LandingContractBundle> landingContractBundle = this.getLandingContractBundle(contractIds, true);
      // 获取所有updateUser
      List<String> updateUserIds =
          landingContractBundle.stream().map(LandingContractBundle::getUpdateUser)
              .filter(StrUtil::isNotBlank)
              .distinct().collect(Collectors.toList());
      updateUserIds.add("-1");
      List<User> users = userRepository.findAllById(updateUserIds);
      Map<String, User> id2User = users.stream().collect(Collectors.toMap(User::getId, item -> item));
      result.set(landingContractBundle.stream().map(item -> {
        PlatformSupplierBundle platformSupplierBundle = new PlatformSupplierBundle();
        platformSupplierBundle.setCreateTime(item.getCreateTime());
        platformSupplierBundle.setBundleType(item.getBundleType());
        platformSupplierBundle.makeBundleValue(item);
        platformSupplierBundle.setUpdateTime(item.getUpdateTime());
        User user = Optional.ofNullable(id2User.get(item.getUpdateUser())).orElse(new User());
        platformSupplierBundle.setUpdateUser(user.getId());
        platformSupplierBundle.setUpdateUserName(user.getRealName());
        LandingMerchantContract landingMerchantContract =
            Optional.ofNullable(id2Contract.get(item.getLandingContractId()))
                .orElse(new LandingMerchantContract());
        Supplier supplier = Optional.ofNullable(
                id2Supplier.get(landingMerchantContract.getSecondSigningSupplierId()))
            .orElse(new Supplier());
        platformSupplierBundle.setSupplierMdmCode(supplier.getMdmCode());
        platformSupplierBundle.setSupplierName(supplier.getEnterpriseName());
        return platformSupplierBundle;
      }).collect(Collectors.toList()));
    });
    return result.get();
  }

  @Override
  public List<SupplierBundleMatchVO> matchSupplierBundle(SupplierBundleMatchQueryForm form) {
    // 品牌、地区、客户单位不能都为空
    if (StrUtil.isBlank(form.getBundleArea())
        && StrUtil.isBlank(form.getBundleCustomer())
        && StrUtil.isBlank(form.getBundleBrand())) {
      throw new CheckException("您好，请输入需要匹配的绑品信息！");
    }
    Map<String, Object> queryMap = form.toQueryMap(this);
    List<LandingContractBundle> landingContractBundleList =
        landingContractBundleDao.findBySupplierBundleMatchQueryForm(queryMap);
    Map<String, List<LandingContractBundle>> id2LandingContractBundle =
        landingContractBundleList.stream().collect(Collectors.groupingBy(LandingContractBundle::getLandingContractId));
    // 获取所有的供应商id
    List<String> contractIds =
        landingContractBundleList.stream().map(LandingContractBundle::getLandingContractId)
            .distinct().collect(Collectors.toList());
    List<SupplierBundleMatchVO> res = new ArrayList<>();
    this.batchGetInfo(contractIds, (id2Contract, id2Supplier) -> {
      List<LandingMerchantContract> contractList = new ArrayList<>(id2Contract.values());
      // contractList根据 getSecondSigningSupplierId分组
      Map<String, List<LandingMerchantContract>> supplierContractMap =
          contractList.stream().collect(Collectors.groupingBy(LandingMerchantContract::getSecondSigningSupplierId));
      for (String supplierId : supplierContractMap.keySet()) {
        Supplier supplier = id2Supplier.get(supplierId);
        if (supplier == null) {
          continue;
        }
        List<LandingMerchantContract> contracts = supplierContractMap.get(supplierId);
        List<LandingContractBundle> landingContractBundles = new ArrayList<>();
        for (LandingMerchantContract contract : contracts) {
          List<LandingContractBundle> landingContractBundle = id2LandingContractBundle.get(contract.getId());
          if (CollUtil.isNotEmpty(landingContractBundle)) {
            landingContractBundles.addAll(landingContractBundle);
          }
        }
        // 通过合同获取相应的绑品信息
        SupplierBundleMatchVO vo = new SupplierBundleMatchVO();
        vo.setQueryForm(form);
        vo.setSupplierMdmCode(supplier.getMdmCode());
        vo.setSupplierName(supplier.getEnterpriseName());
        vo.makeBundleList(landingContractBundles, queryMap);
        vo.makeMatchScore();
        res.add(vo);
      }
      // 重新根据matchScore排序
      res.sort((o1, o2) -> o2.getMatchScore().compareTo(o1.getMatchScore()));
    });
    return res;
  }

  @Override
  public List<SupplierBundleMatchBatchVO> matchSupplierBundleBatch(List<SupplierBundleMatchQueryForm> forms) {
    for (SupplierBundleMatchQueryForm form : forms) {
      // 品牌、地区、客户单位不能都为空
      if (StrUtil.isBlank(form.getBundleArea())
          && StrUtil.isBlank(form.getBundleCustomer())
          && StrUtil.isBlank(form.getBundleBrand())) {
        throw new CheckException("您好，请输入需要匹配的绑品信息！");
      }
    }
    List<SupplierBundleMatchBatchVO> res = new ArrayList<>();
    if (CollUtil.isEmpty(forms)) {
      return new ArrayList<>();
    }
    // 异步批量匹配
    List<CompletableFuture<List<SupplierBundleMatchVO>>> futures = new ArrayList<>();
    for (SupplierBundleMatchQueryForm form : forms) {
      CompletableFuture<List<SupplierBundleMatchVO>> future = CompletableFuture.supplyAsync(() -> {
        try {
          return this.matchSupplierBundle(form);
        } catch (Exception e) {
          log.error("批量匹配供应商绑品失败:{}", e.getMessage(), e);
          return new ArrayList<>();
        }
      });
      futures.add(future);
    }
    // 等待所有异步完成
    CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    // 按forms的顺序获取结果
    for (int i = 0; i < forms.size(); i++) {
      SupplierBundleMatchQueryForm form = forms.get(i);
      List<SupplierBundleMatchVO> matchVOS = futures.get(i).join();
      SupplierBundleMatchBatchVO batchVO = new SupplierBundleMatchBatchVO();
      batchVO.setQueryForm(form);
      batchVO.setResult(matchVOS);
      res.add(batchVO);
    }
    return res;
  }

  @Override
  public void batchGetInfo(List<String> landingContractIds, LandingContractBundleBatchGetHandler handler) {
    if (CollUtil.isEmpty(landingContractIds)) {
      return;
    }
    List<LandingMerchantContract> contractList =
        landingMerchantContractRepository.findAllById(landingContractIds);
    Map<String, LandingMerchantContract> id2Contract = contractList.stream().collect(
        Collectors.toMap(LandingMerchantContract::getId,
            Function.identity(), (o1, o2) -> o1));
    // 获取供应商信息
    List<String> supplierIds =
        contractList.stream().map(LandingMerchantContract::getSecondSigningSupplierId)
            .collect(Collectors.toList());
    supplierIds.add("-1");
    List<Supplier> suppliers = supplierRepository.findAllById(supplierIds);
    Map<String, Supplier> id2Supplier =
        suppliers.stream().collect(Collectors.toMap(Supplier::getId, Function.identity(), (o1, o2) -> o1));
    handler.handler(id2Contract, id2Supplier);
  }
}
