package com.xhgj.srm.service;

import com.xhgj.srm.common.dto.invoice.InvoiceOcrRecognitionResultDTO;
import com.xhgj.srm.common.enums.InvoiceTypeEnum;
import com.xhgj.srm.dto.inputInvoice.InputInvoiceOrderWithSupplierOrder;
import com.xhgj.srm.dto.InputInvoiceOrderWithDetail;
import com.xhgj.srm.handle.SupplierInvoiceToDetailBatchGetHandler;
import com.xhgj.srm.jpa.entity.InputInvoiceOrder;
import com.xhgj.srm.jpa.entity.OrderSupplierInvoice;
import com.xhgj.srm.jpa.entity.SupplierInvoiceToDetail;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

public interface ShareInputInvoiceService extends BootBaseService<InputInvoiceOrder, String> {

  /**
   * @param orderInvoiceRelation 订单发票单
   * @return 获取第一次开票时间
   */
  Optional<Long> getFirstOpenInvoiceDate(InputInvoiceOrder orderInvoiceRelation);

  /**
   * @param orderInvoiceRelation 订房发票单
   * @return 获取发票类型集合
   */
  List<InvoiceTypeEnum> getInvoiceTypeEnums(InputInvoiceOrder orderInvoiceRelation);

  /**
   * @return 此行发票单中所有发票的去税金额之和
   */
  BigDecimal getTotalAmountOfTaxDeduction(InputInvoiceOrder orderInvoiceRelation);

  /**
   * 根据进项票id查询相关的供应商订单
   */
  List<InputInvoiceOrderWithSupplierOrder> getSupplierOrderByInputInvoiceIds(List<String> inputInvoiceIds);

  /**
   * 根据进项票id删除相关的分割关系
   */
  void deleteSupplierOrderByInputInvoiceIds(List<String> inputInvoiceIds);

  /**
   * 保存 + 保存分割关系
   */
  void saveAndSplit(InputInvoiceOrder orderInvoiceRelation);

  /**
   * 根据采购订单关联信息，获取相关采购订单信息
   */
  void batchGetSupplierInvoiceToDetailLink(List<SupplierInvoiceToDetail> list, SupplierInvoiceToDetailBatchGetHandler handler);

  /**
   * 进项票删除 统一方法
   */
  void deleteInputInvoiceOrder(String orderInvoiceRelationId);

  /**
   * 进项票删除关联履约订单
   */
  void deleteInputInvoiceLinkOrder(String orderInvoiceRelationId);

  /**
   * 删除发票关联订单明细
   */
  void deleteInputInvoiceLinkSupplierOrder(String orderInvoiceRelationId);

  /**
   * 删除发票单明细
   */
  void deleteInputInvoiceLinkInvoice(String orderInvoiceRelationId);

  /**
   * 获取发票关联订单明细(根据detailId去重)
   */
  List<SupplierInvoiceToDetail> getSupplierInvoiceToDetailDistinct(String orderInvoiceRelationId);

  /**
   * 获取关联发票明细 根据 InvoiceNum 去重
   * @param orderInvoiceRelationId
   * @return
   */
  List<OrderSupplierInvoice> getOrderSupplierInvoiceDistinct(String orderInvoiceRelationId);

  /**
   * 根据采购订单明细id查询进项票列表(使用新字段进行关联，效率更高)
   */
  List<InputInvoiceOrderWithDetail> getOrderInvoiceRelationListByDetailIdsRef(List<String> supplierOrderDetailIds);

  /**
   * 根据订单号查询ocr识别结果
   * @param invoiceNum
   * @return
   */
  InvoiceOcrRecognitionResultDTO getOcrRecognitionResult(String invoiceNum);
}


