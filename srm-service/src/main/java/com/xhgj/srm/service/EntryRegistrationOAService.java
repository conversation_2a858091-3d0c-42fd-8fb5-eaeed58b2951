package com.xhgj.srm.service;


import com.xhgj.srm.dto.entryregistration.ApprovalResultCallbackParam;
import com.xhgj.srm.dto.entryregistration.EntryRegistrationDetail;
import com.xhgj.srm.jpa.entity.EntryRegistrationOrder;
import com.xhiot.boot.framework.jpa.service.BootBaseService;

public interface EntryRegistrationOAService extends BootBaseService<EntryRegistrationOrder, String> {

  Boolean approvalResultCallback(ApprovalResultCallbackParam param);

  /**
   * 入驻报备单供 H5 详情
   * @param id
   * @return
   */
  EntryRegistrationDetail settlementRegistrationFormForH5Details(String id);
}
