package com.xhgj.srm.service;/**
 * @since 2025/3/17 15:33
 */

import com.xhgj.srm.common.dto.OmsOrderReceiptDto;
import com.xhgj.srm.common.dto.OmsOrderReceiptTestDto;
import com.xhgj.srm.jpa.entity.Order;
import com.xhgj.srm.jpa.entity.OrderReceiptRecord;
import java.util.List;

/**
 *<AUTHOR>
 *@date 2025/3/17 15:33:48
 *@description 履约回款记录业务方法
 */
public interface OrderReceiptRecordService {

  /**
   * 保存履约回款记录
   * todo 更新或创建新 需付账款
   */
  void saveOrderReceiptRecord(OmsOrderReceiptDto form, Order order);

  /**
   * 根据ids 查询履约回款记录
   * todo 需付账款批量查询需要用到
   */
  List<OrderReceiptRecord> findByIds(List<String> ids);

  /**
   * 根据履约订单ids 查询履约回款记录
   * todo 履约订单列表、详情、可付款订单可以用到
   */
  List<OrderReceiptRecord> findByOrderIds(List<String> ids);

  /**
   * 根据履约订单id 查询回款方式
   * @param orderId
   * @return 去重且转换成SAP付款方式名称
   */
  List<String> findPaymentMethodsByOrderId(String orderId);

  /**
   * 回款数据生成--测试使用
   */
  void testData(OmsOrderReceiptTestDto dto, String orderId);
}
