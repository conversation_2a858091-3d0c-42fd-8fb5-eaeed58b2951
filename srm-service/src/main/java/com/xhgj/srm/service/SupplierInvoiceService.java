package com.xhgj.srm.service;

import com.xhgj.srm.common.dto.invoice.BatchUpdateSupplierInvoiceParam;
import com.xhgj.srm.common.dto.invoice.InvoiceIdentifyResultDTO;
import com.xhgj.srm.dto.order.invoice.InvoiceParam;
import com.xhgj.srm.dto.order.invoice.InvoiceVerificationResult;
import com.xhgj.srm.dto.order.invoice.OtherInvoiceParam;
import com.xhgj.srm.dto.order.invoice.SupplierInvoiceDetailsVO;
import com.xhgj.srm.jpa.dto.AddResultDTO;
import com.xhgj.srm.jpa.dto.inputInvoice.InvoiceAddBatchForm;
import com.xhgj.srm.jpa.dto.MateResultDTO;
import com.xhgj.srm.jpa.dto.OrderMateDTO;
import com.xhgj.srm.jpa.dto.OrderProductDetailInfoDTO;
import com.xhgj.srm.jpa.dto.ProductDetailInfoDTO;
import com.xhgj.srm.jpa.dto.inputInvoice.InputInvoiceManualAddForm;
import com.xhgj.srm.jpa.entity.InvoiceVerification;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Created by Geng Shy on 2023/9/7
 */
public interface SupplierInvoiceService {

  /**
   * 发票验真
   */
  InvoiceVerificationResult invoiceVerification(InvoiceParam invoiceParam);
  /**
   * 发票识别提取
   */
  List<InvoiceIdentifyResultDTO> batchInvoiceIdentify(List<String> fileUrls);

  /**
   * 更新发票验真结果
   */
  boolean batchUpdateInvoiceVerification(BatchUpdateSupplierInvoiceParam param);

  /**
   * 查询验真结果
   * @param invoiceType 发票类型
   * @param invoiceNum  发票号码
   * @param invoiceCode 发票代码
   * @param invoiceTime 开票时间
   * @param totalAmount 总计金额
   * @param totalTaxAmount  总计税额
   * @param totalAmountIncludingTax 价税合计
   * @param checkCode 校验码
   * @param effective 是否验真成功
   * @return  验真结果
   */
  Optional<InvoiceVerification> getInvoiceVerification(String invoiceType,
      String invoiceNum, String invoiceCode, Long invoiceTime, BigDecimal totalAmount,
      BigDecimal totalTaxAmount, BigDecimal totalAmountIncludingTax, String checkCode, String
      effective);

  /**
   * 根据发票号 匹配订单信息
   * @param invoiceNumber 发票号
   * @param supplierName 供应商
   * @param orderIds 订单id
   * @param manualTotalAmountWithTax 手工录票价税合计
   * @return
   */
  List<OrderMateDTO> orderMate(String userGroup, String invoiceNumber,String supplierName,
      List<String> orderIds, BigDecimal manualTotalAmountWithTax, Boolean historyOrder);

  /**
   * 根据发票号 匹配入库单信息
   */
  List<OrderMateDTO> warehousingMate(String userGroup, String invoiceNumber, String supplierName,
      BigDecimal manualTotalAmountWithTax, Boolean historyOrder);

  /**
   * 物料明细信息
   * @param supplierName
   * @return
   */
  List<ProductDetailInfoDTO> productDetailInfo(String userGroup,String supplierName,
      String productName,String model,
      BigDecimal num,BigDecimal ratePrice,String orderNo,String productVoucher, Boolean historyOrder);

  /**
   * 订单明细
   * @param id
   * @return
   */
  List<OrderProductDetailInfoDTO> getSupplierOrderDetail(String id);

  /**
   * 获取入库单明细
   * @param id
   * @return
   */
  List<OrderProductDetailInfoDTO> getWarehousingOrderDetail(String id);

  /**
   * 获取关联订单匹配结果
   * @param invoiceNumber
   * @param supplierName
   * @return
   */
  MateResultDTO mateResult(String userGroup, String invoiceNumber, String supplierName,
      BigDecimal manualTotalAmountWithTax, Boolean historyOrder);

  /**
   * 保存/提交进项票 后台批量提交
   * @param params
   */
  List<AddResultDTO> ocrInvoiceAddBatch(InvoiceAddBatchForm params);

  /**
   * 进项票 手工票录入
   */
  List<AddResultDTO> manualInvoiceAdd(InputInvoiceManualAddForm form);

  /**
   * 发票单详情
   * @param id 发票单id
   */
  SupplierInvoiceDetailsVO getDetail(String id);

  /**
   * 设置采购单供应商开票状态
   * @param supplierOrderId 采购单id
   */
  void setSupplierOpenInvoiceState(String supplierOrderId);

  /**
   * 批量设置采购单供应商开票状态
   */
  void batchSetSupplierOpenInvoiceState(List<String> supplierOrderId);

  /**
   * 关联明细
   * @param orderIdList
   * @param orderToFormIdList
   * @return
   */
  List<ProductDetailInfoDTO> getAssociationProductDetail(List<String> orderIdList,
      List<String> orderToFormIdList, String inputInvoiceId);

  /**
   * 其他发票验真
   */
  InvoiceVerificationResult invoiceOtherVerification(OtherInvoiceParam param);

  /**
   * @description: 已退库物料列表
   * @param: supplierName 供应商名称
   * @param: productName 物料名称
   * @param: productCode 物料编码
   * @param: invoiceNums 关联发票号
   **/
  List<ProductDetailInfoDTO>  returnedMaterialsList(String supplierName, String productName,
      String productCode, String invoiceNums, Boolean historyOrder);

  /**
   * 导出进项票关联订单详情
   * @param inputInvoiceId
   * @return
   */
  Map<String,Object> exportLinkOrderDetail(String inputInvoiceId);
}
