package com.xhgj.srm.service;

import com.xhgj.srm.dto.supplier.OrderSupplierDTO;
import com.xhgj.srm.dto.supplier.SupplierStatusDTO;
import com.xhgj.srm.jpa.entity.Group;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierInGroup;
import com.xhgj.srm.request.dto.partner.PartnerDTO;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import com.xhiot.boot.mvc.base.PageResult;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * Created by Geng Shy on 2023/10/25
 */
public interface ShareSupplierService extends BootBaseService<Supplier, String> {

  Optional<Supplier> findByName(String supplierName);

  /** 更新供应商完善度 */
  void updateSupplierScore(String supplierInGroupId);
  /** 更新供应商完善度 */
  void updateSupplierScoreBySupplierInGroupId(String supplierInGroupId);
  void updateSupplierScoreBySupplierId(String supplierId);

  /**
   * 获取供应商资料完整度
   */
  Integer getSupplierIntegrity(SupplierInGroup supplierInGroup);

  /**
   * 获取供应商各平台的履约信息可接单状态
   */
  PageResult<SupplierStatusDTO> getOrderSupplierPage(String enterpriseName, String platform, String status,
      Long effectiveStart, Long effectiveEnd, Integer pageNo, Integer pageSize);

  /**
   * 获取派单供应商列表
   * @param enterpriseName
   * @return
   */
  List<OrderSupplierDTO> getOrderSupplierList(String enterpriseName);


  /**
   * 获取供应商各平台的履约信息可接单状态
   * @param enterpriseName
   * @param platform
   * @return
   */
  List<SupplierStatusDTO> getOrderSupplierList(String enterpriseName,String platform);

  /**
   * 获取供应履约金额
   * @param supplierId
   * @return
   */
  BigDecimal getSupplierOrderPrice(String supplierId,String platform);

  /**
   * 创建供应商，并同步至MDM
   */
  String createSupplier(Supplier supplier, Group wanJuGroup);


  PartnerDTO getPartnerDTO(String supplierName, String supplierType);
}
