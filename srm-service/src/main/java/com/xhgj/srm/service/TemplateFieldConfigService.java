package com.xhgj.srm.service;

import com.xhgj.srm.dto.filedConfig.ProcurementFieldConfigDTO;
import com.xhgj.srm.dto.filedConfig.ProcurementFieldDTO;
import com.xhgj.srm.dto.filedConfig.TitleFieldListDTO;
import com.xhgj.srm.dto.filedConfig.UpdateProcurementShipperDTO;
import com.xhgj.srm.dto.filedConfig.purchaseOrderV2.ButtonConfigParam;
import com.xhgj.srm.dto.filedConfig.purchaseOrderV2.ButtonTypeConfigDto;
import com.xhgj.srm.dto.filedConfig.purchaseOrderV2.ButtonTypeConfigItemDto;
import com.xhgj.srm.dto.filedConfig.purchaseOrderV2.ConfigItemDto;
import com.xhgj.srm.dto.filedConfig.purchaseOrderV2.FieldDto;
import com.xhgj.srm.dto.filedConfig.purchaseOrderV2.OrderTypeFieldDto;
import com.xhgj.srm.dto.filedConfig.purchaseOrderV2.PurchaseOrderConfigVo;
import com.xhgj.srm.jpa.entity.TemplateFieldConfig;
import com.xhgj.srm.v2.form.PurchaseOrderAddV2Form;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import java.util.List;
import java.util.Map;
import org.springframework.web.multipart.MultipartFile;

public interface TemplateFieldConfigService extends
    BootBaseService<TemplateFieldConfig, String> {

  /**
   * 获取采购段配置模板
   * @return List<ProcurementFieldConfigDTO>
   */
    List<ProcurementFieldConfigDTO> findConfigListByBigType(String bigType);

  /**
   * 获取采购申请模板详情,当 id 不传时是获取模板
   * @param id
   * @return
   */
  ProcurementFieldDTO getTemplateFieldInfo(String key, String type);

  /**
   * 根据使用组织编码获取配置字段
   * @param code 使用组织编码
   * @return Map<String, String>
   */
  Map<String, String> buildFieldMap(String code);
  /**
   * 修改采购申请字段作用组织
   */
  void updateProcurementShipper(UpdateProcurementShipperDTO dto);

  /**
   * 修改配置模版
   */
  TemplateFieldConfig saveOrUpdate(String id,String name,String remark,String textJson,String bigType);

  void deleteTemplate(List<String> ids);


  /**
   * 获取采购订单模板详情,无id时获取默认模板
   * @param id
   * @return
   */
  PurchaseOrderConfigVo purchaseOrderV2Detail(String id);

  /**
   * 采购订单配置数据初始化  全量替换
   * @param file
   */
  void purchaseOrderV2FieldInit(MultipartFile file);

  /**
   * 采购订单配置  - 根据使用组织、订单类型获取配置字段
   * @param userCode
   * @param orderType
   * @return
   */
  List<FieldDto> getPurchaseOrderV2FieldConfig(String userCode, String orderType);

  /**
   * 采购订单配置  - 根据使用组织、订单类型、实体字段名获取指定配置字段
   * @param userGroup
   * @param orderType
   * @param entityFieldName
   * @return
   */
  FieldDto getPurchaseOrderV2FieldConfig(String userGroup, String orderType, String entityFieldName);
  /**
   * 采购订单配置 - 字段是否展示
   * 2-根据场景 返回 false
   * 使用时需注意 2-根据场景 时的处理
   */
  boolean isFieldShow(String userGroup, String orderType, String entityFieldName);

  /**
   * 采购订单配置 - 字段必填校验
   */
  void checkFieldRequired(String userGroup, String orderType, PurchaseOrderAddV2Form form);

  /**
   * 根据组织获取采购订单字段配置
   * @param orgCode
   * @return
   */
  OrderTypeFieldDto orderFieldByOrg(String orgCode, String orderType);

  /**
   * 根据组织获取采购订单列表字段配置
   * @param orgCode
   * @return
   */
  List<TitleFieldListDTO> listFieldByOrg(String orgCode);

  /**
   * 根据组织获取新增采购订单弹窗
   * @param orgCode
   * @return
   */
  List<OrderTypeFieldDto> orderPopByOrg(String orgCode);

  /**
   * 修改采购订单配置模版
   */
  TemplateFieldConfig saveOrUpdate(String id, String name, String remark, ConfigItemDto configItemDto);

  /**
   * 根据组织和订单类型校验订单是否可以下推
   * @param orgCode
   * @param orderType
   */
  void checkPush(String orgCode, String orderType);

  /**
   * 修改采购订单按钮配置模版
   */
  TemplateFieldConfig saveOrUpdate(String id, String name, String remark, ButtonTypeConfigDto buttonTypeConfigDto);

  /**
   * 获取采购订单按钮配置
   * @param id
   * @return
   */
  ButtonConfigParam purchaseOrderV2ButtonDetail(String id);

  /**
   * 采购订单按钮配置数据初始化  全量替换
   */
  void purchaseOrderV2ButtonFieldInit(MultipartFile file);

  /**
   * 根据组织和订单类型获取采购订单按钮配置
   * @param orgCode
   * @param orderType
   * @return
   */
  ButtonTypeConfigItemDto buttonFieldByOrg(String orgCode, String orderType);
}
