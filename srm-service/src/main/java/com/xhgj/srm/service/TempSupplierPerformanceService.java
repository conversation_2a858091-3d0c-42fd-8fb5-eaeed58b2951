package com.xhgj.srm.service;

import com.xhgj.srm.jpa.entity.SupplierPerformance;
import com.xhiot.boot.framework.jpa.service.BootBaseService;

public interface TempSupplierPerformanceService extends BootBaseService<SupplierPerformance, String> {

  /**
   * 根据供应商和平台类型获取负责采购
   * @param platformCode 平台编码
   * @param supplierId 供应商 id
   * @return
   */
  String getSupplierPerformanceDockingPurchaseErpCode(String platformCode, String supplierId);
}
