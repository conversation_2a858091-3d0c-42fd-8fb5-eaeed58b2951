package com.xhgj.srm.service;

import com.xhgj.srm.jpa.entity.OrderDetail;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName TempOrderDetailService
 **/
public interface TempOrderDetailService extends BootBaseService<OrderDetail,String> {
  /**
   * 根据订单id获取订单详情列表
   * @param orderId 订单id
   * @return
   */
  List<OrderDetail> getOrderDetailByOrderId(String orderId);
}
