package com.xhgj.srm.service;

import com.xhgj.srm.jpa.entity.EntryRegistrationOrder;
import com.xhgj.srm.jpa.entity.Supplier;

public interface ShareContactService {

  void addContactIfNotExist(String contacts, String supplierId, String mobile, String userId, String groupId);


  /**
   * 根据报备单信息添加供应商联系人
   * @param entryRegistrationOrder 报备单信息
   * @param supplier 供应商信息
   * @param supplierInGroupId   组织内供应商id
   */
  void addContactByEntryRegistrationOrder(EntryRegistrationOrder entryRegistrationOrder,
      Supplier supplier, String supplierInGroupId);
}
