package com.xhgj.srm.factory;
import java.math.BigDecimal;
import java.math.RoundingMode; /**
 * @since 2025/3/19 14:49
 */
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.enums.order.OrderCancelStatus;
import com.xhgj.srm.dto.returned.OrderReturnDetailAddParam;
import com.xhgj.srm.jpa.entity.Order;
import com.xhgj.srm.jpa.entity.OrderCancel;
import com.xhgj.srm.jpa.entity.OrderCancelDetail;
import com.xhgj.srm.jpa.entity.OrderDetail;
import com.xhgj.srm.jpa.repository.OrderCancelRepository;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;

/**
 *<AUTHOR>
 *@date 2025/3/19 14:49:02
 *@description
 */
@Component
public class OrderCancelFactory {

  private static final String ORDER_CANCEL_NO = "QX{}-{}";
  @Resource
  OrderCancelRepository orderCancelRepository;

  /**
   * 创建订单取消对象
   * @return
   */
  public OrderCancel createOrderCancel(Order order) {
    OrderCancel orderCancel = new OrderCancel();
    orderCancel.setOrderId(order.getId());
    Integer index = orderCancelRepository.countAllByOrderIdAndState(order.getId(), Constants.STATE_OK);
    String orderNo = order.getOrderNo();
    orderCancel.setCancelNo(StrUtil.format(ORDER_CANCEL_NO, index + 1, orderNo));
    orderCancel.setCreateTime(System.currentTimeMillis());
    orderCancel.setPrice(BigDecimal.ZERO);
    orderCancel.setNum(BigDecimal.ZERO);
    orderCancel.setState(Constants.STATE_OK);
    orderCancel.setStatus(OrderCancelStatus.CANCEL_FAIL.getCode());
    orderCancel.setErpNo(null);
    orderCancel.setPurchaseOrderNo(null);
    return orderCancel;
  }

  /**
   * 创建订单取消明细对象
   * @param orderCancel
   * @param order
   * @param orderDetail
   * @param actualCancelNum
   * @return
   */
  public OrderCancelDetail createDetail(OrderCancel orderCancel, Order order,
      OrderDetail orderDetail, BigDecimal actualCancelNum, OrderReturnDetailAddParam cancelOne) {
    OrderCancelDetail cancelDetail = new OrderCancelDetail();
    cancelDetail.setCancelId(orderCancel.getId());
    cancelDetail.setOrderId(order.getId());
    cancelDetail.setBrand(
        StrUtil.emptyToDefault(cancelOne.getBrand(), orderDetail.getBrand()));
    cancelDetail.setCode(
        StrUtil.emptyToDefault(cancelOne.getCode(), orderDetail.getCode()));
    cancelDetail.setModel(
        StrUtil.emptyToDefault(cancelOne.getModel(), orderDetail.getModel()));
    cancelDetail.setUnit(
        StrUtil.emptyToDefault(cancelOne.getUnit(), orderDetail.getUnit()));
    cancelDetail.setName(
        StrUtil.emptyToDefault(cancelOne.getName(), orderDetail.getName()));
    cancelDetail.setPrice(orderDetail.getPrice());
    cancelDetail.setNum(actualCancelNum);
    cancelDetail.setErpDetailRowId(null);
    cancelDetail.setCreateTime(System.currentTimeMillis());
    cancelDetail.setOrderDetailId(orderDetail.getId());
    cancelDetail.setState(Constants.STATE_OK);
    return cancelDetail;
  }

  public Order updateOrder(Order order, OrderDetail orderDetail, BigDecimal num) {
    BigDecimal subTotal = this.getCancelPrice(orderDetail, num);
    //设置订单取消金额
    order.setCancelPrice(order.getCancelPrice().add(subTotal));
    return order;
  }

  public BigDecimal getCancelPrice(OrderDetail orderDetail, BigDecimal cancelNum) {
    //成本价税率
    BigDecimal costPriceTaxRate = Convert.toBigDecimal(orderDetail.getCostPriceTaxRate());
    if (costPriceTaxRate == null) {
      costPriceTaxRate = new BigDecimal("0.13");
    }
    //税额
    BigDecimal taxAmount =
        NumberUtil.div(orderDetail.getPrice(), BigDecimal.ONE.add(costPriceTaxRate))
            .multiply(cancelNum).multiply(costPriceTaxRate).setScale(2, RoundingMode.HALF_UP);
    //不含税金额
    BigDecimal amountExcludingTax =
        NumberUtil.div(orderDetail.getPrice(), BigDecimal.ONE.add(costPriceTaxRate))
            .multiply(cancelNum).setScale(2, RoundingMode.HALF_UP);
    //单种商品总退货金额
    return NumberUtil.add(taxAmount, amountExcludingTax);
  }
}
