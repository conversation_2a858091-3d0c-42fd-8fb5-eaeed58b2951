package com.xhgj.srm.api.dto.supplierUser;

import com.xhgj.srm.api.dto.FileDTO;
import com.xhgj.srm.api.dto.SupplierUserDataDTO;
import com.xhgj.srm.jpa.entity.Supplier;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class SupplierDataInUserV2DTO {

    @ApiModelProperty("供应商 id")
    private String id;
    @ApiModelProperty("供应商名称")
    private String enterName;
    @ApiModelProperty("统一社会信用代")
    private String uscc;
    @ApiModelProperty("法人")
    private String corporate;
    @ApiModelProperty("合作类型")
    private String cooperateType;
    @ApiModelProperty("账号信息")
    private List<SupplierUserDataDTO> supplierUserDataDTOList;
    @ApiModelProperty("落地商菜单集合")
    private List<String> menuIds;
    @ApiModelProperty("营业执照")
    private String license;
    @ApiModelProperty("营业执照")
    private List<FileDTO> licenseFile;



    public SupplierDataInUserV2DTO(Supplier supplier, List<String> menuIds) {
        this.id = supplier.getId();
        this.enterName = supplier.getEnterpriseName();
        this.uscc = supplier.getUscc();
        this.cooperateType = supplier.getCooperateType();
        this.menuIds = menuIds;
    }

}
