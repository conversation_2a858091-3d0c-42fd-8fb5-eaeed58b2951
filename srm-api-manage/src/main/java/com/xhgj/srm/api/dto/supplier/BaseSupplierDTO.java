package com.xhgj.srm.api.dto.supplier;

import cn.hutool.core.annotation.Alias;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.api.enums.SupplierRelativeKeyType;
import com.xhgj.srm.jpa.entity.BaseSupplierInGroup;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierInGroup;
import com.xhgj.srm.jpa.entity.SupplierInGroupTemp;
import com.xhgj.srm.request.dto.partner.PartnerIcpDTO;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import java.util.Objects;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/7/11 15:45
 */
@Data
@NoArgsConstructor
public abstract class BaseSupplierDTO {
  @ApiModelProperty("组织内供应商 id，新增时不传，编辑时必传")
  private String id;

  @ApiModelProperty("供应商 id")
  private String supplierId;

  @ApiModelProperty("关联 key，以搜索结果新增时必传。仅传入")
  private String relativeKey;

  @ApiModelProperty("关联 key 的类型，以搜索结果新增时必传。仅传入")
  private SupplierRelativeKeyType relativeKeyType;

  /**
   * 最长250字符
   */
  @ApiModelProperty(value = "企业名称", required = true)
  @NotBlank(message = "企业名称必填！")
  @Alias("enterpriseName")
  @Size(max = 250, message = "企业名称最长250字符！")
  private String name;

  @ApiModelProperty("支付方式,默认付款方式")
  private String payType;

  @ApiModelProperty("其他付款方式")
  private String payTypeOther;

  @ApiModelProperty(value = "供应商等级", required = true)
  @NotBlank(message = "供应商等级必填！")
  private String enterpriseLevel;

  // ==================== 财务信息
  @ApiModelProperty("发票类型")
  private String invoiceType;

  @ApiModelProperty("默认税率")
  private String taxRate;

  @ApiModelProperty("结算币别")
  private String settleCurrency;

  @ApiModelProperty("财务信息列表")
  private List<SupplierFinancialDTO> financials;

  // ==================== 联系人
  @ApiModelProperty("联系人列表")
  @Valid
  private List<SupplierContactDTO> contacts;

  @ApiModelProperty("负责人描述")
  private String chargeManDesc;

  @ApiModelProperty("供应商处于新增审核中")
  private Boolean assessing;

  @ApiModelProperty("当前登录人是否拥有修改供应商权限")
  private Boolean hasUpdatePermission;

  @ApiModelProperty("拉黑原因")
  private String blockReason;

  @ApiModelProperty("原始数据 id")
  private String targetId;

  @ApiModelProperty("申请详情url")
  private String applicationDetailUrl;

  @ApiModelProperty("评估表")
  private SupplierFileDTO evaluationTable;

  @ApiModelProperty(value = "网址备案信息")
  private List<PartnerIcpDTO> websiteRegistrationList;

  @ApiModelProperty(value = "网络备案时间(格式：2025-04-14 20:30:59)非必填")
  private String websiteRegistrationSyncTime;

  public BaseSupplierDTO(
      BaseSupplierInGroup supplierInGroup,
      List<SupplierFinancialDTO> financials,
      List<SupplierContactDTO> contacts,
      String chargeManDesc,
      Boolean hasUpdatePermission,
      SupplierFileDTO evaluationTable) {
    this.id = supplierInGroup.getId();
    this.supplierId = supplierInGroup.getSupplierId();
    Supplier supplier = supplierInGroup.getSupplier();
    this.name = supplier.getEnterpriseName();
    if (supplierInGroup instanceof SupplierInGroupTemp) {
      // 这里如果supplierInGroup是SupplierInGroupTemp类型，那么就取SupplierInGroupTemp的payType和payTypeOther展示审核页面修改的数据
      this.payType = ((SupplierInGroupTemp) supplierInGroup).getPayType();
      this.payTypeOther = ((SupplierInGroupTemp) supplierInGroup).getPayTypeOther();
    }else{
      this.payType = supplier.getPayType();
      this.payTypeOther = supplier.getPayTypeOther();
    }
    this.enterpriseLevel = supplierInGroup.getEnterpriseLevel();
    this.invoiceType = supplierInGroup.getInvoiceType();
    this.taxRate = supplierInGroup.getTaxRate();
    this.settleCurrency = supplierInGroup.getSettleCurrency();
    this.financials = financials;
    this.contacts = contacts;
    this.chargeManDesc = chargeManDesc;
    if (supplierInGroup instanceof SupplierInGroup) {
      this.assessing =
          Objects.equals(supplier.getState(), Constants.COMMONSTATE_CHECKING)
              || Objects.equals(
                  ((SupplierInGroup) supplierInGroup).getState(), Constants.COMMONSTATE_CHECKING);
      this.blockReason = StrUtil.emptyIfNull(((SupplierInGroup) supplierInGroup).getReason());
    } else {
      this.assessing = Boolean.TRUE;
      this.blockReason = StrUtil.EMPTY;
    }
    this.hasUpdatePermission = hasUpdatePermission;
    this.evaluationTable = Objects.nonNull(evaluationTable) ? evaluationTable : null;
    String websiteRegistration  = supplier.getWebsiteRegistration();
    this.websiteRegistrationSyncTime = supplier.getWebsiteRegistrationSyncTime();
    if(StrUtil.isNotEmpty(websiteRegistration)){
      try {
        ObjectMapper mapper = new ObjectMapper();
        this.websiteRegistrationList  =  new ObjectMapper().readValue(
            websiteRegistration,
            mapper.getTypeFactory().constructCollectionType(List.class, PartnerIcpDTO.class)
        );
      }catch (Exception e){
        e.printStackTrace();
      }
    }
  }
}
