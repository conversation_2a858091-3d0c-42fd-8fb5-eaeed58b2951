package com.xhgj.srm.api.dto.financial.voucher;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.enums.VoucherTypeEnum;
import com.xhgj.srm.common.utils.supplierorder.BigDecimalUtil;
import com.xhgj.srm.jpa.entity.FinancialVoucher;
import com.xhgj.srm.jpa.entity.Group;
import com.xhgj.srm.jpa.entity.InputInvoiceOrder;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierOrder;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class FinancialVoucherPageVO {

  @ApiModelProperty("id")
  private String id;

  @ApiModelProperty("财务凭证号")
  private String financialVoucherNo;

  /**
   * SAP会计年度。
   */
  @ApiModelProperty("SAP会计年度")
  private String accountingYear;

  /**
   * {@link com.xhgj.srm.common.enums.VoucherTypeEnum}
   * 凭证类型。
   */
  @ApiModelProperty("凭证类型")
  private String voucherType;

  /**
   * 进项发票单号。
   */
  @ApiModelProperty("进项发票单号")
  private String invoiceOrderNo;

  /**
   * 凭证金额。
   */
  @ApiModelProperty("凭证金额")
  private BigDecimal voucherPrice;

  /**
   * {@link com.xhgj.srm.common.enums.VoucherPaymentStateEnum}
   * 凭证付款状态。
   */
  @ApiModelProperty("凭证付款状态")
  private String voucherPaymentState;

  /**
   * 基准日期。
   */
  @ApiModelProperty("基准日期")
  private Long baseDate;

  /**
   * 账期枚举。
   */
  @ApiModelProperty("账期枚举")
  private String accountPeriod;

  /**
   * 预计付款日期。
   */
  @ApiModelProperty("预计付款日期")
  private Long expectedPaymentDate;

  /**
   * {@link com.xhgj.srm.common.enums.VoucherPaymentTypeEnum}
   * 付款方式。
   */
  @ApiModelProperty("付款方式")
  private String paymentType;

  /**
   * 付款冻结状态。
   * {@link com.xhgj.srm.common.enums.PaymentFreezeStatusEnum}
   */
  @ApiModelProperty("付款冻结状态")
  private String paymentFreezeStatus;

  /**
   * 采购订单号。
   */
  @ApiModelProperty("采购订单号")
  private String purchaseOrderNo;

  /**
   * 供应商名称。
   */
  @ApiModelProperty("供应商名称")
  private String supplierName;

  /**
   * 供应商Id。
   */
  @ApiModelProperty("供应商Id")
  private String supplierId;

  /**
   * {@link com.xhgj.srm.common.enums.PaymentTermsEnum}
   * 付款条件。
   */
  @ApiModelProperty("付款条件")
  private String paymentTerms;

  /**
   * 客户回款状态（1==已完成，0==未完成）。
   */
  @ApiModelProperty("客户回款状态（1==已完成，0==未完成）")
  private String customerCollectionState;

  /**
   * 订货金额。
   */
  @ApiModelProperty("订货金额")
  private BigDecimal orderAmount;

  /**
   * 关联金额。
   */
  @ApiModelProperty("关联金额")
  private BigDecimal relatedAmount;

  @ApiModelProperty("发票单id")
  private String invoiceRelationId;

  @ApiModelProperty("采购订单id")
  private String purchaseOrderId;

  @ApiModelProperty("进项票类型")
  private String orderSource;

  @ApiModelProperty("来源标识")
  private String manageFlag;

  @ApiModelProperty("采购订单待确认状态")
  private Boolean confirmState;

  @ApiModelProperty("订单状态")
  private String orderState;

  @ApiModelProperty("抵消预付金额")
  private String offsetPrepaidAmount;

  @ApiModelProperty("已提款金额")
  private String withdrawnAmount;

  @ApiModelProperty("已退款金额")
  private String refundAmount;

  @ApiModelProperty("剩余可提款金额")
  private String remainingWithdrawableAmount;

  @ApiModelProperty("SAP凭证行项目")
  private String voucherLineItems;

  @ApiModelProperty("付款申请金额")
  private String paymentApplyAmount;

  @ApiModelProperty("采购组织")
  private String purchaseGroupName;

  @ApiModelProperty("预付申请冲销状态")
  private Boolean advanceReversalState = false;

  @ApiModelProperty("关联付款申请单号")
  private String  relevancePaymentApplicationNo;

  @ApiModelProperty("采购申请id集合")
  private List<String> applyRecordIds;

  @ApiModelProperty("申请类型集合")
  private List<String> applyTypes;

  /**
   * groupCode
   */
  @ApiModelProperty("组织code")
  private String groupCode;

  /**
   * 银行回执单url
   */
  @ApiModelProperty("银行回执单url")
  private String fileUrl;

  /**
   * 订单类型
   */
  private String orderType;

  /**
   * 期初订单导入excel标识---代表没有关联的采购订单
   */
  private Boolean initialOrderFlag;

  /**
   * version
   * @see com.xhgj.srm.jpa.sharding.enums.VersionEnum
   */
  @ApiModelProperty("版本")
  private String version;

  /**
   *
   */
  public FinancialVoucherPageVO(FinancialVoucher financialVoucher,
      SupplierOrder purchaseOrder,
      Supplier supplier,
      Group group) {
    this(financialVoucher, null, purchaseOrder, supplier, group, false);
  }


  public FinancialVoucherPageVO(FinancialVoucher financialVoucher,
      InputInvoiceOrder orderInvoiceRelation,
      SupplierOrder purchaseOrder,
      Supplier supplier,
      Group group,
      Boolean advanceReversalState) {
    String defaultStr = "-";
    this.initialOrderFlag = financialVoucher.getInitialOrder();
    this.purchaseGroupName = StrUtil.emptyIfNull(group.getName());
    this.supplierId = financialVoucher.getSupplierId();
    if (Boolean.TRUE.equals(initialOrderFlag)) {
      this.orderAmount = financialVoucher.getOrderAmount();
    } else if (purchaseOrder != null) {
      this.orderAmount = BigDecimalUtil.formatForStandard(purchaseOrder.getPrice());
      this.purchaseOrderId = purchaseOrder.getId();
      this.confirmState = purchaseOrder.getOrderConfirmState();
      this.orderState = purchaseOrder.getOrderState();
      this.orderType = purchaseOrder.getOrderType();
      this.version = purchaseOrder.getVersion();
    }
    if (supplier.isOneTimeSupplier() && purchaseOrder != null) {
      this.supplierName = StrUtil.emptyToDefault(purchaseOrder.getSupplierName(), defaultStr);
    } else {
      this.supplierName = StrUtil.emptyToDefault(supplier.getEnterpriseName(), defaultStr);
    }
    this.id = financialVoucher.getId();
    this.groupCode = financialVoucher.getGroupCode();
    this.financialVoucherNo =
        StrUtil.emptyToDefault(financialVoucher.getFinancialVoucherNo(), defaultStr);
    this.accountingYear = StrUtil.emptyToDefault(financialVoucher.getAccountingYear(), defaultStr);
    this.voucherType = StrUtil.emptyToDefault(financialVoucher.getVoucherType(), defaultStr);
    this.invoiceOrderNo = StrUtil.emptyToDefault(financialVoucher.getInvoiceOrderNo(), defaultStr);
    this.voucherPrice = BigDecimalUtil.formatForStandard(financialVoucher.getVoucherPrice());
    this.voucherPaymentState =
        StrUtil.emptyToDefault(financialVoucher.getVoucherPaymentState(), defaultStr);
    this.baseDate = financialVoucher.getBaseDate() == null ? 0 : financialVoucher.getBaseDate();
    this.accountPeriod = financialVoucher.getAccountPeriod();
    this.expectedPaymentDate = financialVoucher.getExpectedPaymentDate() == null ? 0
        : financialVoucher.getExpectedPaymentDate();
    this.paymentFreezeStatus =
        StrUtil.emptyToDefault(financialVoucher.getPaymentFreezeStatus(), defaultStr);
    this.paymentType = StrUtil.emptyToDefault(financialVoucher.getPaymentType(), defaultStr);
    this.purchaseOrderNo =
        StrUtil.emptyToDefault(financialVoucher.getPurchaseOrderNo(), defaultStr);
    this.paymentTerms = StrUtil.emptyToDefault(financialVoucher.getPaymentTerms(), defaultStr);
    this.customerCollectionState =
        StrUtil.emptyToDefault(financialVoucher.getCustomerCollectionState(), defaultStr);
    this.relatedAmount = BigDecimalUtil.formatForStandard(financialVoucher.getRelatedAmount());
    if (orderInvoiceRelation != null) {
      this.invoiceRelationId = orderInvoiceRelation.getId();
      this.orderSource = orderInvoiceRelation.getOrderSource();
      this.manageFlag = orderInvoiceRelation.getManageFlag();
    } else {
      this.invoiceRelationId = defaultStr;
      this.orderSource = defaultStr;
      this.manageFlag = defaultStr;
    }
    this.offsetPrepaidAmount = financialVoucher.getOffsetPrepaidAmount() == null ? "0"
        : financialVoucher.getOffsetPrepaidAmount().stripTrailingZeros().toPlainString();
    this.withdrawnAmount = financialVoucher.getWithdrawnAmount() == null ? "0"
        : financialVoucher.getWithdrawnAmount().stripTrailingZeros().toPlainString();
    this.refundAmount = financialVoucher.getRefundAmount() == null ? "0"
        : financialVoucher.getRefundAmount().stripTrailingZeros().toPlainString();
    BigDecimal settlementTotal =
        NumberUtil.add(financialVoucher.getRelatedAmount(), financialVoucher.getRefundAmount());
    BigDecimal prepaidAndWithdrawalTotal = NumberUtil.add(financialVoucher.getOffsetPrepaidAmount(),
        financialVoucher.getWithdrawnAmount());
    this.remainingWithdrawableAmount = "0";
    if (VoucherTypeEnum.INVOICE_POSTING.getKey().equals(this.voucherType)) {
      this.remainingWithdrawableAmount =
          NumberUtil.sub(settlementTotal, prepaidAndWithdrawalTotal).stripTrailingZeros()
              .toPlainString();
    }
    this.voucherLineItems = StrUtil.emptyToDefault(financialVoucher.getVoucherLineItems(), defaultStr);
    this.paymentApplyAmount = financialVoucher.getPaymentApplyAmount() == null ? "-" :
        financialVoucher.getPaymentApplyAmount().stripTrailingZeros().toPlainString();
    this.advanceReversalState = advanceReversalState;
    this.fileUrl = financialVoucher.getFileUrl();
  }
}
