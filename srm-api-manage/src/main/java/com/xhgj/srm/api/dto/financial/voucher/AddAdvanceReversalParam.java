package com.xhgj.srm.api.dto.financial.voucher;

import com.xhgj.srm.jpa.entity.User;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class AddAdvanceReversalParam {
  @ApiModelProperty("财务凭证id")
  @NotBlank
  private String financialVoucherId;
  @ApiModelProperty("冲销备注")
  private String reversalNotes;
  private User currentUser;
}
