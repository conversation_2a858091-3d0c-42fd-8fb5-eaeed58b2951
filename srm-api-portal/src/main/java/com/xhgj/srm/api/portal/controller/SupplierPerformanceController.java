package com.xhgj.srm.api.portal.controller;

import com.xhgj.srm.api.portal.service.SupplierPerformanceService;
import com.xhiot.boot.mvc.base.ResultBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/supplierPerformance")
@Validated
@Api("供应商平台管理")
public class SupplierPerformanceController {

  @Resource
  private SupplierPerformanceService supplierPerformanceService;

  @ApiOperation(value = "获取供应商平台编码列表数据", notes = "获取供应商平台编码列表数据")
  @PostMapping(value = "/getPerformCodeListBySupplierId")
  public ResultBean<List<String>> getPerformCodeListBySupplierId(
      @NotBlank(message = "参数不合法") String supplierId) {
    return new ResultBean<>(supplierPerformanceService.getPerformCodeListBySupplierId(supplierId));
  }

}
